appId: com.ttsapp.desktop
productName: Creative Hub
artifactName: ${productName}_${version}-${os}-${arch}.${ext}
asar: true
asarUnpack:
  - static/**/*

protocols:
  name: ttsapp
  schemes:
    - ttsapp
forceCodeSigning: false
files:
  - package.json
  - scripts/**/*
  - static/**/*
  - '!scripts/venv/**/*'
  - '!static/Faster-Whisper-XXL/_xxl_data/**/*'
  - dist/**/*
  - public/**/*
  - from: ./jsc/
    to: electron/
# mac:
#   category: public.app-category.developer-tools
#   icon: electron/resources/icons/mac/icon.icns
#   entitlements: electron/resources/mac/entitlements.mac.plist
#   entitlementsInherit: electron/resources/mac/entitlements.mac.plist
#   hardenedRuntime: true
#   gatekeeperAssess: false
#   target: dmg

win:
  target: nsis
  icon: public/icon.png

nsis:
  oneClick: true
  allowToChangeInstallationDirectory: false

# linux:
#   target: tar.gz
#   icon: electron/resources/icons/png/512x512.png

directories:
  output: dist_electron

publish:
  provider: github
  releaseType: release
  vPrefixedTagName: false
