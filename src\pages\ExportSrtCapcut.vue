<template>
  <div class="export-srt-capcut">
    <!-- Back Button -->
    <div class="mb-4">
      <a-button @click="$router.back()" class="flex items-center gap-2">
        <ArrowLeftOutlined />
        Back
      </a-button>
    </div>

    <div class="header mb-6">
      <h1 class="text-2xl font-bold mb-2">CapCut SRT Exporter</h1>
      <p class="text-gray-600"><PERSON><PERSON><PERSON>t thư mục CapCut Drafts và xuất file SRT từ draft_content.json</p>
    </div>

    <!-- Directory Input Section -->
    <div class="directory-section mb-6">
      <div class="flex gap-4 mb-4">
        <a-input
          v-model:value="capcutDraftsPath"
          placeholder="C:\Users\<USER>\AppData\Local\CapCut Drafts"
          class="flex-1"
          @change="saveDirectoryPath"
        />
        <a-button type="primary" @click="browseDirectory" class="flex items-center">
          <FolderOpenOutlined />
          Browse
        </a-button>
        <a-button @click="loadProjects" :loading="loading" class="flex items-center">
          <ReloadOutlined />
          Load Projects
        </a-button>
      </div>
    </div>

    <!-- Two Column Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" v-if="projects.length > 0">
      <!-- Left Column - Projects List -->
      <div class="projects-section">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-semibold">CapCut Projects ({{ filteredProjects.length }})</h3>
          <span class="text-sm text-gray-500" v-if="searchQuery">{{ projects.length }} total</span>
        </div>

        <!-- Search Input -->
        <div class="mb-4">
          <a-input
            v-model:value="searchQuery"
            placeholder="Search projects..."
            class="w-full"
            allowClear
          >
            <template #prefix>
              <SearchOutlined class="text-gray-400" />
            </template>
          </a-input>
        </div>

        <div class="space-y-3 max-h-[600px] overflow-y-auto border border-gray-700  rounded-lg p-4 bg-slate-700">
          <!-- No results found -->
          <div v-if="filteredProjects.length === 0 && searchQuery" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <p class="text-gray-300">No projects found</p>
            <p class="text-sm text-gray-400 mt-1">Try adjusting your search terms</p>
          </div>

          <div
            v-for="project in filteredProjects"
            :key="project.path"
            class="project-card border rounded-lg p-3 hover:shadow-md cursor-pointer bg-slate-800"
            :class="{ 'border-blue-500 bg-blue-700': selectedProject?.path === project.path }"
            @click="selectProject(project)"
          >
            <div class="flex gap-3">
              <!-- Thumbnail -->
              <div class="flex-shrink-0">
                <img
                  :src="project.thumbnailUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI2IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"
                  :alt="project.name"
                  class="w-16 h-16 object-cover rounded border border-gray-700 "
                  @error="handleImageError"
                />
              </div>

              <!-- Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between mb-1">
                  <h4 class="font-medium truncate text-sm">{{ project.name }}</h4>
                  <a-tag color="green" v-if="project.hasDraftContent" size="small">
                    <CheckCircleOutlined />
                  </a-tag>
                </div>
                <p class="text-xs text-gray-500 truncate" :title="project.path">{{ project.path }}</p>
                <div class="flex justify-between items-center mt-2">
                  <p class="text-xs text-gray-400">
                    {{ formatDate(project.lastModified) }}
                  </p>
                  <a-tag size="small" color="blue">JSON</a-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Project Details & Actions -->
      <div class="project-actions">
        <!-- Selection Prompt -->
        <div v-if="!selectedProject" class="text-center py-12 border-2 border-dashed border-gray-700 rounded-lg">
          <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <p class="text-gray-500 text-xl font-medium">Select a project</p>
          <p class="text-sm text-gray-400 mt-2">Click on a project from the left to extract SRT</p>
        </div>

        <!-- Selected Project Details -->
        <div v-if="selectedProject" class="space-y-4">
          <!-- Project Info -->
          <div class="bg-slate-800 border border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 class="text-xl font-semibold mb-2 text-green-600">
              📁 {{ selectedProject.name }}
            </h3>
            <p class="text-sm text-gray-500 mb-4">{{ selectedProject.path }}</p>

            <!-- Action Buttons -->
            <div class="flex gap-3">
              <a-button type="primary" @click="extractSrt" :loading="extracting" size="large" class="flex items-center">
                <ExportOutlined />
                Extract SRT
              </a-button>
              <a-button @click="clearSelection" size="large">
                Clear Selection
              </a-button>
            </div>
          </div>

          <!-- SRT Content Display -->
          <div v-if="srtContent" class="srt-content bg-slate-800 border border-gray-700 rounded-lg p-6 shadow-sm">
            <div class="flex justify-between items-center mb-4 gap-1">
              <h4 class="font-medium text-lg">SRT Content ({{ srtItems.length }} items)</h4>
              <!-- copy txt -->
              <div class="flex-1"></div>
              <a-button type="dashed" @click="copyToText" :disabled="!txtContent" size="large">
                <CopyOutlined />
                Copy Text
              </a-button>
              <!-- copy srt -->
              <a-button type="dashed" @click="copySrtToClipboard" :disabled="!srtContent" size="large">
                <CopyOutlined />
                Copy SRT
              </a-button>
              <a-button type="primary" @click="saveSrtFile" :disabled="!srtContent" size="large" class="flex items-center">
                <DownloadOutlined />
                Save SRT File
              </a-button>
            </div>

            <a-textarea
              v-model:value="srtContent"
              :rows="20"
              class="mb-4"
              placeholder="SRT content will appear here..."
            />
          </div>
        </div>
      </div>
    </div>



    <!-- Error Display -->
    <a-alert
      v-if="error"
      :message="error"
      type="error"
      closable
      @close="error = ''"
      class="mb-4"
    />

    <!-- Loading Indicator -->
    <div v-if="loading" class="text-center py-8">
      <a-spin size="large" />
      <p class="mt-2">Loading projects...</p>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && projects.length === 0 && capcutDraftsPath" class="text-center py-8">
      <div class="mb-4">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <p class="text-gray-500 text-lg">No CapCut projects found</p>
      <p class="text-sm text-gray-400 mt-2">Make sure the path contains CapCut project folders with draft_content.json files.</p>
      <div class="mt-4 text-xs text-gray-400">
        <p>Expected structure:</p>
        <p class="font-mono mt-1">📁 CapCut Drafts/</p>
        <p class="font-mono">├── 📁 Project1/</p>
        <p class="font-mono">│   └── 📄 draft_content.json</p>
        <p class="font-mono">└── 📁 Project2/</p>
        <p class="font-mono">    └── 📄 draft_content.json</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  FolderOpenOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExportOutlined,
  DownloadOutlined,
  SearchOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons-vue'

// Reactive data
const capcutDraftsPath = ref('')
const projects = ref([])
const selectedProject = ref(null)
const srtContent = ref('')
const txtContent = ref('')
const srtItems = ref([])
const loading = ref(false)
const extracting = ref(false)
const error = ref('')
const searchQuery = ref('')

// Computed property for filtered projects
const filteredProjects = computed(() => {
  if (!searchQuery.value.trim()) {
    return projects.value
  }

  const query = searchQuery.value.toLowerCase().trim()
  return projects.value.filter(project =>
    project.name.toLowerCase().includes(query) ||
    project.path.toLowerCase().includes(query)
  )
})

// Load saved directory path on mount
onMounted(async () => {
  const saved = localStorage.getItem('capcutDraftsPath')
  if (saved) {
    capcutDraftsPath.value = saved
  }
})

// Save directory path to localStorage
const saveDirectoryPath = () => {
  localStorage.setItem('capcutDraftsPath', capcutDraftsPath.value)
}

// Browse for directory
const browseDirectory = async () => {
  try {
    const result = await window.electronAPI.invoke('open-file-dialog', {
      properties: ['openDirectory'],
      title: 'Select CapCut Drafts Directory'
    })

    if (!result.canceled && result.filePaths.length > 0) {
      capcutDraftsPath.value = result.filePaths[0]
      saveDirectoryPath()
      await loadProjects()
    }
  } catch (err) {
    error.value = 'Error browsing directory: ' + err.message
  }
}

// Load CapCut projects from directory
const loadProjects = async () => {
  if (!capcutDraftsPath.value) {
    error.value = 'Please specify CapCut Drafts directory path'
    return
  }

  loading.value = true
  error.value = ''
  projects.value = []

  try {
    // Check if directory exists
    const dirExists = await window.electronAPI.invoke('fs:existsSync', capcutDraftsPath.value)
    if (!dirExists) {
      throw new Error('Directory does not exist: ' + capcutDraftsPath.value)
    }

    // Read directory contents
    const result = await window.electronAPI.invoke('read-directory-with-subdirs', capcutDraftsPath.value)
    if (!result.success) {
      throw new Error(result.error)
    }

    // Filter directories and check for draft_content.json
    const projectFolders = result.items.filter(item => item.isDirectory)

    for (const folder of projectFolders) {
      try {
        const draftContentPath = await window.electronAPI.invoke('path:join', folder.path, 'draft_content.json')
        const hasDraftContent = await window.electronAPI.invoke('fs:existsSync', draftContentPath)

        if (hasDraftContent) {
          // Try to get file stats for last modified date
          let lastModified = new Date()
          try {
            const fileContent = await window.electronAPI.invoke('fs:readFileSync', draftContentPath, 'utf-8')
            // If we can read the file, it exists and we can get basic info
            lastModified = new Date()
          } catch (err) {
            console.warn('Could not read file stats for:', draftContentPath)
          }

          // Check for thumbnail
          const thumbnailPath = await window.electronAPI.invoke('path:join', folder.path, 'draft_cover.jpg')
          const hasThumbnail = await window.electronAPI.invoke('fs:existsSync', thumbnailPath)

          projects.value.push({
            name: folder.name,
            path: folder.path,
            draftContentPath,
            thumbnailPath: hasThumbnail ? thumbnailPath : null,
            thumbnailUrl: hasThumbnail ? `file://${thumbnailPath}` : null,
            hasDraftContent: true,
            lastModified
          })
        }
      } catch (err) {
        console.warn('Error processing folder:', folder.path, err)
      }
    }

    message.success(`Found ${projects.value.length} CapCut projects`)
  } catch (err) {
    error.value = 'Error loading projects: ' + err.message
    console.error('Error loading projects:', err)
  } finally {
    loading.value = false
  }
}

// Select a project
const selectProject = (project) => {
  console.log('Selecting project:', project)
  selectedProject.value = project
  srtContent.value = ''
  txtContent.value = ''
  srtItems.value = []
  message.info(`Selected project: ${project.name}`)
}

// Clear selection
const clearSelection = () => {
  selectedProject.value = null
  srtContent.value = ''
  txtContent.value = ''
  srtItems.value = []
}

// Extract SRT from CapCut draft_content.json
const extractSrt = async () => {
  if (!selectedProject.value) {
    error.value = 'No project selected'
    return
  }

  extracting.value = true
  error.value = ''

  try {
    // Read draft_content.json
    const result = await window.electronAPI.invoke('read-json-file', selectedProject.value.draftContentPath)
    if (!result.success) {
      throw new Error(result.error)
    }

    const draftData = result.data
    console.log('Draft data:', draftData)

    // Extract subtitle tracks from CapCut data
    // 
    // const subtitleTracks = extractSubtitleTracks(draftData)
    const subtitleTracks = generateData(draftData)
    // console.log('Subtitle tracks:', subtitleTracks)
    if (subtitleTracks.length === 0) {
      throw new Error('No subtitle tracks found in this project')
    }
    if(subtitleTracks.success === false){
      throw new Error(subtitleTracks.error)
    }
    // Convert to SRT format
    // const srtData = convertToSrt(subtitleTracks)
    const srtData = subtitleTracks
    srtItems.value = srtData.items
    srtContent.value = srtData.srtOut
    //copyOut
    txtContent.value = srtData.copyOut

    message.success(`Extracted ${srtItems.value.length} subtitle items`)
  } catch (err) {
    error.value = 'Error extracting SRT: ' + err.message
    console.error('Error extracting SRT:', err)
  } finally {
    extracting.value = false
  }
}
function msToSrt(timeInMs) {
    const convertMs = Math.floor(timeInMs / 1000)

    const ms = convertMs % 1000
    const totalSeconds = (convertMs - ms) / (1000)
    const seconds = (totalSeconds) % (60)
    const totalMinutes = (totalSeconds - seconds) / 60
    const minutes = totalMinutes % 60
    const hour = (totalMinutes - minutes) / 60
    return `${hour < 10 ? '0' + hour : hour}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds},${ms}`
}


const findBetween = (text, separator1, separator2) => {
    const posisiSeparator1 = text.indexOf(separator1);
    if (posisiSeparator1 === -1) {
        return "";
    }

    const potongan1 = text.substring(posisiSeparator1 + separator1.length);

    const posisiSeparator2 = potongan1.indexOf(separator2);

    if (posisiSeparator2 === -1) {
        return "";
    }

    const hasil = potongan1.substring(0, posisiSeparator2);

    return hasil;
}


const convertData = (subtitlesInfo, filename, items) => {
    const srtOut = subtitlesInfo.reduce((srt, i) => {
        let check = findBetween(i.content, '"text":"', '"}')
        i.content = check.length > 0 ? check : i.content
        const subtitle = `${i.subNumber}\n${i.srtTiming}\n${i.content}\n\n`
        return srt + subtitle
    }, '')
    const copyOut = subtitlesInfo.reduce((srt, i) => {
        const subtitle = `${i.content}\n`
        return srt + subtitle
    }, '')

    return {
        success: true,
        srtOut: srtOut,
        copyOut: copyOut,
        filename: filename,
        items: items
    }
}


const generateData = (draftData) => {
    try {
        const data = draftData

        const { materials, tracks } = data;

        let subTrackNumber = 1
        let subTiming = tracks[subTrackNumber].segments
        const items = []
        var subtitlesInfo = materials.texts.map(i => {
            let content = i.content.replace(/\<.*?\>/g, '').replace(/\<\/.*?\>/g, '').replace(/\[|\]/g, '')
            const textMatch = content.match(/"text":\s*"([^"]*)"/);
            content = textMatch ? textMatch[1] : content
            items.push(content)
            return {
                content,
                id: i.id
            }
        })

        subtitlesInfo = subtitlesInfo.map((s, i) => {
            let segment = subTiming.find(i => i.material_id === s.id)
            while (!segment) {
                subTrackNumber++
                subTiming = tracks[subTrackNumber].segments
                segment = subTiming.find(i => i.material_id === s.id)
            }
            s.start = segment.target_timerange.start
            s.end = s.start + segment.target_timerange.duration
            s.srtStart = msToSrt(s.start)
            s.srtEnd = msToSrt(s.end)
            s.subNumber = i + 1
            s.srtTiming = s.srtStart + ' --> ' + s.srtEnd

            return s
        })

        return convertData(subtitlesInfo, materials.videos[0].material_name, items)
    } catch (e) {
        return {
            success: false
        }
    }
}


const copyToText = async () => {
  if (!txtContent.value) {
    error.value = 'No text content to copy'
    return
  }

  try {
    await navigator.clipboard.writeText(txtContent.value)
    message.success('Text copied to clipboard!')
  } catch (err) {
    error.value = 'Error copying text: ' + err.message
    console.error('Error copying text:', err)
  }
}

const copySrtToClipboard = async () => {
  if (!srtContent.value) {
    error.value = 'No SRT content to copy'
    return
  }

  try {
    await navigator.clipboard.writeText(srtContent.value)
    message.success('SRT copied to clipboard!')
  } catch (err) {
    error.value = 'Error copying SRT: ' + err.message
    console.error('Error copying SRT:', err)
  }
}

// Save SRT file
const saveSrtFile = async () => {
  if (!srtContent.value || !selectedProject.value) {
    error.value = 'No SRT content to save'
    return
  }

  try {
    const result = await window.electronAPI.invoke('save-file-dialog', {
      title: 'Save SRT File',
      defaultPath: `${selectedProject.value.name}.srt`,
      filters: [
        { name: 'SRT Files', extensions: ['srt'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      await window.electronAPI.invoke('write-file', result.filePath, srtContent.value)
      message.success('SRT file saved successfully!')
    }
  } catch (err) {
    error.value = 'Error saving SRT file: ' + err.message
    console.error('Error saving SRT file:', err)
  }
}

// Format date for display
const formatDate = (date) => {
  if (!date) return 'Unknown'
  return new Date(date).toLocaleDateString()
}

// Handle image loading error
const handleImageError = (event) => {
  // Set fallback image (placeholder SVG)
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI2IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'
}
</script>

<style scoped>
.export-srt-capcut {
  width: 100%;
  margin: 0 auto;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem;
  box-sizing: border-box;
}

.project-card {
  transition: all 0.2s ease;
  min-height: 80px;
  position: relative;
}

.project-card img {
  transition: opacity 0.2s ease;
}

.project-card img:hover {
  opacity: 0.8;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.project-card.border-blue-500 {
  box-shadow: 0 0 0 2px #3b82f6;
  background-color: #002b63 !important;
  border-color: #3b82f6 !important;
}

.project-card.border-blue-500::before {
  content: "✓";
  position: absolute;
  top: 8px;
  right: 8px;
  background: #3b82f6;
  color: white;
  font-size: 12px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 10;
}

.srt-content {
  /* border: 1px solid #d9d9d9; */
  border-radius: 6px;
  padding: 16px;
  /* background: #fafafa; */
}

.directory-section .ant-input {
  font-family: 'Courier New', monospace;
}

/* Two column layout specific styles */
.projects-section {
  min-height: 600px;
}

.project-actions {
  min-height: 600px;
}

.projects-section .space-y-3 > * + * {
  margin-top: 0.75rem;
}

@media (max-width: 1024px) {
  .grid.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .projects-section .max-h-96 {
    max-height: 500px;
  }
}

@media (max-width: 768px) {
  .export-srt-capcut {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .flex.gap-4, .flex.gap-3 {
    flex-direction: column;
    gap: 0.5rem;
  }

  .projects-section .max-h-96 {
    max-height: 250px;
  }
}

/* Ensure scrollbar is always visible when needed */
.export-srt-capcut::-webkit-scrollbar {
  width: 8px;
}

.export-srt-capcut::-webkit-scrollbar-track {
  /* background: #f1f1f1; */
  border-radius: 4px;
}

.export-srt-capcut::-webkit-scrollbar-thumb {
  /* background: #c1c1c1; */
  border-radius: 4px;
}

.export-srt-capcut::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>