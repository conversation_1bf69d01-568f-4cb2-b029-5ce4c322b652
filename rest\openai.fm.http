###
POST https://tts-toktok.vercel.app/tts-openai
Content-Type: application/json
accept: application/json

{
    "input": "<PERSON><PERSON><PERSON> không phải mớ lý thuyết copy dăm ba chỗ về dán lại. <PERSON><PERSON><PERSON> là quy trình thực chiến từng ngày, t<PERSON><PERSON> bư<PERSON>, đ<PERSON><PERSON><PERSON> bó<PERSON> tách từ kinh nghiệm dựng kênh view ngoại, kiếm tiền CPM cao, áp dụng được ngay kể cả cho người mới.",
    "prompt": "",
    "voice": "onyx",
    "vibe": null
}

### https://tts-toktok.vercel.app/docs

curl -X 'POST' \
  'https://tts-toktok.vercel.app/tts' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "text": "<PERSON><PERSON>y không phải mớ lý thuyết copy dăm ba chỗ về dán lại. <PERSON><PERSON><PERSON> là quy trình thực chiến từng ngà<PERSON>, t<PERSON><PERSON> b<PERSON>, <PERSON><PERSON><PERSON><PERSON> bóc tách từ kinh nghiệm dựng kênh view ngoại, kiếm tiền CPM cao, áp dụng được ngay kể cả cho người mới.",
  "output_format": "base64",
  "text_speaker": "BV075_streaming"
}'

###
curl -X 'POST' \
  'https://tts-toktok.vercel.app/tts-openai' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "input": "Đây không phải mớ lý thuyết copy dăm ba chỗ về dán lại. Đây là quy trình thực chiến từng ngày, từng bước, được bóc tách từ kinh nghiệm dựng kênh view ngoại, kiếm tiền CPM cao, áp dụng được ngay kể cả cho người mới.",
  "prompt": "",
  "voice": "sage",
  "vibe": "null"
}'