@x-key=123

curl --request POST \
  --url https://api.bfl.ai/v1/flux-dev \
  --header 'Content-Type: application/json' \
  --header 'x-key: {{x-key}}' \
  --data '{
  "prompt": "Owl, Partridge",
  "seed": 64,
  "aspect_ratio": "1:1",
  "output_format": "jpeg",
  "prompt_upsampling": false,
  "safety_tolerance": 2
}'


###
curl --request GET \
  --url https://api.us1.bfl.ai/v1/get_result?id=b0fcd0dc-f722-4081-a4cc-50bc796b722c

###
flux-dev 1 req = 3.5 credit
flux-kontext-pro 1 req = 4 credit

###
curl 'https://fal-kontext-demo.vercel.app/api/trpc/generateImageStream?input={"json":{"imageUrl":"https://v3.fal.media/files/kangaroo/pBD4cta53TMbXk-NwJImw_1751780865221.jpeg","prompt":"Convert to wojak style drawing","loraUrl":"https://huggingface.co/fal/Wojak-Kontext-Dev-LoRA/resolve/main/wojak-kontext-dev-lora.safetensors"}}' \
  -H 'accept: text/event-stream' \
  -H 'accept-language: vi-VN,vi;q=0.9,yue-HK;q=0.8,yue;q=0.7,zh-TW;q=0.6,zh;q=0.5,fr-FR;q=0.4,fr;q=0.3,en-US;q=0.2,en;q=0.1,kri;q=0.1' \
  -H 'cache-control: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fal-kontext-demo.vercel.app/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'sec-gpc: 1' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'