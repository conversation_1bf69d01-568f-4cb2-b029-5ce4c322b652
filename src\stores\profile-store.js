import { defineStore } from 'pinia'
import { useSubtitleStore } from './subtitle-store'

export const useProfileStore = defineStore('profile', {
  state: () => ({
    profiles: [],
    currentProfile: null,
    isLoading: false,
    error: null
  }),

  getters: {
    getProfileById: (state) => (id) => {
      return state.profiles.find(profile => profile.id === id)
    },
    
    getProfilesByCategory: (state) => (category) => {
      return state.profiles.filter(profile => profile.category === category)
    },
    
    getAllCategories: (state) => {
      const categories = [...new Set(state.profiles.map(p => p.category))]
      return categories.sort()
    }
  },

  actions: {
    // Tạo profile mới từ settings hiện tại
    createProfile(name, description = '', category = 'General') {
      const subtitleStore = useSubtitleStore()
      const currentSettings = this.getCurrentSettings()
      
      const newProfile = {
        id: this.generateId(),
        name,
        description,
        category,
        settings: currentSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0'
      }
      
      this.profiles.push(newProfile)
      this.saveToStorage()
      return newProfile
    },

    // Lấy settings hiện tại từ subtitle store
    getCurrentSettings() {
      const subtitleStore = useSubtitleStore()
      
      return {
        // Text Animation settings
        textAnimation: {
          showText: subtitleStore.renderOptions.showText,
          fontSize: subtitleStore.renderOptions.fontSize,
          textColor: subtitleStore.renderOptions.textColor,
          textValue: subtitleStore.renderOptions.textValue,
          textOpacity: subtitleStore.renderOptions.textOpacity,
          textDirection: subtitleStore.renderOptions.textDirection,
          textFontFamily: subtitleStore.renderOptions.textFontFamily,
          textSpeed: subtitleStore.renderOptions.textSpeed
        },

        // Multiple Media Overlays settings (New Format)
        overlays: {
          logos: JSON.parse(JSON.stringify(subtitleStore.renderOptions.overlays?.logos || [])),
          images: JSON.parse(JSON.stringify(subtitleStore.renderOptions.overlays?.images || [])),
          texts: JSON.parse(JSON.stringify(subtitleStore.renderOptions.overlays?.texts || []))
        },

        // Legacy Media Overlay settings (Backward Compatibility)
        mediaOverlay: {
          // Logo
          showLogo: subtitleStore.renderOptions.showLogo,
          logoPosition: subtitleStore.renderOptions.logoPosition,
          logoSize: subtitleStore.renderOptions.logoSize,
          logoFile: subtitleStore.renderOptions.logoFile,
          logoOptions: { ...subtitleStore.renderOptions.logoOptions },

          // Image
          showImage: subtitleStore.renderOptions.showImage,
          imageFile: subtitleStore.renderOptions.imageFile,
          imageOptions: { ...subtitleStore.renderOptions.imageOptions },

          // Fixed Text
          showFixedText: subtitleStore.renderOptions.showFixedText,
          fixedTextOptions: { ...subtitleStore.renderOptions.fixedTextOptions }
        },

        // Subtitle settings
        subtitle: {
          showSubtitle: subtitleStore.renderOptions.showSubtitle,
          subtitleFontSize: subtitleStore.renderOptions.subtitleFontSize,
          subtitleTextColor: subtitleStore.renderOptions.subtitleTextColor,
          subtitleBackgroundColor: subtitleStore.renderOptions.subtitleBackgroundColor,
          subtitleBorderColor: subtitleStore.renderOptions.subtitleBorderColor,
          subtitleBold: subtitleStore.renderOptions.subtitleBold,
          shadowSize: subtitleStore.renderOptions.shadowSize,
          fontFamily: subtitleStore.renderOptions.fontFamily,
          subInLine: subtitleStore.renderOptions.subInLine,
          assOptions: { ...subtitleStore.renderOptions.assOptions }
        },

        // Voice settings
        voiceSettings: JSON.parse(JSON.stringify(subtitleStore.renderVoiceOptions)),

        // Audio settings
        audio: {
          addBackgroundMusic: subtitleStore.renderOptions.addBackgroundMusic,
          backgroundMusicVolume: subtitleStore.renderOptions.backgroundMusicVolume,
          originalAudioVolume: subtitleStore.renderOptions.originalAudioVolume,
          holdOriginalAudio: subtitleStore.renderOptions.holdOriginalAudio,
          holdMusicOnly: subtitleStore.renderOptions.holdMusicOnly,
          musicFile: subtitleStore.renderOptions.musicFile
        },

        // Output settings
        output: {
          videoQuality: subtitleStore.renderOptions.videoQuality,
          frameRate: subtitleStore.renderOptions.frameRate,
          scaleFactor: subtitleStore.renderOptions.scaleFactor,
          flipVideo: subtitleStore.renderOptions.flipVideo,
          volume: subtitleStore.renderOptions.volume
        },

        // Blur Areas settings
        blurAreas: JSON.parse(JSON.stringify(subtitleStore.renderOptions.blurAreas || []))
      }
    },

    // Apply profile settings
    applyProfile(profileId) {
      const profile = this.getProfileById(profileId)
      if (!profile) {
        this.error = 'Profile not found'
        return false
      }

      try {
        const subtitleStore = useSubtitleStore()
        const settings = profile.settings

        // Apply Text Animation settings
        if (settings.textAnimation) {
          Object.assign(subtitleStore.renderOptions, {
            showText: settings.textAnimation.showText,
            fontSize: settings.textAnimation.fontSize,
            textColor: settings.textAnimation.textColor,
            textValue: settings.textAnimation.textValue,
            textOpacity: settings.textAnimation.textOpacity,
            textDirection: settings.textAnimation.textDirection,
            textFontFamily: settings.textAnimation.textFontFamily,
            textSpeed: settings.textAnimation.textSpeed
          })
        }

        // Apply Multiple Overlays settings (New Format)
        if (settings.overlays) {
          // Initialize overlays structure if not exists
          subtitleStore.initializeOverlays()

          // Apply multiple overlays
          if (settings.overlays.logos) {
            subtitleStore.renderOptions.overlays.logos = JSON.parse(JSON.stringify(settings.overlays.logos))
          }
          if (settings.overlays.images) {
            subtitleStore.renderOptions.overlays.images = JSON.parse(JSON.stringify(settings.overlays.images))
          }
          if (settings.overlays.texts) {
            subtitleStore.renderOptions.overlays.texts = JSON.parse(JSON.stringify(settings.overlays.texts))
          }
        }

        // Apply Legacy Media Overlay settings (Backward Compatibility)
        if (settings.mediaOverlay) {
          Object.assign(subtitleStore.renderOptions, {
            showLogo: settings.mediaOverlay.showLogo,
            logoPosition: settings.mediaOverlay.logoPosition,
            logoSize: settings.mediaOverlay.logoSize,
            logoFile: settings.mediaOverlay.logoFile,
            logoOptions: { ...settings.mediaOverlay.logoOptions },
            showImage: settings.mediaOverlay.showImage,
            imageFile: settings.mediaOverlay.imageFile,
            imageOptions: { ...settings.mediaOverlay.imageOptions },
            showFixedText: settings.mediaOverlay.showFixedText,
            fixedTextOptions: { ...settings.mediaOverlay.fixedTextOptions }
          })
        }

        // Apply Subtitle settings
        if (settings.subtitle) {
          Object.assign(subtitleStore.renderOptions, {
            showSubtitle: settings.subtitle.showSubtitle,
            subtitleFontSize: settings.subtitle.subtitleFontSize,
            subtitleTextColor: settings.subtitle.subtitleTextColor,
            subtitleBackgroundColor: settings.subtitle.subtitleBackgroundColor,
            subtitleBorderColor: settings.subtitle.subtitleBorderColor,
            subtitleBold: settings.subtitle.subtitleBold,
            shadowSize: settings.subtitle.shadowSize,
            fontFamily: settings.subtitle.fontFamily,
            subInLine: settings.subtitle.subInLine,
            assOptions: { ...settings.subtitle.assOptions }
          })
        }

        // Apply Voice settings
        if (settings.voiceSettings) {
          Object.assign(subtitleStore.renderVoiceOptions, settings.voiceSettings)
        }

        // Apply Audio settings
        if (settings.audio) {
          Object.assign(subtitleStore.renderOptions, {
            addBackgroundMusic: settings.audio.addBackgroundMusic,
            backgroundMusicVolume: settings.audio.backgroundMusicVolume,
            originalAudioVolume: settings.audio.originalAudioVolume,
            holdOriginalAudio: settings.audio.holdOriginalAudio,
            holdMusicOnly: settings.audio.holdMusicOnly,
            musicFile: settings.audio.musicFile
          })
        }

        // Apply Output settings
        if (settings.output) {
          Object.assign(subtitleStore.renderOptions, {
            videoQuality: settings.output.videoQuality,
            frameRate: settings.output.frameRate,
            scaleFactor: settings.output.scaleFactor,
            flipVideo: settings.output.flipVideo,
            volume: settings.output.volume
          })
        }

        // Apply Blur Areas settings
        if (settings.blurAreas) {
          subtitleStore.renderOptions.blurAreas = JSON.parse(JSON.stringify(settings.blurAreas))
        }

        this.currentProfile = profile
        this.error = null
        return true
      } catch (error) {
        this.error = `Failed to apply profile: ${error.message}`
        return false
      }
    },

    // Update existing profile
    updateProfile(profileId, updates) {
      const profileIndex = this.profiles.findIndex(p => p.id === profileId)
      if (profileIndex === -1) {
        this.error = 'Profile not found'
        return false
      }

      this.profiles[profileIndex] = {
        ...this.profiles[profileIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      
      this.saveToStorage()
      return true
    },

    // Delete profile
    deleteProfile(profileId) {
      const profileIndex = this.profiles.findIndex(p => p.id === profileId)
      if (profileIndex === -1) {
        this.error = 'Profile not found'
        return false
      }

      this.profiles.splice(profileIndex, 1)
      
      if (this.currentProfile?.id === profileId) {
        this.currentProfile = null
      }
      
      this.saveToStorage()
      return true
    },

    // Duplicate profile
    duplicateProfile(profileId, newName) {
      const originalProfile = this.getProfileById(profileId)
      if (!originalProfile) {
        this.error = 'Profile not found'
        return null
      }

      const duplicatedProfile = {
        ...originalProfile,
        id: this.generateId(),
        name: newName || `${originalProfile.name} (Copy)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      this.profiles.push(duplicatedProfile)
      this.saveToStorage()
      return duplicatedProfile
    },

    // Export profile
    exportProfile(profileId) {
      const profile = this.getProfileById(profileId)
      if (!profile) {
        this.error = 'Profile not found'
        return null
      }

      return {
        ...profile,
        exportedAt: new Date().toISOString(),
        appVersion: '1.0'
      }
    },

    // Import profile
    importProfile(profileData) {
      try {
        const importedProfile = {
          ...profileData,
          id: this.generateId(),
          importedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        this.profiles.push(importedProfile)
        this.saveToStorage()
        return importedProfile
      } catch (error) {
        this.error = `Failed to import profile: ${error.message}`
        return null
      }
    },

    // Utility functions
    generateId() {
      return 'profile_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },

    saveToStorage() {
      try {
        localStorage.setItem('tts-app-profiles', JSON.stringify(this.profiles))
      } catch (error) {
        this.error = `Failed to save profiles: ${error.message}`
      }
    },

    loadFromStorage() {
      try {
        const stored = localStorage.getItem('tts-app-profiles')
        if (stored) {
          this.profiles = JSON.parse(stored)
        }
      } catch (error) {
        this.error = `Failed to load profiles: ${error.message}`
        this.profiles = []
      }
    },

    // Initialize store
    init() {
      this.loadFromStorage()
    }
  },

  persist: {
    storage: localStorage,
    pick: ['profiles', 'currentProfile']
  }
})
