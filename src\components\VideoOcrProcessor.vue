<template>
  <div class="flex-1 flex flex-col ">
    <h2 class="text-xl font-semibold">Video OCR Processor</h2>

    <!-- Processing mode selection -->
    <div class="mb-4 p-3 bg-gray-50 rounded-md">
      <label class="text-sm font-medium mb-2 block">Processing Mode</label>
      <a-radio-group v-model:value="processingMode" @change="handleModeChange">
        <a-radio value="single">Single File</a-radio>
        <a-radio value="multiple">Multiple Files</a-radio>
        <a-radio value="folder">Folder</a-radio>
      </a-radio-group>
    </div>

    <!-- Single file upload area -->
    <div v-if="processingMode === 'single' && !videoFile">
      <DragDropUpload
        accept="video/*"
        :max-size="10 * 1024 * 1024 * 1024"
        :show-preview="false"
        drag-text="Drag and drop video file here"
        drop-text="Drop video file here"
        click-text="or click to select video"
        @files-selected="handleVideoSelected"
      />
    </div>

    <!-- Multiple files upload area -->
    <div v-if="processingMode === 'multiple' && selectedFiles.length === 0">
      <DragDropUpload
        accept="video/*"
        :multiple="true"
        :max-size="10 * 1024 * 1024 * 1024"
        :show-preview="true"
        drag-text="Drag and drop video files here"
        drop-text="Drop video files here"
        click-text="or click to select videos"
        @files-selected="handleMultipleFilesSelected"
      />
    </div>

    <!-- Folder selection -->
    <div v-if="processingMode === 'folder' && selectedFiles.length === 0">
      <div class="border-2 border-dashed border-gray-300 rounded-md p-8 text-center">
        <div class="flex flex-col items-center justify-center">
          <div class="h-8 w-8 mb-2 text-gray-400">📁</div>
          <p class="text-sm text-gray-600 font-medium mb-2">Select a folder containing video files</p>
          <a-button @click="selectFolder" type="primary">Choose Folder</a-button>
        </div>
      </div>
    </div>

    <!-- Selected files list for multiple/folder mode -->
    <div v-if="(processingMode === 'multiple' || processingMode === 'folder') && selectedFiles.length > 0" class="mb-4">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-lg font-medium">Selected Files ({{ selectedFiles.length }})</h3>
        <div class="space-x-2">
          <a-button @click="clearAllFiles" size="small">Clear All</a-button>
          <a-button v-if="processingMode === 'multiple'" @click="addMoreFiles" size="small">Add More</a-button>
          <a-button @click="selectPreviewFile" size="small" type="primary">Preview & Crop</a-button>
        </div>
      </div>
      <div class="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
        <div v-for="(file, index) in selectedFiles" :key="index"
             class="flex items-center justify-between p-3 border-b border-gray-100 last:border-b-0"
             :class="{ 'bg-blue-50': previewFileIndex === index }">
          <div class="flex items-center space-x-3 flex-1">
            <div class="h-4 w-4 text-gray-500">🎬</div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ file.name }}
                <span v-if="previewFileIndex === index" class="text-blue-600 text-xs ml-1">(Preview)</span>
              </p>
              <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
            </div>
            <div v-if="batchResults[index]" class="text-xs">
              <span v-if="batchResults[index].status === 'processing'" class="text-blue-600">Processing...</span>
              <span v-else-if="batchResults[index].status === 'completed'" class="text-green-600">✓ Completed</span>
              <span v-else-if="batchResults[index].status === 'error'" class="text-red-600">✗ Error</span>
              <span v-else class="text-gray-500">Pending</span>
            </div>
          </div>
          <div class="flex items-center space-x-1">
            <button @click="setPreviewFile(index)"
                    class="text-blue-500 hover:text-blue-700 p-1"
                    :class="{ 'text-blue-700': previewFileIndex === index }"
                    title="Use for preview">
              👁️
            </button>
            <button @click="removeFile(index)" class="text-red-500 hover:text-red-700 p-1">
              <div class="h-3 w-3">✕</div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Video preview for batch mode (when preview file is selected) -->
    <div v-if="(processingMode === 'multiple' || processingMode === 'folder') && previewFile && previewVideoUrl"
         class="space-y-4 w-full mx-auto mb-4">
      <!-- Preview file info -->
      <div class="flex justify-between items-center">
        <div class="text-sm flex items-center px-2 py-1 bg-blue-50 rounded-md flex-1 mr-2">
          <div class="font-medium truncate">
            <span class="text-blue-600 mr-1">Preview:</span> {{ previewFile.name }}
            <span class="text-xs text-gray-500 ml-2">(Crop will be applied to all files)</span>
          </div>
        </div>
        <a-button @click="clearPreviewFile" size="small">Close Preview</a-button>
      </div>

      <!-- Video preview with crop selection -->
      <div class="relative rounded-md overflow-hidden bg-black flex items-center justify-center min-h-[300px]">
        <div ref="previewVideoContainer" class="relative">
          <!-- Video element for preview -->
          <video
            ref="previewVideoRef"
            :src="previewVideoUrl"
            class="max-w-full max-h-[400px] object-contain"
            @loadedmetadata="handlePreviewVideoLoaded"
            @timeupdate="handlePreviewTimeUpdate"
            @click="togglePreviewPlayPause"
            @error="handlePreviewVideoError"
          ></video>

          <!-- CropSelector positioned exactly over the preview video -->
          <div
            v-if="previewDisplayDimensions.width > 0"
            class="absolute z-10"
            :style="{
              width: `${previewDisplayDimensions.width}px`,
              height: `${previewDisplayDimensions.height}px`,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              pointerEvents: showSelectionOverlay ? 'auto' : 'none'
            }"
          >
            <CropSelector
              ref="previewCropSelector"
              :active="showSelectionOverlay"
              :content-width="previewVideoDimensions.width"
              :content-height="previewVideoDimensions.height"
              :show-coordinates="true"
              @crop-selected="onPreviewCropSelected"
              @crop-change="onPreviewCropChange"
              @crop-cleared="onPreviewCropCleared"
              class="w-full h-full"
              :style="{
                backgroundColor: showSelectionOverlay ? 'rgba(0,0,0,0.1)' : 'transparent'
              }"
            >
              <div class="w-full h-full"></div>
            </CropSelector>
          </div>
        </div>
      </div>

      <!-- Preview video controls -->
      <div class="p-2 bg-gray-900 rounded-md">
        <div class="flex items-center space-x-2">
          <!-- Play/Pause button -->
          <button
            @click="togglePreviewPlayPause"
            class="px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded"
          >
            {{ isPreviewPlaying ? '⏸️ Pause' : '▶️ Play' }}
          </button>

          <!-- Progress bar -->
          <div class="flex-grow h-3 bg-gray-700 rounded-full overflow-hidden cursor-pointer relative">
            <div class="absolute inset-0 bg-gray-600 rounded-full"></div>
            <div
              class="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-75 ease-out"
              :style="{ width: `${previewVideoProgress}%` }"
            ></div>
          </div>

          <!-- Time display -->
          <div class="text-xs text-gray-600">
            {{ formatTime(previewCurrentTime) }} / {{ formatTime(previewDuration) }}
          </div>
        </div>
      </div>

      <!-- Selection controls for preview -->
      <div class="flex justify-between items-center">
        <div>
          <a-button
            size="small"
            @click="toggleSelectionOverlay"
            :type="showSelectionOverlay ? 'primary' : 'default'"
          >
            {{ showSelectionOverlay ? 'Cancel Selection' : 'Select Region' }}
          </a-button>
          <a-button
            v-if="batchCrop && batchCrop.width > 0"
            size="small"
            @click="clearBatchSelection"
            class="ml-2"
          >
            Clear Selection
          </a-button>
        </div>
        <div v-if="batchCrop && batchCrop.width > 0" class="text-xs text-gray-500">
          Selected region: {{ formatBatchCropValue() }}
        </div>
      </div>
    </div>

    <!-- Video preview and controls (only for single file mode) -->
    <div v-if="processingMode === 'single' && videoFile" class="space-y-4 w-full mx-auto">
      <!-- Video info -->
      <div class="flex justify-between items-center">
        <div class="text-sm flex items-center px-2 py-1 bg-gray-50 rounded-md flex-1 mr-2">
          <div class="font-medium truncate">
            <span class="text-gray-500 mr-1">Video:</span> {{ videoFile.name }}
          </div>
        </div>
        <a-button @click="resetVideo" size="small">Reset</a-button>
      </div>

      <!-- Video preview with crop selection -->
      <div class="relative rounded-md overflow-hidden bg-black flex items-center justify-center min-h-[300px]">
        <div ref="videoContainer" class="relative">
          <!-- Video element -->
          <video
            ref="videoRef"
            :src="videoUrl"
            class="max-w-full max-h-[400px] object-contain"
            @loadedmetadata="handleVideoLoaded"
            @timeupdate="handleTimeUpdate"
            @click="togglePlayPause"
            @error="handleVideoError"
            @loadstart="handleVideoLoadStart"
            @canplay="handleVideoCanPlay"
          ></video>

          <!-- CropSelector positioned exactly over the video -->
          <div
            v-if="displayDimensions.width > 0"
            class="absolute z-10"
            :style="{
              width: `${displayDimensions.width}px`,
              height: `${displayDimensions.height}px`,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              pointerEvents: showSelectionOverlay ? 'auto' : 'none'
            }"
          >
            <CropSelector
              ref="cropSelector"
              :active="showSelectionOverlay"
              :content-width="videoDimensions.width"
              :content-height="videoDimensions.height"
              :show-coordinates="true"
              @crop-selected="onCropSelected"
              @crop-change="onCropChange"
              @crop-cleared="onCropCleared"
              class="w-full h-full"
              :style="{
                backgroundColor: showSelectionOverlay ? 'rgba(0,0,0,0.1)' : 'transparent'
              }"
            >
              <div class="w-full h-full"></div>
            </CropSelector>
          </div>
        </div>
      </div>
      <div class="flex-1 flex flex-col">
        <!-- Custom video controls -->
        <div class="p-2 bg-gray-900 rounded-md">
          <div class="flex items-center space-x-2">
            <!-- Play/Pause button -->
            <button
              @click="togglePlayPause"
              class="px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded"
            >
              {{ isPlaying ? '⏸️ Pause' : '▶️ Play' }}
            </button>

            <!-- Progress bar -->
            <div
              ref="progressBarRef"
              class="flex-grow h-3 bg-gray-700 rounded-full overflow-hidden cursor-pointer relative group"
              @click="seekVideo"
              @mousedown="startSeeking"
              @mousemove="onProgressHover"
              @mouseleave="hideProgressTooltip"
            >
              <!-- Background track -->
              <div class="absolute inset-0 bg-gray-600 rounded-full"></div>

              <!-- Progress fill -->
              <div
                class="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-75 ease-out relative"
                :style="{ width: `${videoProgress}%` }"
              >
                <!-- Progress handle -->
                <div
                  class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-grab active:cursor-grabbing"
                  :class="{ 'opacity-100': isSeeking }"
                ></div>
              </div>

              <!-- Hover tooltip -->
              <div
                v-if="showProgressTooltip"
                class="absolute bottom-6 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded pointer-events-none transform -translate-x-1/2"
                :style="{ left: `${tooltipPosition}%` }"
              >
                {{ formatTime(tooltipTime) }}
              </div>
            </div>

            <!-- Time display -->
            <div class="text-xs text-gray-600">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>

            <!-- Volume control -->
            <div class="flex items-center space-x-1">
              <button @click="toggleMute" class="px-1 py-1 bg-gray-600 hover:bg-gray-300 rounded">
                {{ isMuted ? '🔇' : '🔊' }}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                v-model="volume"
                class="w-16"
              />
            </div>
          </div>
        </div>

        <!-- Selection controls -->
        <div class="mt-2 flex justify-between items-center">
          <div>
            <a-button
              size="small"
              @click="toggleSelectionOverlay"
              :type="showSelectionOverlay ? 'primary' : 'default'"
            >
              {{ showSelectionOverlay ? 'Cancel Selection' : 'Select Region' }}
            </a-button>
            <a-button
              v-if="currentCrop && currentCrop.width > 0"
              size="small"
              @click="clearSelection"
              class="ml-2"
            >
              Clear Selection
            </a-button>
              <a-button
              size="small"
              @click="convertVideo"
              class="ml-2"
              :loading="isConvert"
            >
              Convert Video
            </a-button>

          </div>
          <div v-if="currentCrop && currentCrop.width > 0" class="text-xs text-gray-500">
            Selected region: {{ formatCropValue() }}
          </div>

          <!-- convert if error encoder -->

        </div>
      </div>
    </div>

    <!-- OCR settings (show when files are selected) -->
    <div v-if="(processingMode === 'single' && videoFile) || ((processingMode === 'multiple' || processingMode === 'folder') && selectedFiles.length > 0)"
         class="space-y-4 p-4 border border-gray-200 rounded-md">
        <h3 class="text-lg font-medium">OCR Settings</h3>

        <!-- OCR Method Selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">OCR Method</label>
          <a-radio-group v-model:value="ocrMethod" @change="handleOcrMethodChange">
            <a-radio value="builtin">Built-in OCR</a-radio>
            <a-radio value="batch">Batch Script</a-radio>
            <a-radio value="videocr-cli">VideoCR CLI</a-radio>
          </a-radio-group>
        </div>

        <!-- Language selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Language</label>
          <a-select v-model:value="language" style="width: 100%">
            <a-select-option value="ch">Chinese</a-select-option>
            <a-select-option value="en">English</a-select-option>
            <a-select-option value="ja">Japanese</a-select-option>
            <a-select-option value="ko">Korean</a-select-option>
            <!-- <a-select-option value="vi">Vietnamese</a-select-option> -->
          </a-select>
          <p class="text-xs text-gray-500">
            <span v-if="ocrMethod === 'videocr-cli'">VideoCR CLI uses: ch, en, ja, ko</span>
            <span v-else>Built-in OCR uses: zh, en, ja, ko</span>
          </p>
        </div>

        <!-- Built-in OCR settings -->
        <div v-if="ocrMethod === 'builtin'" class="space-y-1">
          <label class="text-sm font-medium">Frame Rate (frames per second)</label>
          <a-slider
            v-model:value="frameRate"
            :min="1"
            :max="10"
            :step="1"
          />
          <div class="text-xs text-gray-500">
            Higher values capture more text but take longer to process
          </div>
        </div>

        <!-- Batch script settings -->
        <div v-if="ocrMethod === 'batch'" class="space-y-1">
          <label class="text-sm font-medium">OCR Script Path</label>
          <div class="flex space-x-2">
            <a-input v-model:value="batchScriptPath" placeholder="Path to OCR batch script" />
            <a-button @click="selectBatchScript">Browse</a-button>
          </div>
          <p class="text-xs text-gray-500">Select your custom OCR batch script</p>
        </div>

        <!-- VideoCR CLI settings -->
        <div v-if="ocrMethod === 'videocr-cli'" class="space-y-3">
          <!-- VideoCR executable path -->
          <div class="space-y-1">
            <label class="text-sm font-medium">VideoCR CLI Path</label>
            <div class="flex space-x-2">
              <a-input v-model:value="videocrPath" placeholder="Path to videocr-cli-sa.exe" />
              <a-button @click="selectVideocrPath">Browse</a-button>
            </div>
            <p class="text-xs text-gray-500">Path to videocr-cli-sa.exe (PaddleOCR-based)</p>
            <p class="text-xs text-green-600">✓ Supports crop coordinates, GPU acceleration, and advanced OCR settings</p>
            <p class="text-xs text-orange-600">⚠️ If SRT file is empty (0 bytes), try Test button first or check console logs</p>
          </div>

          <!-- Advanced settings -->
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-1">
              <label class="text-sm font-medium">Similarity Threshold</label>
              <a-slider
                v-model:value="simThreshold"
                :min="0"
                :max="100"
                :step="1"
              />
              <div class="text-xs text-gray-500">{{ simThreshold }}%</div>
            </div>

            <div class="space-y-1">
              <label class="text-sm font-medium">Confidence Threshold</label>
              <a-slider
                v-model:value="confThreshold"
                :min="0"
                :max="100"
                :step="1"
              />
              <div class="text-xs text-gray-500">{{ confThreshold }}%</div>
            </div>

            <div class="space-y-1">
              <label class="text-sm font-medium">Brightness Threshold</label>
              <a-slider
                v-model:value="brightnessThreshold"
                :min="0"
                :max="255"
                :step="1"
              />
              <div class="text-xs text-gray-500">{{ brightnessThreshold }}</div>
            </div>

            <div class="space-y-1">
              <label class="text-sm font-medium">Similar Image Threshold</label>
              <a-slider
                v-model:value="similarImageThreshold"
                :min="0"
                :max="5000"
                :step="100"
              />
              <div class="text-xs text-gray-500">{{ similarImageThreshold }}</div>
            </div>
          </div>

          <div class="space-y-1">
            <label class="text-sm font-medium">Frames to Skip</label>
            <a-slider
              v-model:value="framesToSkip"
              :min="1"
              :max="10"
              :step="1"
            />
            <div class="text-xs text-gray-500">Skip {{ framesToSkip }} frame(s) between processing</div>
          </div>

          <div class="space-y-1">
            <label class="text-sm font-medium">Max Merge Gap (seconds)</label>
            <a-slider
              v-model:value="maxMergeGap"
              :min="0.01"
              :max="1.0"
              :step="0.01"
            />
            <div class="text-xs text-gray-500">{{ maxMergeGap }}s - Maximum time gap to merge similar subtitles</div>
          </div>

          <!-- GPU and advanced options -->
          <div class="space-y-2">
            <a-checkbox v-model:checked="useGpu">
              Enable GPU acceleration (if available)
            </a-checkbox>
            <p class="text-xs text-gray-500">Requires CUDA-compatible GPU and drivers</p>

            <div class="mt-2 p-2 bg-gray-50 rounded text-xs">
              <strong>Debug Info:</strong><br>
              Command will be: <code>{{ videocrPath }}</code><br>
              Args: <code>--video_path "video.mp4" --output "output.srt" --lang "{{ language }}" ...</code><br>
              <span class="text-blue-600">Check browser console for full command details</span>
            </div>
          </div>
        </div>

        <!-- Batch processing options (only for multiple/folder mode) -->
        <div v-if="processingMode === 'multiple' || processingMode === 'folder'" class="space-y-1">
          <label class="text-sm font-medium">Batch Processing Options</label>
          <div class="space-y-2">
            <a-checkbox v-model:checked="parallelProcessing">
              Process files in parallel (faster but uses more resources)
            </a-checkbox>
            <div v-if="parallelProcessing" class="ml-6">
              <label class="text-xs text-gray-600">Max concurrent processes:</label>
              <a-slider
                v-model:value="maxConcurrentProcesses"
                :min="1"
                :max="4"
                :step="1"
                class="w-32"
              />
              <span class="text-xs text-gray-500 ml-2">{{ maxConcurrentProcesses }}</span>
            </div>
          </div>
        </div>
      </div>

    <!-- Process buttons (show when files are selected) -->
    <div v-if="(processingMode === 'single' && videoFile) || ((processingMode === 'multiple' || processingMode === 'folder') && selectedFiles.length > 0)"
         class="flex justify-end space-x-2">
        <a-button
          v-if="(isProcessing || isBatchProcessing) && currentProcessId"
          danger
          @click="stopProcessing"
        >
          Stop Processing
        </a-button>

        <!-- Single file processing -->
        <a-button
          v-if="processingMode === 'single'"
          type="primary"
          @click="processVideo"
          :loading="isProcessing"
          :disabled="!videoFile || isProcessing || isBatchProcessing || !isOcrMethodValid"
        >
          {{ isProcessing ? 'Processing...' : 'Extract Text with OCR' }}
        </a-button>

        <!-- Batch processing -->
        <a-button
          v-if="(processingMode === 'multiple' || processingMode === 'folder') && selectedFiles.length > 0"
          type="primary"
          @click="processBatchFiles"
          :loading="isBatchProcessing"
          :disabled="selectedFiles.length === 0 || isProcessing || isBatchProcessing || !isOcrMethodValid"
        >
          {{ isBatchProcessing ? `Processing ${batchProgress.current}/${batchProgress.total}...` : `Process ${selectedFiles.length} Files` }}
        </a-button>

        <!-- Validation message -->
        <div v-if="!isOcrMethodValid" class="text-xs text-red-500 mt-1">
          <span v-if="ocrMethod === 'batch' && !batchScriptPath">Please select a batch script</span>
          <span v-if="ocrMethod === 'videocr-cli' && !videocrPath">Please select VideoCR CLI executable</span>
        </div>
      </div>

    <!-- Processing status -->
    <div v-if="isProcessing || isBatchProcessing" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
          <div>
            <p class="text-blue-700">{{ processingStatus }}</p>
            <div v-if="isBatchProcessing" class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                     :style="{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }"></div>
              </div>
              <p class="text-xs text-gray-600 mt-1">{{ batchProgress.current }} of {{ batchProgress.total }} files processed</p>
            </div>
          </div>
        </div>
      </div>

    <!-- Single file result -->
    <div v-if="processingMode === 'single' && srtFilePath" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-green-700 font-medium">OCR processing complete!</p>
            <p class="text-sm text-gray-600">SRT file saved to: {{ srtFilePath }}</p>
          </div>
          <div class="space-x-2">
            <a-button type="primary" @click="openSrtFile">Open SRT</a-button>
            <a-button @click="importToSrtTable">Import to SRT Table</a-button>
          </div>
        </div>
      </div>

    <!-- Batch processing results -->
    <div v-if="(processingMode === 'multiple' || processingMode === 'folder') && completedBatchResults.length > 0"
         class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex justify-between items-center mb-2">
          <p class="text-green-700 font-medium">Batch processing results:</p>
          <a-button @click="openBatchResultsFolder" size="small">Open Results Folder</a-button>
        </div>
        <div class="space-y-1 max-h-40 overflow-y-auto">
          <div v-for="{ result, index } in completedBatchResults" :key="index"
               class="flex justify-between items-center text-sm">
            <span class="text-gray-700 truncate flex-1">{{ selectedFiles[index]?.name }}</span>
            <div class="space-x-1 ml-2">
              <a-button size="small" @click="openSrtFile(result.outputPath)">Open SRT</a-button>
              <a-button size="small" @click="importToSrtTable(result.outputPath, selectedFiles[index]?.name)">Import</a-button>
            </div>
          </div>
        </div>
      </div>

    <!-- Error -->
    <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      <p class="text-red-700">Error: {{ error }}</p>
    </div>

    <!-- Batch errors -->
    <div v-if="errorBatchResults.length > 0" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-700 font-medium mb-2">Some files failed to process:</p>
        <div class="space-y-1 max-h-32 overflow-y-auto">
          <div v-for="{ result, index } in errorBatchResults" :key="index" class="text-sm">
            <span class="text-gray-700">{{ selectedFiles[index]?.name }}:</span>
            <span class="text-red-600 ml-1">{{ result.error }}</span>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, computed } from 'vue';
import { message } from 'ant-design-vue';
import DragDropUpload from './DragDropUpload.vue';
import CropSelector from './CropSelector.vue';
import { useSRTStore } from '../stores/srtStore';

const srtStore = useSRTStore();

// Load saved videocr path on component mount
const loadVideocrPath = async () => {
  try {
    const result = await window.electronAPI.invoke('database', 'Config.getValueByName', 'videocr_path');
    if (result) {
      videocrPath.value = result;
    }
  } catch (err) {
    console.error('Error loading videocr path:', err);
  }
};

// Load videocr path when component mounts
loadVideocrPath();

// Reactive state
const processingMode = ref('single'); // 'single', 'multiple', 'folder'
const videoFile = ref(null); // For single file mode
const selectedFiles = ref([]); // For multiple files mode
const batchResults = ref([]); // Results for each file in batch processing
const videoUrl = ref(null);
const videoRef = ref(null);
const videoContainer = ref(null);
const language = ref('ch');
const frameRate = ref(3);
const batchScriptPath = ref('');
const ocrMethod = ref('videocr-cli'); // 'builtin', 'batch', 'videocr-cli'
const videocrPath = ref('');
const simThreshold = ref(80);
const confThreshold = ref(75);
const brightnessThreshold = ref(null);
const similarImageThreshold = ref(100);
const framesToSkip = ref(1);
const useGpu = ref(false);
const maxMergeGap = ref(0.09);
const isTesting = ref(false);
const isProcessing = ref(false);
const processingStatus = ref('');
const currentProcessId = ref(null);
const srtFilePath = ref(null);
const error = ref(null);
const isBatchProcessing = ref(false);
const batchProgress = ref({ current: 0, total: 0 });
const parallelProcessing = ref(false);
const maxConcurrentProcesses = ref(2);
const activeProcesses = ref(new Map()); // Track active processes for parallel processing

// Preview video state (for batch mode)
const previewFile = ref(null);
const previewFileIndex = ref(-1);
const previewVideoUrl = ref(null);
const previewVideoRef = ref(null);
const previewVideoContainer = ref(null);
const previewVideoDimensions = ref({ width: 0, height: 0 });
const previewDisplayDimensions = ref({ width: 0, height: 0 });
const isPreviewPlaying = ref(false);
const previewCurrentTime = ref(0);
const previewDuration = ref(0);
const previewVideoProgress = ref(0);
const batchCrop = ref(null); // Crop selection for batch processing
const previewCropSelector = ref(null);

// Computed properties for filtered batch results
const completedBatchResults = computed(() => {
  return batchResults.value
    .map((result, index) => ({ result, index }))
    .filter(({ result }) => result && result.status === 'completed');
});

const errorBatchResults = computed(() => {
  return batchResults.value
    .map((result, index) => ({ result, index }))
    .filter(({ result }) => result && result.status === 'error');
});

// Validate OCR method configuration
const isOcrMethodValid = computed(() => {
  if (ocrMethod.value === 'batch') {
    return !!batchScriptPath.value;
  }
  if (ocrMethod.value === 'videocr-cli') {
    return !!videocrPath.value;
  }
  return true; // builtin is always valid
});

// Video player state
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const volume = ref(1);
const isMuted = ref(false);
const videoProgress = ref(0);

// Progress bar state
const progressBarRef = ref(null);
const isSeeking = ref(false);
const showProgressTooltip = ref(false);
const tooltipPosition = ref(0);
const tooltipTime = ref(0);

// Crop selection state
const showSelectionOverlay = ref(false);
const currentCrop = ref(null);
const cropSelector = ref(null);
const videoDimensions = ref({ width: 0, height: 0 });
const displayDimensions = ref({ width: 0, height: 0 });
const isConvert = ref(false);

// Handle processing mode change
const handleModeChange = () => {
  // Reset all states when changing mode
  resetAllStates();
};

// Handle video selection for single file mode
const handleVideoSelected = (files) => {
  if (files && files.length > 0) {
    handleVideoFile(files[0]);
  }
};

// Handle multiple files selection
const handleMultipleFilesSelected = (files) => {
  if (files && files.length > 0) {
    selectedFiles.value = [...files];
    batchResults.value = new Array(files.length).fill(null).map(() => ({ status: 'pending' }));
    // Reset single file states
    videoFile.value = null;
    videoUrl.value = null;
    srtFilePath.value = null;
    error.value = null;
  }
};

// Select folder
const selectFolder = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      const folderPath = result.filePaths[0];
      // Get all video files from the folder
      const videoFiles = await getVideoFilesFromFolder(folderPath);
      if (videoFiles.length > 0) {
        selectedFiles.value = videoFiles;
        batchResults.value = new Array(videoFiles.length).fill(null).map(() => ({ status: 'pending' }));
        message.success(`Found ${videoFiles.length} video files in the folder`);
      } else {
        message.warning('No video files found in the selected folder');
      }
    }
  } catch (err) {
    console.error('Error selecting folder:', err);
    message.error('Error selecting folder');
  }
};

// Get video files from folder
const getVideoFilesFromFolder = async (folderPath) => {
  try {
    const files = await window.electronAPI.readDirectory(folderPath);
    const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'];

    const videoFiles = files
      .filter(file => {
        const ext = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        return videoExtensions.includes(ext);
      })
      .map(file => ({
        name: file.name,
        path: file.path,
        size: file.size || 0,
        type: 'video/' + file.name.toLowerCase().substring(file.name.lastIndexOf('.') + 1)
      }));

    return videoFiles;
  } catch (err) {
    console.error('Error reading directory:', err);
    return [];
  }
};

// Add more files (for multiple mode)
const addMoreFiles = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Video Files', extensions: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      const newFiles = result.filePaths.map(filePath => {
        const fileName = filePath.split(/[\/\\]/).pop();
        return {
          name: fileName,
          path: filePath,
          size: 0, // We don't have size info from file dialog
          type: 'video/' + fileName.toLowerCase().substring(fileName.lastIndexOf('.') + 1)
        };
      });

      selectedFiles.value = [...selectedFiles.value, ...newFiles];
      batchResults.value = [...batchResults.value, ...new Array(newFiles.length).fill(null).map(() => ({ status: 'pending' }))];
      message.success(`Added ${newFiles.length} more files`);
    }
  } catch (err) {
    console.error('Error adding more files:', err);
    message.error('Error adding more files');
  }
};

// Remove a file from the list
const removeFile = (index) => {
  // If removing the preview file, clear preview
  if (previewFileIndex.value === index) {
    clearPreviewFile();
  } else if (previewFileIndex.value > index) {
    // Adjust preview file index if it's after the removed file
    previewFileIndex.value--;
  }

  selectedFiles.value.splice(index, 1);
  batchResults.value.splice(index, 1);
};

// Clear all files
const clearAllFiles = () => {
  selectedFiles.value = [];
  batchResults.value = [];
  clearPreviewFile();
  resetAllStates();
};

// Reset all states
const resetAllStates = () => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
    isPlaying.value = false;
  }

  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // Reset states
  videoFile.value = null;
  videoUrl.value = null;
  srtFilePath.value = null;
  error.value = null;
  currentTime.value = 0;
  duration.value = 0;
  videoProgress.value = 0;
  isBatchProcessing.value = false;
  batchProgress.value = { current: 0, total: 0 };
  activeProcesses.value.clear();

  // Reset preview video states
  clearPreviewFile();

  // Reset selection
  clearSelection();

  // Reset display dimensions
  displayDimensions.value = { width: 0, height: 0 };

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  } else {
    currentProcessId.value = null;
  }
};

// Format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Select preview file (first file by default)
const selectPreviewFile = () => {
  if (selectedFiles.value.length > 0) {
    setPreviewFile(0);
  }
};

// Set preview file by index
const setPreviewFile = (index) => {
  if (index >= 0 && index < selectedFiles.value.length) {
    previewFileIndex.value = index;
    previewFile.value = selectedFiles.value[index];
    loadPreviewVideo();
  }
};

// Load preview video
const loadPreviewVideo = () => {
  if (!previewFile.value) return;

  // Revoke old URL to avoid memory leaks
  if (previewVideoUrl.value) {
    URL.revokeObjectURL(previewVideoUrl.value);
  }

  // Create object URL for video preview
  if (previewFile.value.path) {
    // For files from folder selection, use file:// protocol
    previewVideoUrl.value = `file://${previewFile.value.path}`;
  } else {
    // For files from drag & drop, create object URL
    previewVideoUrl.value = URL.createObjectURL(previewFile.value);
  }

  // Reset preview video state
  previewCurrentTime.value = 0;
  previewDuration.value = 0;
  previewVideoProgress.value = 0;
  isPreviewPlaying.value = false;
  previewDisplayDimensions.value = { width: 0, height: 0 };

  console.log('Preview video loaded:', {
    fileName: previewFile.value.name,
    videoUrl: previewVideoUrl.value
  });
};

// Clear preview file
const clearPreviewFile = () => {
  // Stop video playback if playing
  if (previewVideoRef.value && isPreviewPlaying.value) {
    previewVideoRef.value.pause();
    isPreviewPlaying.value = false;
  }

  // Revoke object URL
  if (previewVideoUrl.value) {
    URL.revokeObjectURL(previewVideoUrl.value);
  }

  previewFile.value = null;
  previewFileIndex.value = -1;
  previewVideoUrl.value = null;
  previewCurrentTime.value = 0;
  previewDuration.value = 0;
  previewVideoProgress.value = 0;
  previewDisplayDimensions.value = { width: 0, height: 0 };

  // Clear batch crop selection
  clearBatchSelection();
};

// Calculate preview video display dimensions
const calculatePreviewDisplayDimensions = () => {
  if (!previewVideoRef.value || !previewVideoDimensions.value.width) return;

  const videoElement = previewVideoRef.value;
  const containerRect = videoElement.getBoundingClientRect();

  // Get the actual rendered size of the video
  const videoAspectRatio = previewVideoDimensions.value.width / previewVideoDimensions.value.height;
  const containerAspectRatio = containerRect.width / containerRect.height;

  let displayWidth, displayHeight;

  if (videoAspectRatio > containerAspectRatio) {
    // Video is wider than container - fit by width
    displayWidth = containerRect.width;
    displayHeight = containerRect.width / videoAspectRatio;
  } else {
    // Video is taller than container - fit by height
    displayHeight = containerRect.height;
    displayWidth = containerRect.height * videoAspectRatio;
  }

  previewDisplayDimensions.value = {
    width: displayWidth,
    height: displayHeight
  };

  console.log('Preview display dimensions calculated:', {
    videoDimensions: previewVideoDimensions.value,
    containerRect: { width: containerRect.width, height: containerRect.height },
    displayDimensions: previewDisplayDimensions.value,
    videoAspectRatio,
    containerAspectRatio
  });
};

// Handle preview video loaded metadata
const handlePreviewVideoLoaded = () => {
  if (previewVideoRef.value) {
    // Store video dimensions for crop calculations
    previewVideoDimensions.value = {
      width: previewVideoRef.value.videoWidth,
      height: previewVideoRef.value.videoHeight
    };

    console.log('Preview video metadata loaded:', {
      videoDimensions: previewVideoDimensions.value,
      duration: previewVideoRef.value.duration,
      videoSrc: previewVideoRef.value.src
    });

    // Calculate display dimensions
    setTimeout(() => {
      calculatePreviewDisplayDimensions();
    }, 100); // Small delay to ensure video is rendered

    // Set video duration
    previewDuration.value = previewVideoRef.value.duration;

    // Reset video player state
    previewCurrentTime.value = 0;
    isPreviewPlaying.value = false;
    previewVideoProgress.value = 0;

    // Set default volume
    previewVideoRef.value.volume = 0.5; // Lower volume for preview
  }
};

// Handle preview video time update
const handlePreviewTimeUpdate = () => {
  if (previewVideoRef.value) {
    previewCurrentTime.value = previewVideoRef.value.currentTime;
    previewVideoProgress.value = (previewCurrentTime.value / previewDuration.value) * 100;
  }
};

// Handle preview video error
const handlePreviewVideoError = (event) => {
  console.error('Preview video error:', event);
  message.error('Failed to load preview video. Please check the file format.');
};

// Toggle preview play/pause
const togglePreviewPlayPause = () => {
  if (!previewVideoRef.value) return;

  if (isPreviewPlaying.value) {
    previewVideoRef.value.pause();
  } else {
    previewVideoRef.value.play();
  }

  isPreviewPlaying.value = !isPreviewPlaying.value;
};

// Preview crop selector event handlers
const onPreviewCropSelected = (cropData) => {
  batchCrop.value = cropData.normalized;
  console.log('Batch crop selected:', {
    display: cropData.display,
    normalized: cropData.normalized,
    videoDimensions: previewVideoDimensions.value,
    displayDimensions: previewDisplayDimensions.value,
    finalCrop: formatBatchCropValue()
  });
};

const onPreviewCropChange = (cropData) => {
  batchCrop.value = cropData.normalized;
};

const onPreviewCropCleared = () => {
  batchCrop.value = null;
};

// Clear batch selection
const clearBatchSelection = () => {
  if (previewCropSelector.value) {
    previewCropSelector.value.clearSelection();
  }
  batchCrop.value = null;
};

// Format batch crop value for OCR
const formatBatchCropValue = () => {
  if (!batchCrop.value || batchCrop.value.width === 0) return '';
  if (!previewVideoDimensions.value.width) return '';

  // batchCrop.value is already in video pixel coordinates from CropSelector
  // Convert to normalized coordinates (0-1 range) for OCR script
  const x1 = batchCrop.value.x / previewVideoDimensions.value.width;
  const y1 = batchCrop.value.y / previewVideoDimensions.value.height;
  const x2 = (batchCrop.value.x + batchCrop.value.width) / previewVideoDimensions.value.width;
  const y2 = (batchCrop.value.y + batchCrop.value.height) / previewVideoDimensions.value.height;

  // Ensure all values are between 0 and 1
  const clampedX1 = Math.max(0, Math.min(1, x1));
  const clampedY1 = Math.max(0, Math.min(1, y1));
  const clampedX2 = Math.max(0, Math.min(1, x2));
  const clampedY2 = Math.max(0, Math.min(1, y2));

  return `${clampedX1.toFixed(2)},${clampedY1.toFixed(2)},${clampedX2.toFixed(2)},${clampedY2.toFixed(2)}`;
};

// Get batch crop value for OCR
const getBatchCropValue = () => {
  if (!batchCrop.value || batchCrop.value.width === 0) return null;

  // For VideoCR CLI, return pixel coordinates directly
  if (ocrMethod.value === 'videocr-cli') {
    return `${batchCrop.value.x},${batchCrop.value.y},${batchCrop.value.width},${batchCrop.value.height}`;
  }

  // For built-in OCR, return normalized coordinates
  return formatBatchCropValue();
};

// Process video file
const handleVideoFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/')) {
    message.error('Invalid file type. Please select a video file.');
    return;
  }

  // Revoke old URL to avoid memory leaks
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  videoFile.value = file;
  // Create object URL for video preview
  videoUrl.value = URL.createObjectURL(file);

  console.log('Video file loaded:', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
    videoUrl: videoUrl.value
  });

  // Reset result and error
  srtFilePath.value = null;
  error.value = null;

  // Reset selection
  clearSelection();

  // Reset display dimensions
  displayDimensions.value = { width: 0, height: 0 };
};

// Calculate actual displayed video dimensions (considering object-contain)
const calculateDisplayDimensions = () => {
  if (!videoRef.value || !videoDimensions.value.width) return;

  const videoElement = videoRef.value;
  const containerRect = videoElement.getBoundingClientRect();

  // Get the actual rendered size of the video
  const videoAspectRatio = videoDimensions.value.width / videoDimensions.value.height;
  const containerAspectRatio = containerRect.width / containerRect.height;

  let displayWidth, displayHeight;

  if (videoAspectRatio > containerAspectRatio) {
    // Video is wider than container - fit by width
    displayWidth = containerRect.width;
    displayHeight = containerRect.width / videoAspectRatio;
  } else {
    // Video is taller than container - fit by height
    displayHeight = containerRect.height;
    displayWidth = containerRect.height * videoAspectRatio;
  }

  displayDimensions.value = {
    width: displayWidth,
    height: displayHeight
  };

  console.log('Display dimensions calculated:', {
    videoDimensions: videoDimensions.value,
    containerRect: { width: containerRect.width, height: containerRect.height },
    displayDimensions: displayDimensions.value,
    videoAspectRatio,
    containerAspectRatio
  });
};

// Handle video loaded metadata
const handleVideoLoaded = () => {
  if (videoRef.value) {
    // Store video dimensions for crop calculations
    videoDimensions.value = {
      width: videoRef.value.videoWidth,
      height: videoRef.value.videoHeight
    };

    console.log('Video metadata loaded:', {
      videoDimensions: videoDimensions.value,
      duration: videoRef.value.duration,
      videoSrc: videoRef.value.src
    });

    // Calculate display dimensions
    setTimeout(() => {
      calculateDisplayDimensions();
    }, 100); // Small delay to ensure video is rendered

    // Set video duration
    duration.value = videoRef.value.duration;

    // Reset video player state
    currentTime.value = 0;
    isPlaying.value = false;
    videoProgress.value = 0;

    // Set default volume
    videoRef.value.volume = volume.value;
  }
};

// Handle video time update
const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    videoProgress.value = (currentTime.value / duration.value) * 100;
  }
};

// Handle video error
const handleVideoError = (event) => {
  console.error('Video error:', event);
  error.value = 'Failed to load video. Please check the file format.';
};

// Handle video load start
const handleVideoLoadStart = () => {
  console.log('Video load started');
};

// Handle video can play
const handleVideoCanPlay = () => {
  console.log('Video can play');
};

// Toggle play/pause
const togglePlayPause = () => {
  if (!videoRef.value) return;

  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play();
  }

  isPlaying.value = !isPlaying.value;
};

// Seek video to position
const seekVideo = (event) => {
  if (!videoRef.value || !progressBarRef.value) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
  videoRef.value.currentTime = pos * duration.value;
};

// Start seeking (mouse down on progress bar)
const startSeeking = (event) => {
  if (!videoRef.value || !progressBarRef.value) return;

  isSeeking.value = true;
  seekVideo(event);

  // Add global mouse events for smooth dragging
  document.addEventListener('mousemove', onSeekingMouseMove);
  document.addEventListener('mouseup', stopSeeking);

  event.preventDefault();
};

// Handle mouse move while seeking
const onSeekingMouseMove = (event) => {
  if (!isSeeking.value) return;
  seekVideo(event);
};

// Stop seeking (mouse up)
const stopSeeking = () => {
  isSeeking.value = false;
  document.removeEventListener('mousemove', onSeekingMouseMove);
  document.removeEventListener('mouseup', stopSeeking);
};

// Handle progress bar hover
const onProgressHover = (event) => {
  if (!progressBarRef.value || !duration.value) return;

  const rect = progressBarRef.value.getBoundingClientRect();
  const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));

  tooltipPosition.value = pos * 100;
  tooltipTime.value = pos * duration.value;
  showProgressTooltip.value = true;
};

// Hide progress tooltip
const hideProgressTooltip = () => {
  showProgressTooltip.value = false;
};

// Toggle mute
const toggleMute = () => {
  if (!videoRef.value) return;

  videoRef.value.muted = !videoRef.value.muted;
  isMuted.value = videoRef.value.muted;
};

// Watch volume changes
watch(volume, (newVolume) => {
  if (videoRef.value) {
    videoRef.value.volume = newVolume;
  }
});

// Format time (seconds to MM:SS format)
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Reset video (for single mode)
const resetVideo = () => {
  if (processingMode.value === 'single') {
    resetAllStates();
  } else {
    // For multiple/folder mode, just clear the files
    clearAllFiles();
  }
};

// CropSelector event handlers
const onCropSelected = (cropData) => {
  currentCrop.value = cropData.normalized;
  console.log('Crop selected:', {
    display: cropData.display,
    normalized: cropData.normalized,
    videoDimensions: videoDimensions.value,
    displayDimensions: displayDimensions.value,
    finalCrop: formatCropValue(),
    calculatedCoords: {
      x1: cropData.normalized.x / videoDimensions.value.width,
      y1: cropData.normalized.y / videoDimensions.value.height,
      x2: (cropData.normalized.x + cropData.normalized.width) / videoDimensions.value.width,
      y2: (cropData.normalized.y + cropData.normalized.height) / videoDimensions.value.height
    }
  });
};

const onCropChange = (cropData) => {
  currentCrop.value = cropData.normalized;
};

const onCropCleared = () => {
  currentCrop.value = null;
};

// Toggle selection overlay
const toggleSelectionOverlay = () => {
  showSelectionOverlay.value = !showSelectionOverlay.value;

  console.log('Toggle selection overlay:', showSelectionOverlay.value);

  // Recalculate display dimensions when toggling selection
  if (showSelectionOverlay.value) {
    setTimeout(() => {
      // Handle single file mode
      if (processingMode.value === 'single' && videoRef.value) {
        calculateDisplayDimensions();
        console.log('After calculate, displayDimensions:', displayDimensions.value);
        if (cropSelector.value) {
          cropSelector.value.activate();
          console.log('CropSelector activated');
        }
      }

      // Handle batch mode with preview
      if ((processingMode.value === 'multiple' || processingMode.value === 'folder') && previewVideoRef.value) {
        calculatePreviewDisplayDimensions();
        console.log('After calculate, previewDisplayDimensions:', previewDisplayDimensions.value);
        if (previewCropSelector.value) {
          previewCropSelector.value.activate();
          console.log('Preview CropSelector activated');
        }
      }
    }, 50);
  } else {
    // Deactivate crop selectors
    if (cropSelector.value) {
      cropSelector.value.deactivate();
      console.log('CropSelector deactivated');
    }
    if (previewCropSelector.value) {
      previewCropSelector.value.deactivate();
      console.log('Preview CropSelector deactivated');
    }
  }
};

// Clear selection
const clearSelection = () => {
  if (cropSelector.value) {
    cropSelector.value.clearSelection();
  }
  currentCrop.value = null;
};

// Format crop value for OCR
const formatCropValue = () => {
  if (!currentCrop.value || currentCrop.value.width === 0) return '';
  if (!videoDimensions.value.width) return '';

  // currentCrop.value is already in video pixel coordinates from CropSelector
  // Convert to normalized coordinates (0-1 range) for OCR script
  const x1 = currentCrop.value.x / videoDimensions.value.width;
  const y1 = currentCrop.value.y / videoDimensions.value.height;
  const x2 = (currentCrop.value.x + currentCrop.value.width) / videoDimensions.value.width;
  const y2 = (currentCrop.value.y + currentCrop.value.height) / videoDimensions.value.height;

  // Ensure all values are between 0 and 1
  const clampedX1 = Math.max(0, Math.min(1, x1));
  const clampedY1 = Math.max(0, Math.min(1, y1));
  const clampedX2 = Math.max(0, Math.min(1, x2));
  const clampedY2 = Math.max(0, Math.min(1, y2));

  return `${clampedX1.toFixed(2)},${clampedY1.toFixed(2)},${clampedX2.toFixed(2)},${clampedY2.toFixed(2)}`;
};

// Get crop value for OCR
const getCropValue = () => {
  if (!currentCrop.value || currentCrop.value.width === 0) return null;
  return formatCropValue();
};

// Handle OCR method change
const handleOcrMethodChange = async () => {
  // Load saved videocr path when switching to videocr-cli
  if (ocrMethod.value === 'videocr-cli' && !videocrPath.value) {
    try {
      const result = await window.electronAPI.invoke('database', 'Config.getValueByName', 'videocr_path');
      if (result) {
        videocrPath.value = result;
      }
    } catch (err) {
      console.error('Error loading videocr path:', err);
    }
  }
};

// Select batch script
const selectBatchScript = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openFile'],
      filters: [
        { name: 'Batch Files', extensions: ['bat', 'cmd', 'sh', 'py'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      batchScriptPath.value = result.filePaths[0];
    }
  } catch (err) {
    console.error('Error selecting batch script:', err);
    message.error('Error selecting batch script');
  }
};

// Select VideoCR CLI path
const selectVideocrPath = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openFile'],
      filters: [
        { name: 'Executable Files', extensions: ['exe'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      videocrPath.value = result.filePaths[0];

      // Save to database
      try {
        await window.electronAPI.invoke('database', 'Config.updateValueByName', 'videocr_path', videocrPath.value);
        message.success('VideoCR path saved successfully');
      } catch (err) {
        console.error('Error saving videocr path:', err);
        message.error('Error saving VideoCR path');
      }
    }
  } catch (err) {
    console.error('Error selecting VideoCR path:', err);
    message.error('Error selecting VideoCR path');
  }
};



// Process video with OCR (single file)
const processVideo = async () => {
  if (!videoFile.value) return;

  isProcessing.value = true;
  error.value = null;
  srtFilePath.value = null;
  currentProcessId.value = null;

  try {
    processingStatus.value = 'Processing video with OCR...';

    // Get crop value if selection exists
    const cropValue = getCropValue();

    // Choose processing method based on selected OCR method
    let result;

    if (ocrMethod.value === 'batch') {
      // Use batch script
      result = await window.electronAPI.runOcrBatch({
        videoPath: videoFile.value.path,
        batchPath: batchScriptPath.value
      });
    } else if (ocrMethod.value === 'videocr-cli') {
      // Use VideoCR CLI
      result = await window.electronAPI.processVideocrCli({
        videoPath: videoFile.value.path,
        videocrPath: videocrPath.value,
        lang: language.value,
        crop: cropValue,
        simThreshold: simThreshold.value,
        confThreshold: confThreshold.value,
        brightnessThreshold: brightnessThreshold.value,
        similarImageThreshold: similarImageThreshold.value,
        framesToSkip: framesToSkip.value,
        useGpu: useGpu.value,
        maxMergeGap: maxMergeGap.value
      });
    } else {
      // Use built-in OCR
      result = await window.electronAPI.processVideoOcr({
        videoPath: videoFile.value.path,
        lang: language.value === 'ch' ? 'zh' : language.value,
        frameRate: frameRate.value,
        crop: cropValue
      });
    }

    if (result.success) {
      currentProcessId.value = result.processId;
      srtFilePath.value = result.outputPath;

      // Wait for processing to complete
      await checkProcessStatus();

      message.success('OCR processing completed successfully');
    } else {
      throw new Error(result.error || 'Failed to process video with OCR');
    }
  } catch (err) {
    error.value = err.message || 'Unknown error occurred';
    message.error('Error processing video: ' + error.value);
  } finally {
    isProcessing.value = false;
    processingStatus.value = '';
  }
};

// Process multiple files (batch processing)
const processBatchFiles = async () => {
  if (selectedFiles.value.length === 0) return;

  isBatchProcessing.value = true;
  error.value = null;
  batchProgress.value = { current: 0, total: selectedFiles.value.length };
  activeProcesses.value.clear();

  try {
    processingStatus.value = 'Starting batch processing...';

    // Reset batch results
    batchResults.value = new Array(selectedFiles.value.length).fill(null).map(() => ({ status: 'pending' }));

    if (parallelProcessing.value) {
      await processBatchFilesParallel();
    } else {
      await processBatchFilesSequential();
    }

    const completedCount = batchResults.value.filter(r => r.status === 'completed').length;
    const errorCount = batchResults.value.filter(r => r.status === 'error').length;

    if (completedCount > 0) {
      message.success(`Batch processing completed! ${completedCount} files processed successfully${errorCount > 0 ? `, ${errorCount} failed` : ''}`);
    } else {
      message.error('All files failed to process');
    }
  } catch (err) {
    error.value = err.message || 'Unknown error occurred during batch processing';
    message.error('Error during batch processing: ' + error.value);
  } finally {
    isBatchProcessing.value = false;
    processingStatus.value = '';
    currentProcessId.value = null;
    activeProcesses.value.clear();
  }
};

// Process files sequentially (one by one)
const processBatchFilesSequential = async () => {
  for (let i = 0; i < selectedFiles.value.length; i++) {
    const file = selectedFiles.value[i];
    batchResults.value[i] = { status: 'processing' };

    processingStatus.value = `Processing file ${i + 1}/${selectedFiles.value.length}: ${file.name}`;
    batchProgress.value.current = i;

    try {
      const result = await processFile(file);

      if (result.success) {
        currentProcessId.value = result.processId;

        // Wait for this file to complete
        await checkProcessStatus();

        batchResults.value[i] = {
          status: 'completed',
          outputPath: result.outputPath
        };
      } else {
        throw new Error(result.error || 'Failed to process video with OCR');
      }
    } catch (err) {
      console.error(`Error processing file ${file.name}:`, err);
      batchResults.value[i] = {
        status: 'error',
        error: err.message || 'Unknown error occurred'
      };
    }
  }

  batchProgress.value.current = selectedFiles.value.length;
};

// Process files in parallel
const processBatchFilesParallel = async () => {
  const semaphore = new Array(maxConcurrentProcesses.value).fill(null);
  let completedCount = 0;

  const processFileWithSemaphore = async (file, index) => {
    batchResults.value[index] = { status: 'processing' };

    try {
      const result = await processFile(file);

      if (result.success) {
        activeProcesses.value.set(index, result.processId);

        // Wait for this file to complete
        await checkProcessStatusForFile(result.processId);

        batchResults.value[index] = {
          status: 'completed',
          outputPath: result.outputPath
        };
      } else {
        throw new Error(result.error || 'Failed to process video with OCR');
      }
    } catch (err) {
      console.error(`Error processing file ${file.name}:`, err);
      batchResults.value[index] = {
        status: 'error',
        error: err.message || 'Unknown error occurred'
      };
    } finally {
      activeProcesses.value.delete(index);
      completedCount++;
      batchProgress.value.current = completedCount;

      processingStatus.value = `Processing files... ${completedCount}/${selectedFiles.value.length} completed`;
    }
  };

  // Create promises for all files with concurrency limit
  const promises = selectedFiles.value.map(async (file, index) => {
    // Wait for a semaphore slot
    await new Promise(resolve => {
      const checkSlot = () => {
        if (activeProcesses.value.size < maxConcurrentProcesses.value) {
          resolve();
        } else {
          setTimeout(checkSlot, 100);
        }
      };
      checkSlot();
    });

    return processFileWithSemaphore(file, index);
  });

  await Promise.all(promises);
};

// Process a single file
const processFile = async (file) => {
  // Get crop value from batch crop selection
  const cropValue = getBatchCropValue();

  // Choose processing method based on selected OCR method
  if (ocrMethod.value === 'batch') {
    // Use batch script
    return await window.electronAPI.runOcrBatch({
      videoPath: file.path,
      batchPath: batchScriptPath.value
    });
  } else if (ocrMethod.value === 'videocr-cli') {
    // Use VideoCR CLI
    console.log('cropValue',cropValue)
    return await window.electronAPI.processVideocrCli({
      videoPath: file.path,
      videocrPath: videocrPath.value,
      lang: language.value,
      crop: cropValue,
      simThreshold: simThreshold.value,
      confThreshold: confThreshold.value,
      brightnessThreshold: brightnessThreshold.value,
      similarImageThreshold: similarImageThreshold.value,
      framesToSkip: framesToSkip.value,
      useGpu: useGpu.value,
      maxMergeGap: maxMergeGap.value
    });
  } else {
    // Use built-in OCR
    return await window.electronAPI.processVideoOcr({
      videoPath: file.path,
      lang: language.value,
      frameRate: frameRate.value,
      crop: cropValue // Use batch crop for all files
    });
  }
};

// Check process status for a specific process ID
const checkProcessStatusForFile = async (processId) => {
  if (!processId) return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === processId);

      if (!isRunning) {
        // Process completed
        return;
      } else {
        // Check again after 2 seconds
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await checkProcessStatusForFile(processId);
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await checkProcessStatusForFile(processId);
  }
};

// Stop processing
const stopProcessing = async () => {
  try {
    // Stop all active processes
    const processesToStop = [];

    if (currentProcessId.value) {
      processesToStop.push(currentProcessId.value);
    }

    // Add all parallel processes
    for (const processId of activeProcesses.value.values()) {
      if (processId && !processesToStop.includes(processId)) {
        processesToStop.push(processId);
      }
    }

    if (processesToStop.length === 0) return;

    // Stop all processes
    const stopPromises = processesToStop.map(processId =>
      window.electronAPI.stopProcess(processId)
    );

    const results = await Promise.allSettled(stopPromises);

    const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

    if (successCount > 0) {
      message.success(`Stopped ${successCount} process(es)`);
    } else {
      message.error('Failed to stop processing');
    }

    // Reset states
    isProcessing.value = false;
    isBatchProcessing.value = false;
    currentProcessId.value = null;
    processingStatus.value = '';
    activeProcesses.value.clear();

  } catch (err) {
    message.error('Error stopping process: ' + (err.message || 'Unknown error'));
  }
};

// Check process status periodically
const checkProcessStatus = async () => {
  if (!currentProcessId.value || !isProcessing.value) return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === currentProcessId.value);

      if (!isRunning) {
        // Process completed
        return;
      } else {
        // Check again after 2 seconds
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await checkProcessStatus();
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await checkProcessStatus();
  }
};

// Open SRT file
const openSrtFile = async (filePath = null) => {
  const pathToOpen = filePath || srtFilePath.value;
  if (pathToOpen) {
    await window.electronAPI.openFile(pathToOpen);
  }
};

// Import to SRT Table
const importToSrtTable = async (filePath = null, fileName = null) => {
  const pathToImport = filePath || srtFilePath.value;
  if (!pathToImport) return;

  try {
    // Read the SRT file
    const result = await window.electronAPI.readFile({ filePath: pathToImport });

    if (result.success) {
      // Create a pseudo-file object from the content
      const fileNameToUse = fileName || pathToImport.split(/[\/\\]/).pop(); // Get filename without path
      const pseudoFile = {
        name: fileNameToUse,
        type: 'application/x-subrip',
        content: result.content,
        path: pathToImport
      };

      // Import to SRT Table
      await srtStore.processSrtFile(pseudoFile);

      message.success('Imported to SRT Table');
    } else {
      throw new Error(result.error || 'Failed to read SRT file');
    }
  } catch (err) {
    message.error('Error importing to SRT Table: ' + (err.message || 'Unknown error'));
  }
};

// Open batch results folder
const openBatchResultsFolder = async () => {
  if (selectedFiles.value.length > 0) {
    // Get the directory of the first file
    const firstFilePath = selectedFiles.value[0].path;
    const dirPath = firstFilePath.substring(0, firstFilePath.lastIndexOf('/') || firstFilePath.lastIndexOf('\\'));
    await window.electronAPI.openFile(dirPath);
  }
};

// Add resize listener to recalculate display dimensions
const handleResize = () => {
  if (videoDimensions.value.width > 0) {
    calculateDisplayDimensions();
  }
  if (previewVideoDimensions.value.width > 0) {
    calculatePreviewDisplayDimensions();
  }
};

// Set up resize listener
window.addEventListener('resize', handleResize);

// Clean up on component unmount
onBeforeUnmount(() => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
  }

  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // Clean up preview video
  if (previewVideoRef.value && isPreviewPlaying.value) {
    previewVideoRef.value.pause();
  }
  if (previewVideoUrl.value) {
    URL.revokeObjectURL(previewVideoUrl.value);
  }

  // Remove resize listener
  window.removeEventListener('resize', handleResize);

  // Clean up seeking event listeners
  document.removeEventListener('mousemove', onSeekingMouseMove);
  document.removeEventListener('mouseup', stopSeeking);

  // If there's an active process, stop it
  if (currentProcessId.value && (isProcessing.value || isBatchProcessing.value)) {
    stopProcessing();
  }
});


async function convertVideo() {
  try {
    isConvert.value = true;
    const result = await window.electronAPI.convertVideoToVideo({
      input: videoFile.value.path
    });

    if (result.success) {
      // currentProcessId.value = result.processId;

      videoUrl.value = `file://${videoFile.value.path}`;
      message.success('Convert video successfully!');
 
    } else {
      throw new Error(result.error || 'Failed to convert to WAV');
    }
    isConvert.value = false;
  } catch (err) {
    message.error('Error converting video: ' + (err.message || 'Unknown error'));
    isConvert.value = false;
  }

}


</script>
