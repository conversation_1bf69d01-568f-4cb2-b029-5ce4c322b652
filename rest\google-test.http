GET https://generativelanguage.googleapis.com/v1beta/models
X-Goog-Api-Key: AIzaSyAbAlPhgWgEbJ7QXyijktFy6W-5WqAsBN8


###
X-Goog-Api-Key: AIzaSyDKMMtTQ1QnacfbzX-RPVPx8uFrcNGjij0
X-Goog-Api-Key: AIzaSyAnIN9pRtfPR0SUBnLJNk8nQWagrfYCnak
AIzaSyDXIOIfSVIsOVVxacvbO1B0I68TbtsYgew
AIzaSyASx2zLQFSBzaY1Jc_x8TgC053Cbdp89WM
AIzaSyDKW-9Y5uCVYcA2DFcja5cFseD97wkjTcg =ok
AIzaSyC0QKGnXrgSnNn_ze_gzim1oPp8ZYP3rXk =429
AIzaSyAOZczwL-o1QOnyIUeglXA4aDYJ6u4saZA =429
AIzaSyDwDb_oOuF6jS7XBGgxog_Uu9QC7Nls60s =429
AIzaSyAbAlPhgWgEbJ7QXyijktFy6W-5WqAsBN8 =429

###
POST https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
X-Goog-Api-Key: AIzaSyAbAlPhgWgEbJ7QXyijktFy6W-5WqAsBN8

{
    "contents": [
        {
            "role": "user",
            "parts": [
                {
                    "text": "You are a professional translator with expertise in Vietnamese. Please translate the following text to Vietnamese.\n\nTranslation Style: Dịch rõ ràng, trực tiếp và trung lập, giữ nguyên ý nghĩa và ngữ cảnh gốc\n\nContext:\n- Preserve the original context, style, tone, and terminology\n\n\n\nCRITICAL REQUIREMENTS:\n1. Translation Accuracy:\n   - Translate ALL text content accurately\n   - Maintain the original meaning and intent\n   - Preserve technical terms and proper nouns\n   - Keep cultural references when appropriate\n   - Ensure natural flow in the target language\n   - Consider cultural nuances and idioms\n   - Adapt expressions to be culturally appropriate\n   - Maintain the author's voice and style\n   - Preserve humor, sarcasm, and tone\n   - Keep metaphors and analogies meaningful\n\n2. Language Quality:\n   - Use proper grammar and syntax in Vietnamese\n   - Maintain consistent terminology throughout\n   - Adapt idioms and expressions appropriately\n   - Ensure readability and natural flow\n   - Use appropriate register and formality level\n   - Consider regional language variations\n   - Use natural and modern language\n   - Avoid literal translations when inappropriate\n   - Maintain proper sentence structure\n   - Use appropriate punctuation and formatting\n\n3. Content Understanding:\n   - Analyze the text's purpose and audience\n   - Consider the text's genre and style\n   - Understand cultural references and context\n   - Identify key themes and messages\n   - Recognize technical or specialized content\n   - Consider the emotional tone and impact\n   - Understand implicit meanings\n   - Identify wordplay or double meanings\n   - Recognize formal vs informal language\n   - Understand the text's historical context\n\n4. Special Handling:\n   - Keep URLs, email addresses, and code unchanged\n   - Preserve mathematical formulas and equations\n   - Maintain proper names and trademarks\n   - Keep dates and numbers in original format\n   - Preserve HTML/Markdown tags if present\n   - Handle quotes and citations properly\n   - Maintain formatting of lists and tables\n   - Preserve special characters and symbols\n   - Keep currency and measurement units\n   - Handle abbreviations and acronyms\n\n5. Output Requirements:\n   - Return ONLY the translated text\n   - Do not add explanations or notes\n   - Do not include the original text\n   - Ensure the translation is complete\n   - Verify all content is translated\n   - Maintain proper spacing and formatting\n   - Use appropriate line breaks\n   - Ensure consistent terminology\n   - Check for any missing content\n   - Verify cultural appropriateness\n\nText to translate:\nhello fend"
                }
            ]
        }
    ]
}


###

GET https://chat.amri2k.com/api/openai/v1/models

###
POST https://openrouter.ai/api/v1/chat/completions
Authorization: Bearer 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

{
    "model": "google/gemini-2.5-pro-exp-03-25:free",
    "messages": [
        {
            "role": "user",
            "content": "You are a professional translator with expertise in Vietnamese. Please translate the following text to Vietnamese.\n\nTranslation Style: Dịch rõ ràng, trực tiếp và trung lập, giữ nguyên ý nghĩa và ngữ cảnh gốc\n\nContext:\n- Preserve the original context, style, tone, and terminology\n\n\n\nCRITICAL REQUIREMENTS:\n1. Translation Accuracy:\n   - Translate ALL text content accurately\n   - Maintain the original meaning and intent\n   - Preserve technical terms and proper nouns\n   - Keep cultural references when appropriate\n   - Ensure natural flow in the target language\n   - Consider cultural nuances and idioms\n   - Adapt expressions to be culturally appropriate\n   - Maintain the author's voice and style\n   - Preserve humor, sarcasm, and tone\n   - Keep metaphors and analogies meaningful\n\n2. Language Quality:\n   - Use proper grammar and syntax in Vietnamese\n   - Maintain consistent terminology throughout\n   - Adapt idioms and expressions appropriately\n   - Ensure readability and natural flow\n   - Use appropriate register and formality level\n   - Consider regional language variations\n   - Use natural and modern language\n   - Avoid literal translations when inappropriate\n   - Maintain proper sentence structure\n   - Use appropriate punctuation and formatting\n\n3. Content Understanding:\n   - Analyze the text's purpose and audience\n   - Consider the text's genre and style\n   - Understand cultural references and context\n   - Identify key themes and messages\n   - Recognize technical or specialized content\n   - Consider the emotional tone and impact\n   - Understand implicit meanings\n   - Identify wordplay or double meanings\n   - Recognize formal vs informal language\n   - Understand the text's historical context\n\n4. Special Handling:\n   - Keep URLs, email addresses, and code unchanged\n   - Preserve mathematical formulas and equations\n   - Maintain proper names and trademarks\n   - Keep dates and numbers in original format\n   - Preserve HTML/Markdown tags if present\n   - Handle quotes and citations properly\n   - Maintain formatting of lists and tables\n   - Preserve special characters and symbols\n   - Keep currency and measurement units\n   - Handle abbreviations and acronyms\n\n5. Output Requirements:\n   - Return ONLY the translated text\n   - Do not add explanations or notes\n   - Do not include the original text\n   - Ensure the translation is complete\n   - Verify all content is translated\n   - Maintain proper spacing and formatting\n   - Use appropriate line breaks\n   - Ensure consistent terminology\n   - Check for any missing content\n   - Verify cultural appropriateness\n\nText to translate:\nHello fend"
        }
    ]
}

###
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
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
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

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 =ok


###
GET https://api.openai.com/v1/models
Authorization: Bearer ********************************************************************************************************************************************************************

###
POST https://api.openai.com/v1/chat/completions
Authorization: Bearer ********************************************************************************************************************************************************************

{
    "model": "gpt-4",
    "messages": [
        {
            "role": "user",
            "content": "Use the context below to answer the question:\n\n{context}\n\nQuestion: {user_query}\nAnswer:"
        }
    ],
    "temperature": 0.2
}

###

GET https://api.deepseek.com/v1/models
Authorization: Bearer 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


###
PEXELS_API_KEY=u4eXg16pNHbWDuD16SBiks0vKbV21xHDziyLCHkRyN9z08ruazKntJj7


### translate AIzaSyBWDj0QJvVIx8XOhRegXX5_SrRWxhT5Hs4
GET https://translate-pa.googleapis.com/v1/supportedLanguages?client=te&display_language=en-US&key=AIzaSyBWDj0QJvVIx8XOhRegXX5_SrRWxhT5Hs4&callback=callback

###
POST https://translate-pa.googleapis.com/v1/translateHtml
Content-Type: application/json+protobuf
X-Goog-Api-Key: AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520

[
    [
        [
            "小白",
            "小自",
            "小自自",
            "诶",
            "咋被",
            "狗链",
            "拴了起来",
            "如果你",
            "肯为我",
            "生个小孩",
            "我",
            "就让",
            "主人",
            "把你放出来",
            "不想",
            "生小孩",
            "那",
            "你就",
            "搁这",
            "拴着吧",
        ],
        "auto",
        "vi"
    ],
    "te"
]