const Tesseract = require('tesseract.js');
const { Jimp } = require('jimp');

// Optimized Chinese OCR based on test results
async function optimizedChineseOCR(imagePath) {
  console.log('🎯 Running optimized Chinese OCR...');
  
  try {
    // Based on tests, original image works best, but let's do minimal processing
    const image = await Jimp.read(imagePath);
    
    // Only apply minimal processing if needed
    let processedImage = image;
    
    // Scale up small images
    const { width, height } = image.bitmap;
    if (width < 400 || height < 400) {
      const scaleFactor = Math.max(400 / width, 400 / height);
      processedImage = image.scale(scaleFactor);
      console.log(`🔍 Scaled image by factor: ${scaleFactor.toFixed(2)}`);
    }
    
    // Save processed image
    await processedImage.write('optimized_processed.jpg');
    
    // Use the best configuration found from tests
    const result = await Tesseract.recognize(
      'optimized_processed.jpg',
      'chi_sim', // Simplified Chinese worked best
      {
        logger: m => {
          if (m.status === 'recognizing text') {
            process.stdout.write(`\r⏳ Progress: ${Math.round(m.progress * 100)}%`);
          }
        },
        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
        tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
        // Remove character restrictions for better flexibility
      }
    );
    
    const rawText = result.data.text.trim();
    const cleanedText = rawText.replace(/\s+/g, ''); // Remove all spaces for Chinese text

    console.log(`\n✅ OCR Result:`);
    console.log(`📝 Raw Text: "${rawText}"`);
    console.log(`🧹 Cleaned Text: "${cleanedText}"`);
    console.log(`🎯 Confidence: ${result.data.confidence.toFixed(2)}%`);

    return {
      text: cleanedText,
      rawText: rawText,
      confidence: result.data.confidence
    };
    
  } catch (error) {
    console.error('❌ OCR Error:', error);
    return null;
  }
}

// Export for use in other modules
module.exports = { optimizedChineseOCR };

// Run if called directly
if (require.main === module) {
  const imagePath = process.argv[2] || './logs/73k.png';
  optimizedChineseOCR(imagePath);
}
