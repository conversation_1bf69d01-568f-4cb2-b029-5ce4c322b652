const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const { convertAnyToWav } = require('../ffmpegHandler');
const { get_path_bin } = require('../utils');

function convertPitchFactorToCents(pitchFactor) {
  if (pitchFactor <= 0) {
    throw new Error('pitchFactor phải > 0');
  }
  const cents = 1200 * Math.log2(pitchFactor);
  return Math.round(cents); // làm tròn gần nhất
}

function sliderPitchToCents(sliderValue) {
  return Math.round(sliderValue * 100); // Giả sử sliderValue từ 0 đến 100, với 50 là không thay đổi
}

async function runSoxCommand(inputFile, outputFile, options = []) {
  const soxPath = get_path_bin('sox/sox');
  const command = `"${soxPath}" "${inputFile}" "${outputFile}" ${options.join(' ')}`;
  console.log(`Executing sox command: ${command}`);
  
  return new Promise((resolve, reject) => {
    exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing sox command: ${stderr}`);
        
        // Nếu lỗi normalize, thử lại không có norm
        if (stderr.includes("can't reclaim headroom") || stderr.includes("gain:")) {
          console.log('Retrying without normalize...');
          const optionsWithoutNorm = options.filter(opt => !opt.includes('norm'));
          const retryCommand = `"${soxPath}" "${inputFile}" "${outputFile}" ${optionsWithoutNorm.join(' ')}`;
          
          exec(retryCommand, { encoding: 'utf8' }, (retryError, retryStdout, retryStderr) => {
            if (retryError) {
              console.error(`Retry failed: ${retryStderr}`);
              return reject(retryError);
            }
            console.log(`Retry successful: ${retryStdout}`);
            resolve(retryStdout);
          });
        } else {
          return reject(error);
        }
      } else {
        console.log(`Sox command output: ${stdout}`);
        resolve(stdout);
      }
    });
  });
}

async function soxStart(inputFile, outputFile, options = {}) {
  if (!inputFile || !outputFile) {
    throw new Error('Invalid input or output file');
  }
  
  // Convert to wav
  const inputFileExt = path.extname(inputFile).toLowerCase();
  const tempWavFile = path.join(path.dirname(inputFile), `${path.basename(inputFile, inputFileExt)}_converted.wav`);
  await convertAnyToWav(null, inputFile, tempWavFile);
  fs.unlinkSync(inputFile); // Xóa file gốc
  fs.renameSync(tempWavFile, inputFile); // Đổi tên file tạm thành file gốc
  
  const pitchIsNumber = typeof options?.pitch == 'number' ? options.pitch : parseInt(options.pitch);
  const pitchCents = options.pitch ? sliderPitchToCents(pitchIsNumber) : 0;
  
  // Sắp xếp options theo thứ tự tối ưu
  const soxOptions = [];
  
  // 1. Trim trước (giảm data)
  if (options.trim) {
    soxOptions.push(`trim ${options.trim.start} ${options.trim.end}`);
  }
  
  // 2. Reverse
  if (options.reverse) {
    soxOptions.push('reverse');
  }
  
  // 3. Pitch & tempo
  if (pitchCents !== 0) {
    soxOptions.push(`pitch ${pitchCents}`);
  }
  
  if (options.speed) {
    soxOptions.push(`tempo ${options.speed}`);
  }
  
  // 4. Volume trước effects
  if (options.vol) {
    soxOptions.push(`vol ${options.vol}`);
  }
  
  // 5. Effects
  if (options.reverb) {
    soxOptions.push(`reverb ${options.reverb}`);
  }
  
  if (options.echo) {
    soxOptions.push(`echo ${options.echo.gainIn} ${options.echo.gainOut} ${options.echo.delay} ${options.echo.decay}`);
  }
  
  // 6. Fade cuối
  if (options.fade) {
    soxOptions.push(`fade t ${options.fade.start} ${options.fade.duration}`);
  }
  
  // 7. Normalize cuối (sẽ tự động retry nếu lỗi)
  soxOptions.push('norm');
  
  await runSoxCommand(inputFile, outputFile, soxOptions);
}

module.exports = {
  soxStart,
};

// Test usage:
// soxStart("F:\\ReviewDao\\20-nam\\p4.wav", 'F:\\ReviewDao\\20-nam\\p4.wav', {
//     pitch: 1.1, // Tăng cao độ lên 20%
//     speed: 1.2,
//     // echo: {
//     //     gainIn: 0.8,
//     //     gainOut: 0.88,
//     //     delay: 60, // Thời gian trễ của echo
//     //     decay: 0.5 // Độ suy giảm của echo
//     // },
// }).then(() => {
//   console.log('Audio extraction and processing completed successfully.');
// }).catch(err => {
//   console.error('Error during audio extraction:', err);
// });