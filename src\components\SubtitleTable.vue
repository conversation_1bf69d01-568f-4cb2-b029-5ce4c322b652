<template>
  <!-- Container với layout pattern chuẩn -->
  <div class="flex-1 flex-column bg-gray-900 text-white h-full relative">
    <!-- Batch error quick retry section -->
    <div v-if="onRetryBatch && errorBatchCount > 0" class="mx-4 mb-2 p-3 border border-amber-200 bg-amber-50 rounded">
      <div class="flex items-start gap-2">
        <AlertTwoTone twoToneColor="#faad14" class="mt-1 flex-shrink-0" />
        <div class="flex-1">
          <h3 class="text-sm font-medium text-amber-800">
            Quick batch retry
          </h3>
          <p class="text-xs text-amber-700 mb-2">
            {{ t("batchErrorDisplay.description") }}
          </p>

          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mt-1">
            <div v-for="batch in errorBatchesWithErrors" :key="`batch-${batch.actualBatchIndex}`"
              class="flex items-center justify-between py-1 px-2 bg-white border border-amber-100 rounded text-sm">
              <div class="truncate">
                <span class="font-medium">
                  {{ t("subtitleTable.batch") }} {{ batch.actualBatchIndex + 1 }}:
                </span>
                #{{ batch.firstId }}-{{ batch.lastId }}
                <span class="ml-1 text-rose-600 text-xs">
                  ({{ batch.errorCount }} {{ t("subtitleTable.errors") }})
                </span>
              </div>
              <a-button size="small" type="text" @click="handleRetryBatch(batch.actualBatchIndex)"
                :disabled="retryingBatch === batch.actualBatchIndex || translating"
                class="h-6 px-2 text-xs text-amber-600 hover:text-amber-700 hover:bg-amber-100">
                <template v-if="retryingBatch === batch.actualBatchIndex">
                  <LoadingOutlined class="mr-1" />
                  {{ t("common.retrying") }}
                </template>
                <template v-else>
                  <ReloadOutlined class="mr-1" />
                  {{ t("common.retry") }}
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Subtitle table -->
    <div class="relative flex-column h-full w-full">
      <div ref="tableContainerRef"
        :class="`flex-1 h-full absolute w-full  overflow-y-auto bg-slate-800 custom-scrollbar border border-gray-700 scroll-container`">
        <!-- :style="{ height: `${isMaxHeight}px` }" -->

        <a-table :dataSource="currentPageData" :columns="columns" :pagination="false" size="small"
           style="margin-bottom: 40px;">
          <!-- ID Column -->
          <template #bodyCell="{ column, record }">
            <div :class="[record.isEnabled ? '' : 'opacity-50']">
              <div v-if="column.key === 'id'"
                :ref="record.id === state.currentPlayingSubtitleId ? 'highlightedRowRef' : null"
                :class="`flex items-center ${record.id === state.currentPlayingSubtitleId ? 'bg-green-800' : ''}`">
                {{ record.id }}
                <span v-if="record.id % batchSize === 1" class="ml-1 text-xs text-gray-400">
                  B{{ Math.floor((record.id - 1) / batchSize) + 1 }}
                </span>
                <!-- checkbox for isEnabled -->
                <a-checkbox class="ml-1" :checked="record.isEnabled" @click.stop
                  @change.stop="() => toggleEnabledForSubtitle(record.id)"
                  :title="record.isEnabled ? 'Disable audio' : 'Enable audio'" :style="{ transform: 'scale(1.4)' }" />
              </div>

              <!-- Time Column -->
              <template v-else-if="column.key === 'time'">
                <div class="text-xs text-gray-500 whitespace-nowrap" @click="handleRowClick(record.id)">
                  <div>{{ formatTime(record.startTime) }}</div>
                  <div class="text-gray-400">↓</div>
                  <div>{{ formatTime(record.endTime) }}</div>
                </div>
              </template>

              <!-- Original Text Column -->
              <template v-else-if="column.key === 'translatedText'">
                <!-- 2 item col - Always show textarea -->
                <div class="flex flex-row gap-2">
                  <a-textarea
                    :value="record.text"
                    @input="(e) => handleTextInput(record.id, e)"
                    @blur="(e) => handleTextBlur(record.id, e)"
                    :rows="1"
                    class="flex-1 min-h-[80px] max-h-[150px] text-sm custom-scrollbar transparent-textarea"
                    placeholder="Nhập text cho subtitle"
                  />
                  <!-- screen shot and ocr text handler -->
                  <div class="flex">
                    <a-button size="small" @click.stop="handleReText(record)" :loading="isLoading[record.id]" class="flex items-center">
                      <scan-outlined />
                    </a-button>
                  </div>
                </div>

                <!-- </template> -->

                <!-- Translation Column -->
                <!-- <template v-else-if="column.key === 'translatedText'"> -->
                <div class="relative flex flex-row gap-2">
                  <!-- Always show textarea for translation -->
                  <div class="w-full relative">
                    <a-textarea
                      :value="record.translatedText || ''"
                      @input="(e) => handleTranslationInput(record.id, e)"
                      @blur="(e) => handleTranslationBlur(record.id, e)"
                      :rows="2"
                      class="w-full min-h-[80px] max-h-[150px] text-sm custom-scrollbar transparent-textarea"
                      placeholder="Nhập bản dịch cho subtitle"
                      :class="record.status === 'error' ? 'error-textarea' : ''"
                    />

                    <!-- Error overlay -->
                    <div v-if="record.status === 'error'" class="absolute top-1 right-1">
                      <a-button type="link" size="small" class="text-red-500 hover:text-red-700 p-0"
                        @click.stop="onRetry(record.id)" :disabled="translating">
                        <ReloadOutlined />
                      </a-button>
                    </div>

                    <!-- Loading overlay -->
                    <div v-if="record.status === 'translating'" class="absolute top-1 right-1">
                      <LoadingOutlined class="text-blue-500" />
                    </div>

                    <!-- Suggestions panel -->
                    <div v-if="record.id === suggestingId" class="suggestion-panel bg-slate-700 absolute top-full left-0 w-full z-10"
                      @click.stop>
                      <div class="px-3 py-2 bg-blue-900 border-b border-blue-400">
                        <div class="flex justify-between items-center">
                          <h4 class="text-sm font-medium text-blue-100 flex items-center">
                            <ThunderboltOutlined class="mr-1.5 text-blue-200" />
                            {{ t("subtitleTable.aiSuggestions") }}
                          </h4>
                          <a-button type="text" size="small" class="h-6 w-6 p-0 rounded-full" @click="closeSuggestions">
                            ×
                          </a-button>
                        </div>
                        <p class="text-xs text-blue-200 mt-1">
                          {{ t("subtitleTable.chooseSuggestion") }}
                        </p>
                      </div>

                      <!-- Loading state -->
                      <div v-if="loadingSuggestions" class="py-8 text-center">
                        <LoadingOutlined class="text-xl mb-2 text-blue-200" />
                        <p class="text-sm text-gray-500">
                          {{ t("common.loading") }}
                        </p>
                      </div>

                      <!-- Suggestions list -->
                      <div v-else-if="suggestions.length > 0">
                        <div v-for="(suggestion, idx) in suggestions" :key="`suggestion-${idx}`"
                          class="p-2 hover:bg-slate-800" @click="applyTranslationSuggestion(suggestion)">
                          <div class="text-blue-300">
                            {{ getLabelText(idx) }}
                          </div>
                          <p class="text-sm text-gray-100 whitespace-pre-wrap">
                            {{ suggestion }}
                          </p>
                          <div class="flex justify-between items-center mt-2">
                            <p class="text-xs text-blue-300">
                              {{ t("subtitleTable.clickToApply") }}
                            </p>
                            <a-button size="small" type="text" class="h-6 text-xs py-0 px-2"
                              @click.stop="applyTranslationSuggestion(suggestion)">
                              Áp dụng
                            </a-button>
                          </div>
                        </div>
                      </div>

                      <!-- No suggestions -->
                      <div v-else class="py-4 text-center">
                        <p class="text-sm text-gray-500">
                          {{ t("subtitleTable.noSuggestions") }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="flex-1" v-if="!(editingId === record.id)"></div>
                  <div class="flex mt-1" v-if="!(editingId === record.id)">
                    <a-button size="small"
                      @click.stop="handleSuggestTranslation(record.id, record.text, record.translatedText)"
                      :disabled="translating" class="flex items-center">
                      <TranslationOutlined />
                    </a-button>
                  </div>
                </div>
              </template>

              <!-- Action Column -->
              <template v-else-if="column.key === 'action'">
                <div class="flex flex-col space-y-1 items-center" @click.stop>
                  <div class="flex items-center justify-center gap-2 mb-2">
                    <SoundOutlined />
                    <div class="flex items-center gap-2.5 border border-gray-600 rounded">
                      <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 1)"
                        @change="() => toggleVoiceForSubtitle(record.id, 1)" :style="{ transform: 'scale(1.4)' }" />
                      <Audio v-if="record.isGenerated1" :src="record.audioUrl1" />
                    </div>
                    <div class="flex items-center gap-2.5 border border-gray-600 rounded">
                      <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 2)" class="ml-1"
                        @change="() => toggleVoiceForSubtitle(record.id, 2)" :style="{ transform: 'scale(1.4)' }" />
                      <Audio v-if="record.isGenerated2" :src="record.audioUrl2" />
                    </div>
                    <div class="flex items-center border border-gray-600 rounded gap-1">
                      <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 3)" class="ml-1"
                        @change="() => toggleVoiceForSubtitle(record.id, 3)" :style="{ transform: 'scale(1.4)' }" />
                      <Audio v-if="record.isGenerated3" :src="record.audioUrl3" />
                    </div>
                    <div class="flex items-center gap-2.5 border border-gray-600 rounded">
                      <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 4)" class="ml-1"
                        @change="() => toggleVoiceForSubtitle(record.id, 4)" :style="{ transform: 'scale(1.4)' }" />
                      <Audio v-if="record.isGenerated4" :src="record.audioUrl4" />
                    </div>
                    <div class="flex items-center gap-2.5 border border-gray-600 rounded">
                      <a-checkbox :checked="isVoiceSelectedForSubtitle(record.id, 5)" class="ml-1"
                        @change="() => toggleVoiceForSubtitle(record.id, 5)" :style="{ transform: 'scale(1.4)' }" />
                      <Audio v-if="record.isGenerated5" :src="record.audioUrl5" />
                    </div>


                    <!-- btn -->
                    <a-button size="small" @click="generateAudioForSubtitle(record, true)" :disabled="generatingAudio"
                      :loading="generatingAudio && state.currentPlayingSubtitleId === record.id" title="Generate Audio" class="
                      flex items-center">
                      <reload-outlined v-if="!(generatingAudio && state.currentPlayingSubtitleId === record.id)" />
                    </a-button>
                  </div>
                  <SubtitleActions :record="record" :editing-id="editingId" :translating="translating"
                    :loading-suggestions="loadingSuggestions" :retrying-batch="retryingBatch" :batch-size="batchSize"
                    :on-suggest-translation="onSuggestTranslation" @play="handlePlayVideo" @edit="handleEdit"
                    @suggest="handleSuggestTranslation" @retry="onRetry" @delete="handleDelete"
                    @insert-before="handleInsertBefore" @insert-after="handleInsertAfter" @split="handleSplit"
                    @merge="handleMerge" />
                </div>
              </template>
            </div>
          </template>
        </a-table>
      </div>
    </div>

    <!-- Insert Modal -->
    <SubtitleInsertModal v-model:open="showInsertModal" :target-id="insertTargetId"
      :target-subtitle="insertTargetSubtitle" :position="insertPosition" @confirm="handleInsertConfirm" />

    <!-- Split Modal -->
    <SubtitleSplitModal v-model:open="showSplitModal" :subtitle="splitTargetSubtitle" @confirm="handleSplitConfirm" />

    <!-- Fixed Pagination -->
    <div class="fixed-pagination">
      <a-pagination
        v-bind="paginationConfig"
        class="custom-pagination"
      />
    </div>
  </div>
</template>

<script setup>
import { defineComponent, ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import {
  UpOutlined,
  DownOutlined,
  EditOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
  LoadingOutlined,
  AlertTwoTone,
  LeftOutlined,
  RightOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  SoundOutlined,
  PlayCircleOutlined,
  PauseOutlined,
  DeleteOutlined,
  ScanOutlined,
  TranslationOutlined

} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
// import HighlightedText from './HighlightedText.vue';
import { useI18n } from '@/i18n/i18n';
import { useSubtitleStore } from '@/stores/subtitle-store';
import { useTTSStore } from '@/stores/ttsStore';
import Audio from './Audio.vue';
import SubtitleActions from './SubtitleActions.vue';
import SubtitleInsertModal from './SubtitleInsertModal.vue';
import SubtitleSplitModal from './SubtitleSplitModal.vue';
import { state } from '@/lib/state';
import { parseTimeToSeconds, formatTime } from '@/lib/utils';
import {
  reorderSubtitleIds,
  adjustSubtitleTimes,
  splitSubtitle,
  mergeSubtitles,
  insertSubtitle
} from '@/lib/subtitleUtils';

const props = defineProps({
  subtitles: {
    type: Array,
    required: true
  },
  onRetry: {
    type: Function,
    required: true
  },
  onRetryBatch: {
    type: Function,
    default: null
  },
  onUpdateTranslation: {
    type: Function,
    required: true
  },
  onUpdateOriginalText: {
    type: Function,
    required: true
  },
  translating: {
    type: Boolean,
    default: false
  },
  batchSize: {
    type: Number,
    default: 10
  },
  highlightedSubtitleId: {
    type: Number,
    default: null
  },
  onSuggestTranslation: {
    type: Function,
    default: null
  },
  activeGlossary: {
    type: Object,
    default: null
  },
  toggleVoiceForSubtitle: {
    type: Function,
    default: () => { }
  },
  generateAudioForSubtitle: {
    type: Function,
    default: () => { }
  },
  generatingAudio: {
    type: Boolean,
    default: false
  },
  currentGeneratingId: {
    type: Number,
    default: null
  },
  onPlayVideo: {
    type: Function,
    default: null
  },
  onDelete: {
    type: Function,
    default: null
  },
  onInsert: {
    type: Function,
    default: null
  },
  onSplit: {
    type: Function,
    default: null
  },
  onMerge: {
    type: Function,
    default: null
  },
  onReorder: {
    type: Function,
    default: null
  },
  mode: {
    type: String,
    default: 'normal'
  }
})
const { t } = useI18n();
const subtitleStore = useSubtitleStore();
const ttsStore = useTTSStore();

// State variables
const editingId = ref(null);
const editingId1 = ref(null);
const editText = ref('');
const editText1 = ref('');
const originalEditText = ref('');
const originalEditText1 = ref('');
const retryingBatch = ref(null);
const expandedTable = ref(false);
const tableContainerRef = ref(null);
const highlightedRowRef = ref(null);
const suggestingId = ref(null);
const suggestions = ref([]);
const loadingSuggestions = ref(false);
const isLoading = ref({});

// Modal states
const showInsertModal = ref(false);
const showSplitModal = ref(false);
const insertTargetId = ref(null);
const insertTargetSubtitle = ref(null);
const insertPosition = ref('after');
const splitTargetSubtitle = ref(null);

// Table pagination - now managed by Ant Design
const currentPage = ref(1);
const rowsPerPage = ref(30);

// Calculate total pages
const totalPages = computed(() =>
  Math.ceil(props.subtitles.length / rowsPerPage.value)
);

// Get current page data - manually slice data for current page
const currentPageData = computed(() => {
  if (props.subtitles.length === 0) return [];

  const start = (currentPage.value - 1) * rowsPerPage.value;
  const end = Math.min(start + rowsPerPage.value, props.subtitles.length);
  return props.subtitles.slice(start, end);
});

// Pagination config - for standalone pagination component
const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: rowsPerPage.value,
  total: props.subtitles.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => {
    const translatedCount = props.subtitles.filter(s => s.status === "translated").length;
    const errorCount = props.subtitles.filter(s => s.status === "error").length;
    const start = (currentPage.value - 1) * rowsPerPage.value + 1;
    const end = Math.min(start + rowsPerPage.value - 1, total);
    return `${start}-${end} của ${total} subtitles | ${translatedCount} ${t("subtitleTable.translated")}, ${errorCount} ${t("subtitleTable.errors")} | ID trùng lặp: ${duplicateIds}`;
  },
  pageSizeOptions: ['10', '20', '30', '50', '100'],
  onChange: goToPage,
  onShowSizeChange: handlePageSizeChange,
}));

// Get current page subtitles - now handled by Ant Design Table
// const currentPageSubtitles = computed(() => {
//   if (props.subtitles.length === 0) return [];

//   const start = (currentPage.value - 1) * rowsPerPage.value;
//   const end = Math.min(start + rowsPerPage.value, props.subtitles.length);
//   return props.subtitles.slice(start, end);
// });

// Table columns
const columns = [
  {
    title: t("subtitleTable.id"),
    key: 'id',
    width: 10,
    className: 'bg-slate-800 text-white'
  },
  {
    title: t("subtitleTable.time"),
    key: 'time',
    width: 10,
    className: 'bg-slate-800 text-white'
  },
  // {
  //   title: t("subtitleTable.originalText"),
  //   key: 'text',
  //   className: 'bg-slate-800 text-white'
  // },
  {
    title: t("subtitleTable.translation"),
    key: 'translatedText',
    width: 300,
    className: 'bg-slate-800 text-white'
  },
  // {
  //   title: 'Voice',
  //   key: 'voice',
  //   width: 150,
  //   className: 'bg-slate-800 text-white'
  // },
  // {
  //   title: t("subtitleTable.statusTranslation"),
  //   key: 'status',
  //   width: 110,
  //   className: 'bg-slate-800 text-white text-center'
  // },
  {
    title: t("subtitleTable.action"),
    key: 'action',
    width: 90,
    className: 'bg-slate-800 text-white text-right'
  }
];

// Batches with errors
const errorBatches = computed(() => {
  const result = [];
  const processedBatchIndexes = new Set();

  // Process all subtitles to find error batches
  for (const subtitle of props.subtitles) {
    if (subtitle.status !== "error") continue;

    const batchIndex = Math.floor((subtitle.id - 1) / props.batchSize);

    // Skip if we already processed this batch
    if (processedBatchIndexes.has(batchIndex)) continue;
    processedBatchIndexes.add(batchIndex);

    // Find all subtitles in this batch
    const batchItems = props.subtitles.filter(item => {
      const itemBatchIndex = Math.floor((item.id - 1) / props.batchSize);
      return itemBatchIndex === batchIndex;
    });

    result.push({
      batchIndex,
      items: batchItems,
      hasErrors: true
    });
  }

  return result;
});

// Filtered error batches with actual errors
const errorBatchesWithErrors = computed(() => {
  return errorBatches.value.map(batch => {
    // Count actual errors (some may have been fixed)
    const errorItems = batch.items.filter(item => {
      const subtitle = props.subtitles.find(s => s.id === item.id);
      return subtitle && subtitle.status === "error";
    });

    const errorCount = errorItems.length;

    if (errorCount === 0) return null;

    const firstId = batch.items[0]?.id;
    const lastId = batch.items[batch.items.length - 1]?.id;
    const actualBatchIndex = Math.floor((firstId - 1) / props.batchSize);

    return {
      actualBatchIndex,
      firstId,
      lastId,
      errorCount
    };
  }).filter(Boolean);
});

// Count of error batches
const errorBatchCount = computed(() => errorBatches.value.length);

// Debounce timers
const debounceTimers = ref({});

// Store original values to detect changes
const originalValues = ref({});

// Memoized event handlers to prevent recreation on each render
const textInputHandlers = new Map();
const textBlurHandlers = new Map();
const translationInputHandlers = new Map();
const translationBlurHandlers = new Map();

const getTextInputHandler = (id) => {
  if (!textInputHandlers.has(id)) {
    textInputHandlers.set(id, (e) => {
      // Store original value on first input if not already stored
      if (!originalValues.value[`text_${id}`]) {
        const subtitle = props.subtitles.find(s => s.id === id);
        if (subtitle) {
          originalValues.value[`text_${id}`] = subtitle.text || '';
        }
      }
      handleTextChange(id, e.target.value);
    });
  }
  return textInputHandlers.get(id);
};

const getTextBlurHandler = (id) => {
  const key = `${id}`;
  if (!textBlurHandlers.has(key)) {
    textBlurHandlers.set(key, (e) => {
      // Clear any pending debounce timer first
      if (debounceTimers.value[`text_${id}`]) {
        clearTimeout(debounceTimers.value[`text_${id}`]);
        delete debounceTimers.value[`text_${id}`];
      }

      // Get the current value from the textarea
      const currentValue = e.target.value;
      const originalValue = originalValues.value[`text_${id}`] || '';
      const subtitle = props.subtitles.find(s => s.id === id);
      if (subtitle) {
        // Update the subtitle object immediately
        subtitle.text = currentValue;
        // Save if there's a change from original value
        if (originalValue !== currentValue) {
          handleSave1(id, currentValue);
        }
        // Clear the stored original value
        delete originalValues.value[`text_${id}`];
      }
    });
  }
  return textBlurHandlers.get(key);
};

const getTranslationInputHandler = (id) => {
  if (!translationInputHandlers.has(id)) {
    translationInputHandlers.set(id, (e) => {
      console.log('Translation input handler called for id:', id, 'value:', e.target.value);

      // Store original value on first input if not already stored
      if (!originalValues.value[`translation_${id}`]) {
        const subtitle = props.subtitles.find(s => s.id === id);
        if (subtitle) {
          originalValues.value[`translation_${id}`] = subtitle.translatedText || '';
          console.log('Stored original value for id:', id, 'value:', originalValues.value[`translation_${id}`]);
        }
      }
      handleTranslationChange(id, e.target.value);
    });
  }
  return translationInputHandlers.get(id);
};

const getTranslationBlurHandler = (id) => {
  const key = `${id}`;
  if (!translationBlurHandlers.has(key)) {
    console.log('Creating new translation blur handler for id:', id);
    translationBlurHandlers.set(key, (e) => {
      console.log('Translation blur handler called for id:', id, 'value:', e.target.value);

      // Clear any pending debounce timer first
      if (debounceTimers.value[`translation_${id}`]) {
        clearTimeout(debounceTimers.value[`translation_${id}`]);
        delete debounceTimers.value[`translation_${id}`];
      }

      // Get the current value from the textarea
      const currentValue = e.target.value;
      const originalValue = originalValues.value[`translation_${id}`] || '';
      console.log('Original value:', originalValue, 'Current value:', currentValue);

      const subtitle = props.subtitles.find(s => s.id === id);
      if (subtitle) {
        // Update the subtitle object immediately
        subtitle.translatedText = currentValue;
        // Save if there's a change from original value
        if (originalValue !== currentValue) {
          console.log('Saving translation change for id:', id);
          handleSave(id, currentValue);
        } else {
          console.log('No change detected for id:', id);
        }
        // Clear the stored original value
        delete originalValues.value[`translation_${id}`];
      }
    });
  } else {
    console.log('Reusing existing translation blur handler for id:', id);
  }
  return translationBlurHandlers.get(key);
};



// New simplified handlers
const handleTextInput = (id, e) => {
  // Store original value on first input if not already stored
  if (!originalValues.value[`text_${id}`]) {
    const subtitle = props.subtitles.find(s => s.id === id);
    if (subtitle) {
      originalValues.value[`text_${id}`] = subtitle.text || '';
      // console.log('Stored original text value for id:', id, 'value:', originalValues.value[`text_${id}`]);
    }
  }

  // Clear existing timer
  if (debounceTimers.value[`text_${id}`]) {
    clearTimeout(debounceTimers.value[`text_${id}`]);
  }

  // Set new timer with debounce
  debounceTimers.value[`text_${id}`] = setTimeout(() => {
    const subtitle = props.subtitles.find(s => s.id === id);
    if (subtitle) {
      subtitle.text = e.target.value;
    }
  }, 300); // 300ms delay
};

const handleTextBlur = (id, e) => {
  // console.log('handleTextBlur called for id:', id, 'value:', e.target.value);

  // Clear any pending debounce timer first
  if (debounceTimers.value[`text_${id}`]) {
    clearTimeout(debounceTimers.value[`text_${id}`]);
    delete debounceTimers.value[`text_${id}`];
  }

  // Get the current value from the textarea
  const currentValue = e.target.value;
  const originalValue = originalValues.value[`text_${id}`] || '';
  // console.log('Text - Original value:', originalValue, 'Current value:', currentValue);

  const subtitle = props.subtitles.find(s => s.id === id);
  if (subtitle) {
    // Update the subtitle object immediately
    subtitle.text = currentValue;
    // Save if there's a change from original value
    if (originalValue !== currentValue) {
      // console.log('Saving text change for id:', id);
      handleSave1(id, currentValue);
    } else {
      console.log('No text change detected for id:', id);
    }
    // Clear the stored original value
    delete originalValues.value[`text_${id}`];
  }
};

const handleTranslationInput = (id, e) => {
  // console.log('handleTranslationInput called for id:', id, 'value:', e.target.value);

  // Store original value on first input if not already stored
  if (!originalValues.value[`translation_${id}`]) {
    const subtitle = props.subtitles.find(s => s.id === id);
    if (subtitle) {
      originalValues.value[`translation_${id}`] = subtitle.translatedText || '';
      // console.log('Stored original translation value for id:', id, 'value:', originalValues.value[`translation_${id}`]);
    }
  }

  // Clear existing timer
  if (debounceTimers.value[`translation_${id}`]) {
    clearTimeout(debounceTimers.value[`translation_${id}`]);
  }

  // Set new timer with debounce
  debounceTimers.value[`translation_${id}`] = setTimeout(() => {
    const subtitle = props.subtitles.find(s => s.id === id);
    if (subtitle) {
      subtitle.translatedText = e.target.value;
    }
  }, 300); // 300ms delay
};

const handleTranslationBlur = (id, e) => {
  // console.log('handleTranslationBlur called for id:', id, 'value:', e.target.value);

  // Clear any pending debounce timer first
  if (debounceTimers.value[`translation_${id}`]) {
    clearTimeout(debounceTimers.value[`translation_${id}`]);
    delete debounceTimers.value[`translation_${id}`];
  }

  // Get the current value from the textarea
  const currentValue = e.target.value;
  const originalValue = originalValues.value[`translation_${id}`] || '';
  // console.log('Translation - Original value:', originalValue, 'Current value:', currentValue);

  const subtitle = props.subtitles.find(s => s.id === id);
  if (subtitle) {
    // Update the subtitle object immediately
    subtitle.translatedText = currentValue;
    // Save if there's a change from original value
    if (originalValue !== currentValue) {
      // console.log('Saving translation change for id:', id);
      handleSave(id, currentValue);
    } else {
      console.log('No translation change detected for id:', id);
    }
    // Clear the stored original value
    delete originalValues.value[`translation_${id}`];
  }
};

// Legacy handlers (keeping for compatibility)
const handleTextChange = (id, newText) => {
  // Clear existing timer
  if (debounceTimers.value[`text_${id}`]) {
    clearTimeout(debounceTimers.value[`text_${id}`]);
  }

  // Set new timer with debounce
  debounceTimers.value[`text_${id}`] = setTimeout(() => {
    const subtitle = props.subtitles.find(s => s.id === id);
    if (subtitle) {
      subtitle.text = newText;
    }
  }, 300); // 300ms delay
};

const handleTranslationChange = (id, newText) => {
  // Clear existing timer
  if (debounceTimers.value[`translation_${id}`]) {
    clearTimeout(debounceTimers.value[`translation_${id}`]);
  }

  // Set new timer with debounce
  debounceTimers.value[`translation_${id}`] = setTimeout(() => {
    const subtitle = props.subtitles.find(s => s.id === id);
    if (subtitle) {
      subtitle.translatedText = newText;
    }
  }, 300); // 300ms delay
};

// Handle editing a subtitle (legacy - now unused)
const handleEdit = (id, text) => {
  editingId.value = id;
  editText.value = text;
  originalEditText.value = text; // Store original text

  // Auto-focus textarea after DOM update
  nextTick(() => {
    const textarea = document.querySelector(`[data-editing-id="${id}"] textarea`);
    if (textarea) {
      textarea.focus();
    }
  });
};
const handleEdit1 = (id, text) => {
  editingId1.value = id;
  editText1.value = text;
  originalEditText1.value = text; // Store original text

  // Auto-focus textarea after DOM update
  nextTick(() => {
    const textarea = document.querySelector(`[data-editing-id1="${id}"] textarea`);
    if (textarea) {
      textarea.focus();
    }
  });
};

// Save edited subtitle - for always-visible textareas
const handleSave = (id, newText) => {
  // console.log('handleSave', id, 'saving translation:', newText);
  props.onUpdateTranslation(id, newText);
};

const handleSave1 = (id, newText) => {
  // console.log('handleSave1', id, 'saving text:', newText);
  props.onUpdateOriginalText(id, newText);
};

// Legacy save handlers (now unused)
const handleSaveLegacy = (id) => {
  // console.log('handleSaveLegacy', id, editText.value);

  // Only update if text has changed
  if (editText.value !== originalEditText.value) {
    props.onUpdateTranslation(id, editText.value);
  }

  editingId.value = null;
  editText.value = '';
  originalEditText.value = '';
};
const handleSave1Legacy = (id) => {
  // Only update if text has changed
  if (editText1.value !== originalEditText1.value) {
    props.onUpdateOriginalText(id, editText1.value);
  }

  editingId1.value = null;
  editText1.value = '';
  originalEditText1.value = '';
};

// Cancel editing
const handleCancel = () => {
  editingId.value = null;
  editText.value = '';
  originalEditText.value = '';
};
const handleCancel1 = () => {
  editingId1.value = null;
  editText1.value = '';
  originalEditText1.value = '';
};

// Handle row click
const handleRowClick = (id) => {
  if (id !== editingId.value && props.onRetry && !props.translating) {
    if (state.currentPlayingSubtitleId !== id) {
      props.onRetry(id);
    }
  }
};

// Custom row props
// const customRow = (record) => {
//   return {
//     onClick: () => handleRowClick(record.id),
//   };
// };
// Thêm function scrollToSubtitle vào return của setup()
const scrollToSubtitle = (subtitleId) => {
  if (!tableContainerRef.value || !subtitleId) return;

  // Tìm row element bằng class
  const targetRow = tableContainerRef.value.querySelector(`.subtitle-row-${subtitleId}`);

  if (targetRow) {
    // Scroll container đến vị trí của row, đặt row ở giữa màn hình
    const containerRect = tableContainerRef.value.getBoundingClientRect();
    const rowRect = targetRow.getBoundingClientRect();
    const containerScrollTop = tableContainerRef.value.scrollTop;

    // Tính toán vị trí scroll để row nằm ở giữa container
    const targetScrollTop = containerScrollTop +
      (rowRect.top - containerRect.top) -
      (containerRect.height / 2) +
      (rowRect.height / 2);

    tableContainerRef.value.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: 'smooth'
    });
  }
};


// Try to retry a batch of subtitles
const handleRetryBatch = async (batchIndex) => {
  if (!props.onRetryBatch) return;

  console.log(`SubtitleTable: Retrying batch ${batchIndex}`);
  retryingBatch.value = batchIndex;

  try {
    // Check if batch exists in UI
    const batchExistsInUI = errorBatchesWithErrors.value.some(
      batch => batch.actualBatchIndex === batchIndex
    );

    if (!batchExistsInUI) {
      console.warn(`Batch ${batchIndex} no longer exists or has no errors in UI`);
      retryingBatch.value = null;
      return;
    }

    await props.onRetryBatch(batchIndex);
    console.log(`SubtitleTable: Successfully retried batch ${batchIndex}`);
  } catch (error) {
    console.error(`SubtitleTable: Error retrying batch ${batchIndex}:`, error);
  } finally {
    retryingBatch.value = null;
  }
};

// Handle pagination
const goToPage = (page, pageSize) => {
  console.log(`SubtitleTable: Going to page ${page}, pageSize: ${pageSize}`);

  currentPage.value = page;
  if (pageSize) {
    rowsPerPage.value = pageSize;
  }

  // Scroll to top when changing page
  nextTick(() => {
    if (tableContainerRef.value) {
      tableContainerRef.value.scrollTo({ top: 0, behavior: 'smooth' });
    }
  });
};

// Handle page size change
const handlePageSizeChange = (current, size) => {
  console.log(`SubtitleTable: Changing page size to ${size}`);
  currentPage.value = current;
  rowsPerPage.value = size;

  // Scroll to top
  nextTick(() => {
    if (tableContainerRef.value) {
      tableContainerRef.value.scrollTo({ top: 0, behavior: 'smooth' });
    }
  });
};

// Handle scrolling to highlighted subtitle
const scrollToHighlighted = () => {
  nextTick(() => {
    // Add a small delay to ensure DOM is fully updated
    setTimeout(() => {
      if (highlightedRowRef.value) {
        highlightedRowRef.value.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      } else {
        // Fallback: try to find the highlighted row by class
        const highlightedElement = tableContainerRef.value?.querySelector('.bg-green-800');
        if (highlightedElement) {
          highlightedElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }
      }
    }, 100);
  });
};

// Handle suggesting translations
const handleSuggestTranslation = async (id, originalText, currentTranslation) => {
  if (!props.onSuggestTranslation) return;

  suggestingId.value = id;
  loadingSuggestions.value = true;

  try {
    const aiSuggestions = await props.onSuggestTranslation(
      id,
      originalText,
      currentTranslation
    );

    // Parse the response if it's in code block format
    if (aiSuggestions.length === 1 && aiSuggestions[0].includes("```json")) {
      try {
        const jsonMatch = aiSuggestions[0].match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch && jsonMatch[1]) {
          const jsonData = JSON.parse(jsonMatch[1]);
          if (jsonData.translations && Array.isArray(jsonData.translations)) {
            suggestions.value = jsonData.translations;
            return;
          }
        }
      } catch (parseError) {
        console.error("Error parsing JSON in suggestion:", parseError);
      }
    }

    suggestions.value = aiSuggestions;
  } catch (error) {
    console.error("Error getting translation suggestions:", error);
    suggestions.value = [];
  } finally {
    loadingSuggestions.value = false;
  }
};

// Apply a suggestion
const applyTranslationSuggestion = (suggestion) => {
  if (suggestingId.value === null) return;

  props.onUpdateTranslation(suggestingId.value, suggestion);
  closeSuggestions();
};

// Close suggestions
const closeSuggestions = () => {
  suggestingId.value = null;
  suggestions.value = [];
};

// Get label type for suggestions
const getLabelType = (idx) => {
  return idx === 0 ? "common" : idx === 1 ? "academic" : "creative";
};

// Get label text for suggestions
const getLabelText = (idx) => {
  return idx === 0 ? "Thông dụng" : idx === 1 ? "Học thuật" : "Sáng tạo";
};

// Toggle table expansion
const setExpandedTable = (value) => {
  expandedTable.value = value;
};

// Watch for changes in subtitles length
watch(() => props.subtitles.length, (newLength) => {
  // Reset to page 1 if subtitles change significantly
  if (newLength > 0 && currentPage.value > Math.ceil(newLength / rowsPerPage.value)) {
    currentPage.value = 1;
  }
});

// Watch for highlighted subtitle changes to scroll to it
watch(() => state.currentPlayingSubtitleId, (newId, oldId) => {
  if (newId && newId !== oldId) {
    // Find which page this subtitle is on and navigate to it
    const index = props.subtitles.findIndex(s => s.id === newId);
    if (index !== -1) {
      const targetPage = Math.floor(index / rowsPerPage.value) + 1;
      if (targetPage !== currentPage.value) {
        currentPage.value = targetPage;
        // Wait for page change to complete before scrolling
        nextTick(() => {
          setTimeout(() => {
            scrollToHighlighted();
          }, 200);
        });
      } else {
        // Same page, just scroll
        scrollToHighlighted();
      }
    }
  }
}, { immediate: true });

function handlePlayVideo(record) {
  if (props.onPlayVideo) {
    props.onPlayVideo(record);
  }
}
// Debounced save for enabled state changes
const enabledStateTimers = ref({});

function toggleEnabledForSubtitle(id) {
  const subtitle = props.subtitles.find(s => s.id === id);
  if (subtitle) {
    // Update the UI immediately for responsive feel
    subtitle.isEnabled = !subtitle.isEnabled;

    // Clear existing timer for this subtitle
    if (enabledStateTimers.value[id]) {
      clearTimeout(enabledStateTimers.value[id]);
    }

    // Set debounced timer to save the change
    enabledStateTimers.value[id] = setTimeout(() => {
      // The change is already applied to the subtitle object
      // The store's debounced persistence will handle the actual saving
      delete enabledStateTimers.value[id];
    }, 150); // 150ms debounce for lighter feel
  }
}
async function handleReText(record) {
  console.log('handleReText', record);
  isLoading.value[record.id] = true;
  // state.currentTime
  // screenshotOvr
  const videoPath = ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')
  const cloned = JSON.parse(JSON.stringify({
    videoPath,
    fromSecond: state.currentTime,
    cropData: state.cropData
  }));
  const res = await electronAPI.getTextFromFrameVideo(cloned)
    .catch(err => {
      console.error('Error getting text from frame video:', err);
      message.error('Error getting text from frame video: ' + err.message, 5);
      isLoading.value[record.id] = false;
      return null;
    });
  console.log('res', res);
  if (!res?.text) {
    message.error('Không lấy được đoạn text từ frame video', 5);
    isLoading.value[record.id] = false;
    return;
  } else {
    state.cropText = res.text;
  }


  const confirm = await new Promise((resolve) => {
    Modal.confirm({
      title: 'Xác nhận',
      content: `Lấy đoạn này: ${res?.text}?`,
      okText: 'Có',
      cancelText: 'Không',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });

  if (confirm && res?.text) {
    record.text = res.text;
    console.log('record', record);
  }
  isLoading.value[record.id] = false;
}
async function handleDelete(id) {
  const confirm = await new Promise((resolve) => {
    Modal.confirm({
      title: 'Xác nhận',
      content: 'Bạn có chắc chắn muốn xóa subtitle này?',
      okText: 'Có',
      cancelText: 'Không',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });

  if (confirm) {
    props.onDelete(id);
  }
}

// Handle insert subtitle before current row
const handleInsertBefore = (record) => {
  insertTargetId.value = record.id;
  insertTargetSubtitle.value = record;
  insertPosition.value = 'before';
  showInsertModal.value = true;
};

// Handle insert subtitle after current row
const handleInsertAfter = (record) => {
  insertTargetId.value = record.id;
  insertTargetSubtitle.value = record;
  insertPosition.value = 'after';
  showInsertModal.value = true;
};
F.handleInsertAfter = handleInsertAfter.bind(this);

// Handle insert confirm from modal
const handleInsertConfirm = (newSubtitleData) => {
  if (!props.onInsert) return;

  const newSubtitle = insertSubtitle(props.subtitles, insertTargetId.value, newSubtitleData.position);
  if (newSubtitle) {
    // Update with user data
    newSubtitle.start = newSubtitleData.startTime;
    newSubtitle.end = newSubtitleData.endTime;
    newSubtitle.startTime = parseTimeToSeconds(newSubtitleData.startTime);
    newSubtitle.endTime = parseTimeToSeconds(newSubtitleData.endTime);
    newSubtitle.text = newSubtitleData.text;
    newSubtitle.translatedText = newSubtitleData.translatedText;

    props.onInsert(newSubtitle, insertTargetId.value, newSubtitleData.position, newSubtitleData.adjustTiming);
  }
};

// Handle split subtitle
const handleSplit = (record) => {
  splitTargetSubtitle.value = record;
  showSplitModal.value = true;
};

// Handle split confirm from modal
const handleSplitConfirm = (splitData) => {
  if (!props.onSplit || !splitTargetSubtitle.value) return;

  const record = splitTargetSubtitle.value;
  const splitTime = parseTimeToSeconds(splitData.splitTime);

  const firstPart = {
    ...record,
    end: splitData.splitTime,
    endTime: splitTime,
    text: splitData.part1.text,
    translatedText: splitData.part1.translatedText
  };

  const secondPart = {
    ...record,
    id: record.id + 1,
    index: record.index + 1,
    start: splitData.splitTime,
    startTime: splitTime,
    text: splitData.part2.text,
    translatedText: splitData.part2.translatedText,
    // Reset audio generation status
    isGenerated: false,
    isGenerated1: false,
    isGenerated2: false,
    isGenerated3: false,
    audioUrl: '',
    audioUrl1: '',
    audioUrl2: '',
    audioUrl3: '',
    duration: 0
  };

  props.onSplit(record.id, [firstPart, secondPart]);
};

// Handle merge subtitle
const handleMerge = (record) => {
  if (!props.onMerge) return;

  const nextSubtitle = props.subtitles.find(s => s.id === record.id + 1);
  if (!nextSubtitle) {
    message.warning('Không có subtitle tiếp theo để hợp nhất');
    return;
  }

  Modal.confirm({
    title: 'Hợp nhất subtitle',
    content: `Bạn có muốn hợp nhất subtitle ${record.id} với ${nextSubtitle.id}?`,
    okText: 'Có',
    cancelText: 'Không',
    onOk: () => {
      const mergedSubtitle = mergeSubtitles(record, nextSubtitle);
      props.onMerge([record.id, nextSubtitle.id], mergedSubtitle);
    }
  });
};

const isMaxHeight = computed(() => {
  // Tính toán chiều cao dựa trên percentage từ state.tabContentRef
  // state.tabContentRef chứa percentage (0-100) của container height cho phần TOP
  // Khi kéo xuống (tăng top %), subtitle table sẽ có ít chiều cao hơn
  // Khi kéo lên (giảm top %), subtitle table sẽ có nhiều chiều cao hơn
  if (!state.tabContentRef) return 400; // Default fallback

  // Lấy chiều cao của window và trừ đi các phần không cần thiết
  const windowHeight = window.innerHeight;
  const headerHeight = 120; // Header + tab bar
  const paginationHeight = 60; // Fixed pagination height
  const containerHeight = Math.max(1200, windowHeight - headerHeight);

  // Tính chiều cao thực tế của phần top (video/editor area)
  const topHeightPercent = Math.max(20, Math.min(80, state.tabContentRef));
  const topHeight = Math.floor(containerHeight * topHeightPercent / 100);

  // Chiều cao còn lại cho subtitle table = container - top - pagination
  // Khi topHeight tăng (kéo xuống) -> availableHeight giảm
  // Khi topHeight giảm (kéo lên) -> availableHeight tăng
  const availableHeight = containerHeight - topHeight - paginationHeight;

  return Math.max(200, availableHeight); // Minimum 200px
});
const duplicateIds = props.subtitles
  .filter((e, i, arr) => {
    const start = Math.max(i - 5, 0);
    const end = Math.min(i + 5 + 1, arr.length); // +1 vì slice không bao gồm end
    const window = arr.slice(start, end).filter((_, idx) => start + idx !== i); // bỏ chính nó
    return window.some(x => x.text === e.text);
  })
  .map(e => e.id)
  .join(',');


const isVoiceSelectedForSubtitle = (subtitleId, voiceNumber) => {
  return props.subtitles.find(item => item.id === subtitleId).isVoice === voiceNumber;
};






// Cleanup memoized handlers on unmount to prevent memory leaks
onUnmounted(() => {
  textInputHandlers.clear();
  textBlurHandlers.clear();
  translationInputHandlers.clear();
  translationBlurHandlers.clear();

  // Clear debounce timers
  Object.values(debounceTimers.value).forEach(timer => clearTimeout(timer));
  debounceTimers.value = {};

  // Clear enabled state timers
  Object.values(enabledStateTimers.value).forEach(timer => clearTimeout(timer));
  enabledStateTimers.value = {};

  // Clear original values
  originalValues.value = {};
});

</script>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.suggestion-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* background: white; */
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 35%;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 80%;
}

/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}

/* Absolute pagination container - giới hạn trong component */
.fixed-pagination {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 100 !important;
  /* background: rgba(30, 41, 59, 0.95) !important; */
  backdrop-filter: blur(8px) !important;
  border-top: 1px solid #374151 !important;
  padding: 8px 16px !important;
  margin: 0 !important;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}


/* Transparent textarea styling - Optimized for performance */
:deep(.transparent-textarea) {
  background: transparent !important;
  border: 1px solid transparent !important;
  color: #e5e7eb !important;
  resize: none !important;
  /* Remove transition to reduce CPU usage on hover */
}

:deep(.transparent-textarea:hover) {
  background: rgba(55, 65, 81, 0.3) !important;
  border-color: rgba(75, 85, 99, 0.5) !important;
}

:deep(.transparent-textarea:focus) {
  background: rgba(55, 65, 81, 0.6) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  outline: none !important;
  /* Only transition on focus for better UX */
}

:deep(.transparent-textarea::placeholder) {
  color: rgba(156, 163, 175, 0.5) !important;
  font-style: italic !important;
}

/* Error state for textarea */
:deep(.error-textarea) {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

:deep(.error-textarea:hover) {
  background: rgba(239, 68, 68, 0.15) !important;
  border-color: rgba(239, 68, 68, 0.5) !important;
}

:deep(.error-textarea:focus) {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

/* Disable Ant Design table hover effects to reduce CPU usage */
:deep(.ant-table-tbody > tr:hover > td) {
  background: transparent !important;
  transition: none !important;
}

:deep(.ant-table-tbody > tr) {
  transition: none !important;
}

:deep(.ant-table-tbody > tr > td) {
  transition: none !important;
}

/* Custom lightweight hover effect */
:deep(.ant-table-tbody > tr:hover) {
  background: rgba(55, 65, 81, 0.1) !important;
}
</style>
