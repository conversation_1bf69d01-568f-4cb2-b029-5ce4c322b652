<template>
  <div
    class="contenteditable"
    :title="modelValue"
    contenteditable="true"
    @blur="onBlur"
    ref="editableDiv"
  >{{ modelValue }}</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const editableDiv = ref<HTMLElement | null>(null)

function onBlur() {
  if (editableDiv.value) {
    emit('update:modelValue', editableDiv.value.textContent ?? '')
  }
}
</script>

<style scoped>
.contenteditable {
  outline: none;
  white-space: pre-wrap;
}
</style>
