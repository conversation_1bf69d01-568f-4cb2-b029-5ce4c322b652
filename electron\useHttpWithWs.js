const axios = require('axios');

const WebSocket = require('ws');

const F = {
    l: console.log.bind(console, 'useHttpWithWs'),
    sleep: (delay, random = 0)=> new Promise(resolve => setTimeout(resolve, (Math.floor(Math.random() * random) + delay))),

}

const S = {
    vBeeWs: null,
    vbeeRequestMap: null,
    event: null,
}


const task_log_flag = 'tts_task_log_flag';
function replay_msg(text) {
    const event = S.event;
    let tmp = {
        data: null,
        msg: text,
    };
    if (!event) F.l('vbeeWsService', text);
    if (event) event.reply(task_log_flag, tmp);
}
F.vbeeWsService = (token) => {
    S.vBeeWs = new WebSocket('wss://vbee.vn/api/v1/notifications');
    let timePing = null;
    // Tạo một Map để lưu các trạng thái `request_id`
    S.vbeeRequestMap = new Map();

    S.vBeeWs.onmessage = (event) => {
        const data = JSON.parse(event.data);
        const { type, result, status } = data;
        if (status === 0) replay_msg('⚠️ Error in: ' + JSON.stringify(data));
        if (data.type === 'SYNTHESIS' && data.result) {
            const { request_id, status, audio_link } = data.result;

            if (status === 'SUCCESS') {
                F.l(`Audio generated successfully: ${audio_link}`);
                replay_msg('✅ SUCCESS', result);
                // Cập nhật trạng thái thành công trong requestMap
                if (S.vbeeRequestMap.has(request_id)) {
                    S.vbeeRequestMap.get(request_id).resolve(data.result);
                    S.vbeeRequestMap.delete(request_id);
                }
            } else if (status === 'IN_PROGRESS') {
                F.l(`Processing request ${request_id}...`);
                replay_msg(`⏳ Request ${request_id} in progress...`);
            } else if (status === 'FAILURE') {
                console.error(`Request ${request_id} failed: ${result.error_message}`);
                replay_msg(`❌ Request ${request_id} failed: ${result.error_message}`);
                // Cập nhật trạng thái thất bại trong requestMap
                if (S.vbeeRequestMap.has(request_id)) {
                    S.vbeeRequestMap.get(request_id).reject(new Error(result.error_message));
                    S.vbeeRequestMap.delete(request_id);
                }
            }
        }
        if (data.type === 'PONG') {
            // F.l('Received PONG');
        }
    };

    S.vBeeWs.onopen = (event) => {
        S.vBeeWs.send(JSON.stringify({ type: 'INIT', accessToken: token }));
        timePing = setInterval(() => {
            S.vBeeWs.send(JSON.stringify({ type: 'PING' }));
        }, 15000);
        F.l('WebSocket on open');
        replay_msg('🔌 WebSocket open', event);
    };

    S.vBeeWs.onerror = (error) => {
        console.error('WebSocket error:', error);
        clearInterval(timePing);
        replay_msg('🚨 WebSocket error:', error);
    };

    S.vBeeWs.onclose = () => {
        F.l('WebSocket closed');
        clearInterval(timePing);
        replay_msg('🛑 WebSocket stopped');
        F.vbeeWsService(token);
    };
};

// Gửi yêu cầu HTTP
F.createVbTTS = async (data, token) => {
    if (!data) return null;
    if (!S.vBeeWs || S.vBeeWs.readyState !== WebSocket.OPEN) {
        F.vbeeWsService(token);
        await F.sleep(3000)
    }
    const response = await axios({
        method: 'POST',
        url: 'https://vbee.vn/api/v1/synthesis',
        data,
        headers: {
            Authorization: 'Bearer ' + token,
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
        },
        timeout: 120000,
    });

    return response.data;
};

F.processVbeeTTS = async (data, token, retryCount = 0) => {
    const maxRetries = 3;

    try {
        // Gửi yêu cầu HTTP
        const response = await F.createVbTTS(data, token);
        const res = response.result

        if (res && res.status === 'IN_PROGRESS') {
            F.l(`Request ${res.request_id} is in progress...`);

            // Thêm vào Map để xử lý sau
            return await new Promise((resolve, reject) => {
                S.vbeeRequestMap.set(res.request_id, { resolve, reject });
            });
        } else {
            throw new Error(`Unexpected status: ${res?.status}`, response);
        }
    } catch (error) {
        // Kiểm tra nếu là lỗi HTTP 400
        const isBadRequest = error?.response?.status === 400;

        if (isBadRequest && retryCount < maxRetries) {
            console.warn(`TTS request failed with 400. Retrying (${retryCount + 1}/${maxRetries})...`);
            await F.sleep(10000, 6000)
            return F.processVbeeTTS(data, token, retryCount + 1);
        }

        console.error('Error during TTS request:', error);
        throw error; // Trả lỗi ra ngoài để xử lý
    }
};


module.exports = {
    vbeeWsService: F.vbeeWsService,
    createVbTTS: F.createVbTTS,
    processVbeeTTS: F.processVbeeTTS,
}