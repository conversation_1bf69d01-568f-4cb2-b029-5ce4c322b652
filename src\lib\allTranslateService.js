import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { state } from './state';
import { getTranslationPrompt } from './promptService';
// import { translateText } from "./translationService";

// Service configuration
let currentConfig = {
  service: 'deepseek',
  model: 'deepseek-chat',
  apiKey: '',
  baseURL: 'https://api.deepseek.com/v1',
};

// Translation control
let shouldStopTranslation = false;

// Service endpoints mapping
const SERVICE_ENDPOINTS = {
  openai: 'https://api.openai.com/v1',
  deepseek: 'https://api.deepseek.com/v1',
  gemini: '', // Gemini uses different SDK
  claude: 'https://api.anthropic.com/v1',
  openrouter: 'https://openrouter.ai/api/v1'
};

// Legacy variables for backward compatibility
let aiTransMode = 'deepseek';
let modelName = 'deepseek-chat';
let configParams = {
  apiKey: '',
  basePath: 'https://api.deepseek.com/v1',
};

// Enhanced configuration function
export function configureTranslateService(config) {
  // Support both old and new API
  if (typeof config === 'string') {
    // Legacy API: configureTranslateService(apiKey, model)
    const apiKey = config;
    const model = arguments[1] || 'deepseek-chat';

    currentConfig.apiKey = apiKey;
    currentConfig.model = model;

    // Determine service from model
    if (model.includes('deepseek')) {
      currentConfig.service = 'deepseek';
      currentConfig.baseURL = SERVICE_ENDPOINTS.deepseek;
    } else if (model.includes('gemini')) {
      currentConfig.service = 'gemini';
      currentConfig.baseURL = SERVICE_ENDPOINTS.gemini;
    } else if (model.includes('gpt')) {
      currentConfig.service = 'openai';
      currentConfig.baseURL = SERVICE_ENDPOINTS.openai;
    } else if (model.includes('claude')) {
      currentConfig.service = 'claude';
      currentConfig.baseURL = SERVICE_ENDPOINTS.claude;
    } else if (model.includes('openrouter') || model.includes('/')) {
      // OpenRouter models often have format like "openai/gpt-4" or "anthropic/claude-3"
      currentConfig.service = 'openrouter';
      currentConfig.baseURL = SERVICE_ENDPOINTS.openrouter;
    }
  } else {
    // New API: configureTranslateService({ service, model, apiKey, baseURL })
    currentConfig = { ...currentConfig, ...config };

    // Auto-set baseURL if not provided
    if (!currentConfig.baseURL && SERVICE_ENDPOINTS[currentConfig.service]) {
      currentConfig.baseURL = SERVICE_ENDPOINTS[currentConfig.service];
    }
  }

  // Update legacy variables for backward compatibility
  configParams.apiKey = currentConfig.apiKey;
  configParams.basePath = currentConfig.baseURL;
  modelName = currentConfig.model;
  aiTransMode = currentConfig.service;

  // console.log(`Configured translate service:`, currentConfig);
}

// Function to auto-configure from store
export function configureFromStore(store) {
  const activeService = store.getActiveAiService();
  if (activeService && activeService.apiKey) {
    configureTranslateService({
      service: store.selectedAiService,
      model: store.selectedModel,
      apiKey: activeService.apiKey,
      baseURL: activeService.baseURL
    });
    return true;
  }
  return false;
}

// Get current configuration
export function getCurrentConfig() {
  return { ...currentConfig };
}

let genAI = null;
let geminiModel = null;

// Initialize Gemini model when needed
function initGeminiModel(apiKey) {
  if (!genAI && apiKey) {
    try {
      genAI = new GoogleGenerativeAI(apiKey);
      geminiModel = genAI.getGenerativeModel({ model: currentConfig.model });
      console.log('Gemini model initialized');
    } catch (error) {
      console.error('Error initializing Gemini model:', error);
    }
  }
  return geminiModel;
}

// Hàm trích xuất tên riêng và thuật ngữ quan trọng từ văn bản
async function extractImportantTerms(text) {
  console.log(`🔧 Extracting terms using service: ${currentConfig.service}`);

  const systemPrompt = `Bạn là chuyên gia phân tích văn bản. Hãy trích xuất tất cả tên nhân vật, địa điểm, và thuật ngữ quan trọng từ văn bản.
Lưu ý: Trích xuất đúng họ tên nhân vật địa điểm từ văn bản.
Hãy trả về kết quả dưới dạng JSON với định dạng sau:
{
  "characters": ["tên_nhân_vật_1", "tên_nhân_vật_2", ...],
  "locations": ["địa_điểm_1", "địa_điểm_2", ...],
  "terms": ["thuật_ngữ_1", "thuật_ngữ_2", ...]
}

Chỉ trả về JSON, không thêm bất kỳ giải thích nào.`;

  const inputText = text.substring(0, 10000); // Phân tích một phần đầu của văn bản

  try {
    let response;

    switch (currentConfig.service) {
      case 'openai':
      case 'deepseek':
      case 'claude':
      case 'openrouter':
        // OpenAI-compatible services
        const model = new ChatOpenAI({
          modelName: currentConfig.model,
          temperature: 0,
          openAIApiKey: currentConfig.apiKey,
          streaming: false,
          cache: true,
          configuration: {
            baseURL: currentConfig.baseURL,
          },
          maxTokens: -1,
          maxRetries: 3,
          tokenCountingFunction: () => ({ totalCount: 0, countsByFunction: {} }),
        });

        response = await model.invoke([
          new SystemMessage(systemPrompt),
          new HumanMessage(inputText)
        ]);
        break;

      case 'gemini':
        // Gemini service
        const geminiModel = initGeminiModel(currentConfig.apiKey);
        if (!geminiModel) {
          throw new Error('Failed to initialize Gemini model');
        }

        const geminiPrompt = `${systemPrompt}\n\n${inputText}`;
        const result = await geminiModel.generateContent(geminiPrompt);
        response = { content: result.response.text() };
        break;

      default:
        throw new Error(`Unsupported service for term extraction: ${currentConfig.service}`);
    }

    // Trích xuất phần JSON từ phản hồi
    const jsonMatch = response.content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const extractedTerms = JSON.parse(jsonMatch[0]);
      console.log(`📝 Extracted ${Object.values(extractedTerms).flat().length} terms using ${currentConfig.service}`);
      return extractedTerms;
    }
  } catch (error) {
    console.error(`Lỗi khi phân tích JSON với ${currentConfig.service}:`, error.message);
  }

  return { characters: [], locations: [], terms: [] };
}

// Hàm xây dựng từ điển thuật ngữ
export async function buildTerminologyDictionary(text, sourceLang) {
  console.log(`🔧 Building terminology dictionary with service: ${currentConfig.service}`);

  // Chỉ extract terms nếu được enable và service hỗ trợ
  const shouldExtractTerms = state.useTerm && currentConfig.service;
  const terms = shouldExtractTerms ? await extractImportantTerms(text) : { characters: [], locations: [], terms: [] };

  // Tạo danh sách tất cả các thuật ngữ cần dịch
  const allTerms = [...(terms.characters || []), ...(terms.locations || []), ...(terms.terms || [])].filter(
    (term) => term && term.trim().length > 0,
  );

  if (allTerms.length === 0) {
    console.log('Không tìm thấy thuật ngữ quan trọng.');
    return {};
  }

  console.log(`Đã tìm thấy ${allTerms.length} thuật ngữ quan trọng.`);

  // Nếu có quá nhiều thuật ngữ, chỉ lấy những thuật ngữ quan trọng nhất
  const termsToTranslate = allTerms.length > 50 ? allTerms.slice(0, 50) : allTerms;

  console.log('Đang xây dựng từ điển thuật ngữ...');
  const dictionary = {};

  // Dịch các thuật ngữ sử dụng service được chọn
  console.log(`🔧 Building terminology dictionary using service: ${currentConfig.service}`);

  const systemPrompt = `Bạn là chuyên gia dịch thuật. Hãy dịch các thuật ngữ sau từ ${sourceLang} sang tiếng Việt.

Hãy dịch sang tiếng Việt một cách thuần Việt và tự nhiên nhất có thể, và dịch đúng họ tên nhân vật địa điểm nếu có.

Trả về kết quả dưới dạng JSON với định dạng sau:
{
  "term1": "bản dịch1",
  "term2": "bản dịch2",
  ...
}

Chỉ trả về JSON, không thêm bất kỳ giải thích nào.`;

  const inputTerms = termsToTranslate.join('\n');

  let response;

  try {
    switch (currentConfig.service) {
      case 'openai':
      case 'deepseek':
      case 'claude':
      case 'openrouter':
        // OpenAI-compatible services
        const model = new ChatOpenAI({
          modelName: currentConfig.model,
          temperature: 0.3,
          openAIApiKey: currentConfig.apiKey,
          streaming: false,
          cache: true,
          configuration: {
            baseURL: currentConfig.baseURL,
          },
          maxTokens: -1,
          maxRetries: 3,
          tokenCountingFunction: () => ({ totalCount: 0, countsByFunction: {} }),
        });

        response = await model.invoke([
          new SystemMessage(systemPrompt),
          new HumanMessage(inputTerms)
        ]);
        break;

      case 'gemini':
        // Gemini service
        const geminiModel = initGeminiModel(currentConfig.apiKey);
        if (!geminiModel) {
          throw new Error('Failed to initialize Gemini model');
        }

        const geminiPrompt = `${systemPrompt}\n\n${inputTerms}`;
        const result = await geminiModel.generateContent(geminiPrompt);
        response = { content: result.response.text() };
        break;

      default:
        throw new Error(`Unsupported service for terminology dictionary: ${currentConfig.service}`);
    }
  } catch (error) {
    console.error(`Lỗi khi dịch thuật ngữ với ${currentConfig.service}:`, error.message);
    return {};
  }

  try {
    // Trích xuất phần JSON từ phản hồi
    const jsonMatch = response.content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const translatedTerms = JSON.parse(jsonMatch[0]);

      // Hiển thị từ điển thuật ngữ
      console.log(`📚 Terminology dictionary built using ${currentConfig.service}:`);
      for (const [term, translation] of Object.entries(translatedTerms)) {
        console.log(`  ${term} => ${translation}`);
        dictionary[term] = translation;
      }

      console.log(`✅ Built dictionary with ${Object.keys(dictionary).length} terms`);
      return dictionary;
    }
  } catch (error) {
    console.error(`Lỗi khi phân tích JSON từ điển với ${currentConfig.service}:`, error.message);
  }

  return {};
}




// Hàm dịch văn bản với streaming
export async function translateTextDeepseek(textList, existingTranslations = {}, startIndex = 0, terminologyDictionary = {}, targetLang = 'Vietnamese', maxRetries = 2) {
  const sourceLang = 'Chinese';

  // Lọc ra các dòng cần dịch (chưa có trong existingTranslations)
  const textsToTranslate = [];
  const indices = [];

  textList.forEach((text, i) => {
    const globalIndex = startIndex + i + 1;
    if (!existingTranslations[globalIndex]) {
      textsToTranslate.push(text);
      indices.push(globalIndex - 1); // Chỉ số trong mảng bắt đầu từ 0
    }
  });

  if (textsToTranslate.length === 0) {
    console.log('Tất cả các dòng trong nhóm này đã được dịch trước đó.');
    return textList.map((_, i) => {
      const globalIndex = startIndex + i + 1;
      return existingTranslations[globalIndex] || '';
    });
  }

  // Tạo prompt dịch thuật với từ điển thuật ngữ
  // const translationPrompt = createTranslationPrompt2(
  //   sourceLang,
  //   targetLang,
  //   terminologyDictionary
  // );
  const translationPrompt = getTranslationPrompt(sourceLang, targetLang, terminologyDictionary);

  const model = new ChatOpenAI({
    modelName: currentConfig.model,
    temperature: 0.3, // Giảm temperature để đảm bảo tính nhất quán
    openAIApiKey: currentConfig.apiKey,
    streaming: true,
    cache: true,
    configuration: {
      baseURL: currentConfig.baseURL,
    },
  });

  const formattedText = textsToTranslate.map((text, i) => `${indices[i] + 1}. ${text}`).join('\n');

  const prompt = `${translationPrompt}
${formattedText}`;

  console.log('Kết quả stream:');
  try {
    let chunkTranslation = '';
    // Sử dụng stream thay vì invoke
    const stream = await model.stream([new SystemMessage(translationPrompt), new HumanMessage(prompt)]);

    // Xử lý stream
    for await (const chunk of stream) {
      const token = chunk.content;
      state.contentStream += token;
      chunkTranslation += token;
    }

    const translatedLines = chunkTranslation.trim().split('\n');

    // Debug: Log AI response
    // console.log('🔍 DeepSeek Response Content:', chunkTranslation);
    // console.log('🔍 Translated Lines:', translatedLines);

    // Xử lý kết quả trả về với nhiều format khác nhau
    const translatedDict = { ...existingTranslations };

    for (let lineIndex = 0; lineIndex < translatedLines.length; lineIndex++) {
      const line = translatedLines[lineIndex].trim();
      if (!line) continue;

      // Thử parse theo format "1. translated text"
      let match = line.match(/^(\d+)\.\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        // console.log(`✅ Parsed line ${index}: "${translatedText}"`);
        continue;
      }

      // Thử parse theo format "1) translated text"
      match = line.match(/^(\d+)\)\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        // console.log(`✅ Parsed line ${index} (format 2): "${translatedText}"`);
        continue;
      }

      // Thử parse theo format "1: translated text"
      match = line.match(/^(\d+):\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        // console.log(`✅ Parsed line ${index} (format 3): "${translatedText}"`);
        continue;
      }

      // Nếu không có số thứ tự, thử map theo thứ tự dòng
      if (lineIndex < textsToTranslate.length) {
        const index = indices[lineIndex] + 1;
        translatedDict[index] = line;
        // console.log(`✅ Parsed line ${index} (sequential): "${line}"`);
        continue;
      }

      console.log(`❌ Failed to parse line: "${line}"`);
    }

    // console.log('🔍 Final translatedDict:', translatedDict);
    // Lưu tiến độ dịch
    // F.saveTranslationProgress(outputSrt, translatedDict);

    // Kiểm tra các dòng trùng lặp
    const duplicates = checkDuplicateLines(textList);
    if (duplicates.length > 0) {
      console.log(`\nPhát hiện ${duplicates.length} nhóm dòng trùng lặp trong nhóm này`);

      // Xử lý các dòng trùng lặp để đảm bảo dịch nhất quán
      duplicates.forEach((dup) => {
        // Lấy chỉ số toàn cục
        const globalIndices = dup.indices.map((idx) => startIndex + idx + 1);

        // Lấy bản dịch đầu tiên làm chuẩn
        const firstGlobalIndex = globalIndices[0];
        if (translatedDict[firstGlobalIndex]) {
          const standardTranslation = translatedDict[firstGlobalIndex];

          // Áp dụng cho tất cả các dòng trùng lặp
          globalIndices.forEach((idx) => {
            translatedDict[idx] = standardTranslation;
          });

          console.log(`Đã chuẩn hóa dịch cho ${globalIndices.length} dòng trùng lặp`);
        }
      });

      // Cập nhật lại tiến độ sau khi xử lý trùng lặp
      // F.saveTranslationProgress(outputSrt, translatedDict);
    }

    // Khớp nội dung dịch với thứ tự ban đầu
    const translatedTexts = textList.map((text, i) => {
      const globalIndex = startIndex + i + 1;
      const translated = translatedDict[globalIndex];

      if (translated) {
        return translated;
      }

      // Fallback: Nếu không tìm thấy bản dịch theo index, thử tìm theo thứ tự
      console.log(`⚠️ No translation found for index ${globalIndex}, trying fallback...`);

      // Nếu số lượng dòng dịch bằng số lượng dòng gốc, map theo thứ tự
      const translatedValues = Object.values(translatedDict).filter(v => v && v.trim());
      if (translatedValues.length === textList.length) {
        console.log(`📝 Using sequential mapping for index ${globalIndex}`);
        return translatedValues[i] || text;
      }

      // Nếu chỉ có 1 dòng dịch và 1 dòng gốc, sử dụng dòng dịch đó
      if (translatedValues.length === 1 && textList.length === 1) {
        console.log(`📝 Using single translation for index ${globalIndex}`);
        return translatedValues[0];
      }

      console.log(`❌ No suitable translation found for index ${globalIndex}, using original text`);
      return text;
    });

    console.log('\n'); // Xuống dòng sau khi stream kết thúc
    return translatedTexts;
  } catch (error) {
    console.error(`\nLỗi khi dịch: ${error}`);

    // Retry logic with limited attempts
    if (maxRetries > 0) {
      console.log(`Đang thử lại sau 3 giây... (còn ${maxRetries} lần thử)`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      return await translateTextDeepseek(textList, existingTranslations, startIndex, terminologyDictionary, targetLang, maxRetries - 1);
    } else {
      // No more retries, throw error
      throw new Error(`DeepSeek translation failed after all retries: ${error.message || error}`);
    }
  }
}

function checkDuplicateLines(textList) {
  const duplicates = [];
  const uniqueTexts = new Set();
  const duplicateIndices = {};

  textList.forEach((text, index) => {
    if (uniqueTexts.has(text)) {
      if (!duplicateIndices[text]) {
        duplicateIndices[text] = [];
      }
      duplicateIndices[text].push(index);
    } else {
      uniqueTexts.add(text);
    }
  });

  // Tìm các dòng trùng lặp liên tiếp
  for (const text in duplicateIndices) {
    const indices = duplicateIndices[text];
    if (indices.length > 0) {
      let consecutiveGroup = [indices[0]];

      for (let i = 1; i < indices.length; i++) {
        if (indices[i] === indices[i - 1] + 1) {
          consecutiveGroup.push(indices[i]);
        } else {
          if (consecutiveGroup.length > 2) {
            duplicates.push({
              text,
              indices: [...consecutiveGroup],
            });
          }
          consecutiveGroup = [indices[i]];
        }
      }

      if (consecutiveGroup.length > 2) {
        duplicates.push({
          text,
          indices: [...consecutiveGroup],
        });
      }
    }
  }

  return duplicates;
}

// OpenAI-compatible translation function
export async function translateTextOpenAI(textList, existingTranslations = {}, startIndex = 0, terminologyDictionary = {}, targetLang = 'Vietnamese', maxRetries = 2) {
  const sourceLang = 'Chinese';

  // Lọc ra các dòng cần dịch (chưa có trong existingTranslations)
  const textsToTranslate = [];
  const indices = [];

  textList.forEach((text, i) => {
    const globalIndex = startIndex + i + 1;
    if (!existingTranslations[globalIndex]) {
      textsToTranslate.push(text);
      indices.push(globalIndex - 1); // Chỉ số trong mảng bắt đầu từ 0
    }
  });

  if (textsToTranslate.length === 0) {
    console.log('Tất cả các dòng trong nhóm này đã được dịch trước đó.');
    return textList.map((_, i) => {
      const globalIndex = startIndex + i + 1;
      return existingTranslations[globalIndex] || '';
    });
  }

  // Tạo prompt dịch thuật với từ điển thuật ngữ
  const translationPrompt = getTranslationPrompt(sourceLang, targetLang, terminologyDictionary);

  const model = new ChatOpenAI({
    modelName: currentConfig.model,
    temperature: 0.3,
    openAIApiKey: currentConfig.apiKey,
    streaming: false,
    cache: true,
    configuration: {
      baseURL: currentConfig.baseURL,
    },
    maxTokens: -1,
    maxRetries: 3,
    tokenCountingFunction: () => ({ totalCount: 0, countsByFunction: {} }),
  });

  const formattedText = textsToTranslate.map((text, i) => `${indices[i] + 1}. ${text}`).join('\n');

  try {
    const response = await model.invoke([
      new SystemMessage(translationPrompt),
      new HumanMessage(formattedText)
    ]);

    const translatedLines = response.content.trim().split('\n');

    // Debug: Log AI response
    console.log('🔍 AI Response Content:', response.content);
    console.log('🔍 Translated Lines:', translatedLines);

    // Xử lý kết quả trả về với nhiều format khác nhau
    const translatedDict = { ...existingTranslations };

    for (let lineIndex = 0; lineIndex < translatedLines.length; lineIndex++) {
      const line = translatedLines[lineIndex].trim();
      if (!line) continue;

      // Thử parse theo format "1. translated text"
      let match = line.match(/^(\d+)\.\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        console.log(`✅ Parsed line ${index}: "${translatedText}"`);
        continue;
      }

      // Thử parse theo format "1) translated text"
      match = line.match(/^(\d+)\)\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        console.log(`✅ Parsed line ${index} (format 2): "${translatedText}"`);
        continue;
      }

      // Thử parse theo format "1: translated text"
      match = line.match(/^(\d+):\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        console.log(`✅ Parsed line ${index} (format 3): "${translatedText}"`);
        continue;
      }

      // Nếu không có số thứ tự, thử map theo thứ tự dòng
      if (lineIndex < textsToTranslate.length) {
        const index = indices[lineIndex] + 1;
        translatedDict[index] = line;
        console.log(`✅ Parsed line ${index} (sequential): "${line}"`);
        continue;
      }

      console.log(`❌ Failed to parse line: "${line}"`);
    }

    console.log('🔍 Final translatedDict:', translatedDict);

    // Kiểm tra các dòng trùng lặp
    const duplicates = checkDuplicateLines(textList);
    if (duplicates.length > 0) {
      console.log(`\nPhát hiện ${duplicates.length} nhóm dòng trùng lặp trong nhóm này`);

      duplicates.forEach((dup) => {
        const globalIndices = dup.indices.map((idx) => startIndex + idx + 1);
        const firstGlobalIndex = globalIndices[0];
        if (translatedDict[firstGlobalIndex]) {
          const standardTranslation = translatedDict[firstGlobalIndex];
          globalIndices.forEach((idx) => {
            translatedDict[idx] = standardTranslation;
          });
          console.log(`Đã chuẩn hóa dịch cho ${globalIndices.length} dòng trùng lặp`);
        }
      });
    }

    // Khớp nội dung dịch với thứ tự ban đầu
    const translatedTexts = textList.map((text, i) => {
      const globalIndex = startIndex + i + 1;
      const translated = translatedDict[globalIndex];

      if (translated) {
        return translated;
      }

      // Fallback: Nếu không tìm thấy bản dịch theo index, thử tìm theo thứ tự
      console.log(`⚠️ No translation found for index ${globalIndex}, trying fallback...`);

      // Nếu số lượng dòng dịch bằng số lượng dòng gốc, map theo thứ tự
      const translatedValues = Object.values(translatedDict).filter(v => v && v.trim());
      if (translatedValues.length === textList.length) {
        console.log(`📝 Using sequential mapping for index ${globalIndex}`);
        return translatedValues[i] || text;
      }

      // Nếu chỉ có 1 dòng dịch và 1 dòng gốc, sử dụng dòng dịch đó
      if (translatedValues.length === 1 && textList.length === 1) {
        console.log(`📝 Using single translation for index ${globalIndex}`);
        return translatedValues[0];
      }

      console.log(`❌ No suitable translation found for index ${globalIndex}, using original text`);
      return text;
    });

    return translatedTexts;
  } catch (error) {
    console.error(`\nLỗi khi dịch OpenAI: ${error}`);

    if (maxRetries > 0) {
      console.log(`Đang thử lại sau 3 giây... (còn ${maxRetries} lần thử)`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      return await translateTextOpenAI(textList, existingTranslations, startIndex, terminologyDictionary, targetLang, maxRetries - 1);
    } else {
      throw new Error(`OpenAI translation failed after all retries: ${error.message || error}`);
    }
  }
}

// Gemini translation function
export async function translateTextGemini(textList, existingTranslations = {}, startIndex = 0, terminologyDictionary = {}, targetLang = 'Vietnamese', maxRetries = 2) {
  if (!textList || textList.length === 0) {
    return textList;
  }

  // Kiểm tra các dòng trùng lặp
  const duplicates = checkDuplicateLines(textList);
  if (duplicates.length > 0) {
    console.log(`Phát hiện ${duplicates.length} nhóm dòng trùng lặp`);
  }

  // Lọc ra các dòng cần dịch (chưa có trong existingTranslations)
  const textsToTranslate = [];
  const indices = [];

  textList.forEach((text, i) => {
    const globalIndex = startIndex + i + 1;
    if (!existingTranslations[globalIndex]) {
      textsToTranslate.push(text);
      indices.push(i);
    }
  });

  if (textsToTranslate.length === 0) {
    console.log('Tất cả các dòng đã được dịch trước đó.');
    return textList.map((_, i) => {
      const globalIndex = startIndex + i + 1;
      return existingTranslations[globalIndex] || '';
    });
  }

  console.log(`Cần dịch ${textsToTranslate.length} dòng mới`);

  // Tạo prompt dịch thuật với từ điển thuật ngữ
  const translationPrompt = getTranslationPrompt('Chinese', targetLang, terminologyDictionary);
  const formattedText = textsToTranslate.map((text, i) => `${indices[i] + 1}. ${text}`).join('\n');

  const prompt = `${translationPrompt}
${formattedText}`;

  try {
    // Initialize Gemini model if needed
    const model = initGeminiModel(currentConfig.apiKey);
    if (!model) {
      throw new Error('Failed to initialize Gemini model');
    }

    const result = await model.generateContent(prompt);
    const response = result.response;
    const translatedText = response.text();
    const translatedLines = translatedText.trim().split('\n');

    // Debug: Log AI response
    console.log('🔍 Gemini Response Content:', translatedText);
    console.log('🔍 Translated Lines:', translatedLines);

    // Xử lý kết quả trả về với nhiều format khác nhau
    const translatedDict = { ...existingTranslations };

    for (let lineIndex = 0; lineIndex < translatedLines.length; lineIndex++) {
      const line = translatedLines[lineIndex].trim();
      if (!line) continue;

      // Thử parse theo format "1. translated text"
      let match = line.match(/^(\d+)\.\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        console.log(`✅ Parsed line ${index}: "${translatedText}"`);
        continue;
      }

      // Thử parse theo format "1) translated text"
      match = line.match(/^(\d+)\)\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        console.log(`✅ Parsed line ${index} (format 2): "${translatedText}"`);
        continue;
      }

      // Thử parse theo format "1: translated text"
      match = line.match(/^(\d+):\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
        console.log(`✅ Parsed line ${index} (format 3): "${translatedText}"`);
        continue;
      }

      // Nếu không có số thứ tự, thử map theo thứ tự dòng
      if (lineIndex < textsToTranslate.length) {
        const index = indices[lineIndex] + 1;
        translatedDict[index] = line;
        console.log(`✅ Parsed line ${index} (sequential): "${line}"`);
        continue;
      }

      console.log(`❌ Failed to parse line: "${line}"`);
    }

    console.log('🔍 Final translatedDict:', translatedDict);

    // Khớp nội dung dịch với thứ tự ban đầu
    const translatedTexts = textList.map((text, i) => {
      const globalIndex = startIndex + i + 1;
      const translated = translatedDict[globalIndex];

      if (translated) {
        return translated;
      }

      // Fallback: Nếu không tìm thấy bản dịch theo index, thử tìm theo thứ tự
      console.log(`⚠️ No translation found for index ${globalIndex}, trying fallback...`);

      // Nếu số lượng dòng dịch bằng số lượng dòng gốc, map theo thứ tự
      const translatedValues = Object.values(translatedDict).filter(v => v && v.trim());
      if (translatedValues.length === textList.length) {
        console.log(`📝 Using sequential mapping for index ${globalIndex}`);
        return translatedValues[i] || text;
      }

      // Nếu chỉ có 1 dòng dịch và 1 dòng gốc, sử dụng dòng dịch đó
      if (translatedValues.length === 1 && textList.length === 1) {
        console.log(`📝 Using single translation for index ${globalIndex}`);
        return translatedValues[0];
      }

      console.log(`❌ No suitable translation found for index ${globalIndex}, using original text`);
      return text;
    });

    // Xử lý các dòng trùng lặp - đảm bảo dịch nhất quán
    duplicates.forEach((dup) => {
      const globalIndices = dup.indices.map((idx) => startIndex + idx + 1);
      const firstGlobalIndex = globalIndices[0];
      if (translatedDict[firstGlobalIndex]) {
        const standardTranslation = translatedDict[firstGlobalIndex];
        globalIndices.forEach((idx) => {
          translatedDict[idx] = standardTranslation;
        });
        console.log(`Đã chuẩn hóa dịch cho ${globalIndices.length} dòng trùng lặp`);
      }
    });

    return translatedTexts;
  } catch (error) {
    console.error(`\nLỗi khi dịch Gemini: ${error}`);

    if (maxRetries > 0) {
      console.log(`Đang thử lại sau 3 giây... (còn ${maxRetries} lần thử)`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      return await translateTextGemini(textList, existingTranslations, startIndex, terminologyDictionary, targetLang, maxRetries - 1);
    } else {
      throw new Error(`Gemini translation failed after all retries: ${error.message || error}`);
    }
  }
}

// Legacy function for backward compatibility
export async function batchTranslateText(textList, existingTranslations = {}, terminologyDictionary = {}, targetLang = 'Vietnamese') {
  // Route to appropriate service based on current config
  switch (currentConfig.service) {
    case 'deepseek':
      return await translateTextDeepseek(textList, existingTranslations, 0, terminologyDictionary, targetLang);
    case 'openai':
      return await translateTextOpenAI(textList, existingTranslations, 0, terminologyDictionary, targetLang);
    case 'gemini':
      return await translateTextGemini(textList, existingTranslations, 0, terminologyDictionary, targetLang);
    case 'claude':
    case 'openrouter':
      // Claude and OpenRouter use OpenAI-compatible API
      return await translateTextOpenAI(textList, existingTranslations, 0, terminologyDictionary, targetLang);
    default:
      throw new Error(`Unsupported service: ${currentConfig.service}`);
  }
}

export async function translateSrtService({ subs, batchSize = 50, onProgress = null, terminologyDictionary, targetLanguage = 'Vietnamese',callback }) {
  /** Dịch file SRT bằng cách gửi toàn bộ nội dung lên API một lần */
  try {
    // Reset stop flag at the beginning
    shouldStopTranslation = false;

    console.log(`Đã phân tích ${subs.length} phụ đề từ file SRT.`);

    // Đọc tiến độ dịch đã lưu (nếu có)
    const existingTranslations = {};

    // Chia nhỏ danh sách phụ đề thành các nhóm để tránh quá tải API
    const batches = [];

    for (let i = 0; i < subs.length; i += batchSize) {
      batches.push(subs.slice(i, i + batchSize));
    }

    // Cập nhật tiến độ - bắt đầu xây dựng từ điển thuật ngữ
    if (onProgress) onProgress(0, batches.length);

    console.log('Bắt đầu xây dựng từ điển thuật ngữ...');

    // Lấy mẫu văn bản để xây dựng từ điển thuật ngữ
    // const Texts = subs.slice(0, 100).map(sub => sub.text);
    // const sampleText = Texts.join('\n\n');

    // terminologyDictionary = await buildTerminologyDictionary(
    //   sampleText,
    //   'Chinese'
    // );

    console.log(`Chia thành ${batches.length} nhóm để dịch.`);

    // Cập nhật tiến độ - đã xây dựng từ điển thuật ngữ
    if (onProgress) onProgress(1, batches.length);

    // Dịch từng nhóm phụ đề
    let allTranslatedTexts = [];
    for (let i = 0; i < batches.length; i++) {
      // Check if translation should be stopped
      if (shouldStopTranslation) {
        console.log('Translation stopped by user');
        throw new Error('Translation stopped by user');
      }

      console.log(`Đang dịch nhóm ${i + 1}/${batches.length}...`);
      // showProgressBar(i+1, batches.length);

      // Cập nhật tiến độ - truyền số items đã xử lý thay vì số batch
      const processedItems = (i + 1) * batchSize;
      const totalItems = subs.length;
      if (onProgress) onProgress(Math.min(processedItems, totalItems), totalItems);

      // Lấy các văn bản cần dịch trong nhóm này
      const batchTexts = batches[i].map((sub) => sub.text);

      // Tính chỉ số bắt đầu của nhóm này trong danh sách đầy đủ
      const startIndex = i * batchSize;

      // Lọc các bản dịch đã có cho nhóm này
      const batchExistingTranslations = {};
      for (let j = 0; j < batchTexts.length; j++) {
        const globalIndex = startIndex + j + 1;
        if (existingTranslations[globalIndex]) {
          batchExistingTranslations[globalIndex] = existingTranslations[globalIndex];
        }
      }
      console.log(batchTexts);
      console.log(existingTranslations);
      console.log(startIndex);
      console.log(terminologyDictionary);

      try {
        // Debug: Log current service configuration
        console.log(`🔧 Using translation service: ${currentConfig.service} with model: ${currentConfig.model}`);
        console.log(`🔧 API endpoint: ${currentConfig.baseURL}`);

        // Dịch nhóm văn bản - route to appropriate service
        let translatedBatch;
        switch (currentConfig.service) {
          case 'deepseek':
            console.log(`📝 Translating with DeepSeek...`);
            translatedBatch = await translateTextDeepseek(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
            break;
          case 'openai':
            console.log(`📝 Translating with OpenAI...`);
            translatedBatch = await translateTextOpenAI(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
            break;
          case 'gemini':
            console.log(`📝 Translating with Gemini...`);
            translatedBatch = await translateTextGemini(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
            break;
          case 'claude':
            console.log(`📝 Translating with Claude (OpenAI-compatible)...`);
            translatedBatch = await translateTextOpenAI(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
            break;
          case 'openrouter':
            console.log(`📝 Translating with OpenRouter...`);
            translatedBatch = await translateTextOpenAI(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
            break;
          case 'nebula':
            console.log(`📝 Translating with OpenRouter...`);
            translatedBatch = await translateTextDeepseek(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
            break;
          default:
            throw new Error(`Unsupported service: ${currentConfig.service}`);
        }

        allTranslatedTexts = [...allTranslatedTexts, ...translatedBatch];
        // Call callback with translated batch and start index for proper mapping
        if (callback) {
          console.log(`📞 Calling callback with ${translatedBatch.length} items at startIndex ${startIndex}`);
          callback(translatedBatch, startIndex);
        }
      } catch (batchError) {
        console.error(`Lỗi khi dịch batch ${i + 1}:`, batchError);

        // Try to retry with smaller batch size or different approach
        let retrySuccess = false;

        try {
          console.log(`Thử lại batch ${i + 1} với batch size nhỏ hơn...`);

          // Split the failed batch into smaller chunks
          const smallerBatchSize = Math.max(1, Math.floor(batchTexts.length / 2));
          const smallerBatches = [];

          for (let k = 0; k < batchTexts.length; k += smallerBatchSize) {
            smallerBatches.push(batchTexts.slice(k, k + smallerBatchSize));
          }

          let retryTranslatedTexts = [];
          for (const smallBatch of smallerBatches) {
            let retryBatch;
            switch (currentConfig.service) {
              case 'deepseek':
                retryBatch = await translateTextDeepseek(smallBatch, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
                break;
              case 'openai':
                retryBatch = await translateTextOpenAI(smallBatch, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
                break;
              case 'gemini':
                retryBatch = await translateTextGemini(smallBatch, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
                break;
              case 'claude':
                retryBatch = await translateTextOpenAI(smallBatch, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
                break;
              case 'openrouter':
                retryBatch = await translateTextOpenAI(smallBatch, existingTranslations, startIndex, terminologyDictionary, targetLanguage);
                break;
              default:
                throw new Error(`Unsupported service: ${currentConfig.service}`);
            }
            retryTranslatedTexts = [...retryTranslatedTexts, ...retryBatch];
          }

          allTranslatedTexts = [...allTranslatedTexts, ...retryTranslatedTexts];
          // Call callback with retry results and start index
          if (callback) {
            console.log(`📞 Calling retry callback with ${retryTranslatedTexts.length} items at startIndex ${startIndex}`);
            callback(retryTranslatedTexts, startIndex);
          }
          retrySuccess = true;
          console.log(`Retry batch ${i + 1} thành công!`);

        } catch (retryError) {
          console.error(`Retry batch ${i + 1} cũng thất bại:`, retryError);
        }

        if (!retrySuccess) {
          // If retry also fails, stop the entire process
          throw new Error(`Batch ${i + 1} translation failed after retry: ${batchError.message || batchError}`);
        }
      }
    }

    // Cập nhật tiến độ - hoàn thành
    if (onProgress) onProgress(subs.length, subs.length);

    console.log('Dịch hoàn tất!');
    // showProgressBar(batches.length, batches.length);

    return allTranslatedTexts;
  } catch (e) {
    console.error(`Error in translateSrt: ${e}`);
    throw e;
  }
}

/**
 * Stop the current translation process
 */
export function stopTranslation() {
  console.log('Stopping translation...');
  shouldStopTranslation = true;
  return true;
}
