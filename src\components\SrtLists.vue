<template>
  <div>
    <a-button type="primary" @click="showModal" :disabled="isLoading">
      {{ buttonText }}
    </a-button>

    <a-modal v-model:open="visible" title="Select SRT File" :width="900" :footer="null" @cancel="handleCancel">
      <div class="srt-list-container">
        <!-- Empty state -->
        <div v-if="srtLists.length === 0" class="empty-state">
          <div class="text-center py-8 text-gray-500">
            <p class="mb-4">No SRT files have been imported yet.</p>
            <a-button @click="handleImportNew">Import New SRT File</a-button>
          </div>
        </div>

        <!-- SRT List -->
        <div v-else>
          <div class="mb-4 flex justify-between items-center">
            <h3 class="text-lg font-medium">Previously Imported SRT Files</h3>
            <a-button type="primary" @click="handleImportNew">Import New</a-button>
          </div>

          <a-table :dataSource="srtLists" :columns="columns" :pagination="{ pageSize: 10 }" :loading="isLoading"
            rowKey="name">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="flex gap-1">
                  <a-image v-if="record.thumb" style="height: 50px; width: 50px; object-fit: cover;" :src="record.thumb"
                    :alt="record.name" />
                  {{ formatSrtName(record.name).slice(0, 30) }}...
                </div>
              </template>
              <template v-if="column.key === 'count'">
                {{ record.items.length }} items
              </template>
              <template v-if="column.key === 'duration'">
                {{ formatTime(record.info.duration, record.info.fps) }}
              </template>
              <template v-if="column.key === 'date'">
                {{ formatDate(record.name) }}
              </template>
              <template v-if="column.key === 'action'">
                <div class="flex gap-2">
                  <a-tag :style="{ margin: '0' }" color="green" v-if="record.isRender">OK</a-tag>
                  <!-- <a-tag :style="{ margin: '0' }" color="purple">protocol</a-tag> -->
                  <!-- <a-tag :style="{ margin: '0' }"  color="blue">TLS</a-tag> -->
                  <!-- <a-switch size="small" v-model="record.enable" @change="switchEnable(dbInbound.id,dbInbound.enable)"></a-switch> -->
                  <!-- <svg height="10px" width="14px" viewBox="0 0 640 512" fill="currentColor">
                    <path d="M484.4 96C407 96 349.2 164.1 320 208.5C290.8 164.1 233 96 155.6 96C69.75 96 0 167.8 0 256s69.75 160 155.6 160C233.1 416 290.8 347.9 320 303.5C349.2 347.9 407 416 484.4 416C570.3 416 640 344.2 640 256S570.3 96 484.4 96zM155.6 368C96.25 368 48 317.8 48 256s48.25-112 107.6-112c67.75 0 120.5 82.25 137.1 112C276 285.8 223.4 368 155.6 368zM484.4 368c-67.75 0-120.5-82.25-137.1-112C364 226.2 416.6 144 484.4 144C543.8 144 592 194.2 592 256S543.8 368 484.4 368z" fill="currentColor"></path>
                  </svg> -->
                  <a-button type="primary" size="small" @click="selectSrt(record)">
                    Select
                  </a-button>
                  <a-button type="default" size="small" @click="editPath(record)">
                    Edit path
                  </a-button>
                  <a-button danger size="small" @click="deleteSrt(record)">
                    Delete
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
    <!-- edit path modal -->
    <a-modal v-model:open="showEditPath" title="Edit SRT Path" :width="500" @cancel="showEditPath = false"
      @ok="updateSrtPath">
      <div>
        <label for="srt-path">SRT Path:</label>
        <a-input id="srt-path" v-model:value="editPathValue.path" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useTTSStore } from '../stores/ttsStore';
import { message, Modal } from 'ant-design-vue';
import {formatTime} from '@/lib/utils'

const props = defineProps({
  buttonText: {
    type: String,
    default: 'Select SRT File'
  }
});

const emit = defineEmits(['select', 'import-new']);

const ttsStore = useTTSStore();
const visible = ref(false);
const isLoading = ref(false);

const showEditPath = ref(false);
const editPathValue = ref({});
const editPath = (record) => {
  showEditPath.value = true;
  editPathValue.value = { ...record };
};

const updateSrtPath = () => {
  const index = ttsStore.srtLists.findIndex(item => item.name === editPathValue.value.name);
  if (index !== -1) {
    ttsStore.srtLists[index] = { ...editPathValue.value };
    message.success('SRT path updated successfully');
  }
  showEditPath.value = false;
};

// Get SRT lists from store
const srtLists = computed(() => {
  return ttsStore.srtLists.slice().reverse() || [];
});

// Table columns
const columns = [
  {
    title: 'File Name',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Items',
    key: 'count',
  },{
    title: 'Duration',
    key: 'duration',
  },
  {
    title: 'Import Date',
    key: 'date',
  },
  {
    title: 'Action',
    key: 'action',
  }
];

// Format SRT name (remove timestamp)
function formatSrtName(name) {
  // Extract the file name part before the timestamp
  const parts = name.split('_');
  if (parts.length > 1) {
    // Remove the last part (timestamp)
    parts.pop();
    return parts.join('_');
  }
  return name;
}

// Format date from timestamp in filename
function formatDate(name) {
  try {
    // Extract timestamp from the end of the name
    const timestamp = name.split('_').pop();
    if (timestamp && !isNaN(Number(timestamp))) {
      const date = new Date(Number(timestamp));
      return date.toLocaleString();
    }
    return 'Unknown date';
  } catch (error) {
    return 'Unknown date';
  }
}

// Show modal
function showModal() {
  visible.value = true;
}

// Handle cancel
function handleCancel() {
  visible.value = false;
}

// Handle import new
function handleImportNew() {
  visible.value = false;
  emit('import-new');
}

// Select SRT
async function selectSrt(record) {
  // Initialize layers array if not present
  if (!record.layers) {
    record.layers = [];
  }

  ttsStore.currentSrtList = record;
  emit('select', record);
  await electronAPI.setCurrentDir(record.path)
  visible.value = false;
  message.success(`Selected SRT file: ${formatSrtName(record.name).slice(0, 30)}`);
}

// Delete SRT
async function deleteSrt(record) {
  const confirm = await new Promise((resolve) => {
    Modal.confirm({
      title: 'Xác nhận',
      content: 'Bạn có chắc chắn muốn xóa subtitle này?',
      okText: 'Có',
      cancelText: 'Không',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });
  if(confirm) {
    const index = ttsStore.srtLists.findIndex(item => item.name === record.name);
    if (index !== -1) {
      ttsStore.srtLists.splice(index, 1);
      message.success(`Deleted SRT file: ${formatSrtName(record.name)}`);
    }
  }

}
</script>

<style scoped>
.srt-list-container {
  max-height: 800px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}
</style>
