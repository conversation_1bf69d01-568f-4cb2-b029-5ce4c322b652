const crypto = require('crypto');
const WebSocket = require('ws');
const axios = require('axios');

function camelCase(str) {
    return str
        .toLowerCase()
        .replace(/[-_.](.)/g, (_, c) => c.toUpperCase())
        .replace(/^(.)/, (_, c) => c.toLowerCase());
}

function camelCaseKeys(input, options = { deep: false }) {
    if (Array.isArray(input)) {
        return input.map((item) => camelCaseKeys(item, options));
    }

    if (input === null || typeof input !== 'object') {
        return input;
    }

    const result = {};

    for (const [key, value] of Object.entries(input)) {
        const camelKey = camelCase(key);

        if (options.deep && value !== null && typeof value === 'object') {
            result[camelKey] = camelCaseKeys(value, options);
        } else {
            result[camelKey] = value;
        }
    }

    return result;
}

const API_URL = 'https://accounts.vbee.vn';
const TTS_SYNTHESIS_URL = 'wss://vbee.vn/api/v1/synthesis';

const WS_TYPE = {
    INIT: 'INIT',
    SYNTHESIS: 'SYNTHESIS',
    PING: 'PING',
};

const REQUEST_STATUS = {
    IN_PROGRESS: 'IN_PROGRESS',
    SUCCESS: 'SUCCESS',
    FAILURE: 'FAILURE',
};

const PING_INTERVAL = 10 * 1000;

const task_log_flag = 'tts_task_log_flag';

const axiosClient = axios.create({
    baseURL: `${API_URL}/api/v1`,
    responseType: 'json',
    timeout: 60 * 1000,
});
axiosClient.interceptors.request.use(
    (config) => config,
    (error) => Promise.reject(error),
);
axiosClient.interceptors.response.use(
    (response) => camelCaseKeys(response.data, { deep: true }),
    (error) => Promise.reject(error),
);

function replay_msg(event, text) {
    let tmp = {
        data: null,
        msg: text,
    };
    if (!event) console.log('useWebSocketSynthesis', text);
    if (event) event.reply?.(task_log_flag, tmp);
}

const ws = {};
const requestMap = new Map();
let access_token = null;
function useWebSocketSynthesis(accessToken) {
    access_token = accessToken;
    let event = null;
    let isConnected = false;
    const setEvent = (evt) => {
        event = evt;
    };
    const startWs = async () => {
        if (ws.current) return;
        ws.current = new WebSocket(TTS_SYNTHESIS_URL);

        ws.current.onopen = () => {
            replay_msg(event, '🔗 WebSocket connected');
            isConnected = true;
            ws.current.send(
                JSON.stringify({
                    type: WS_TYPE.INIT,
                    accessToken,
                }),
            );

            ws.current.pingInterval = setInterval(() => {
                ws.current.send(JSON.stringify({ type: WS_TYPE.PING }));
            }, PING_INTERVAL);
        };

        ws.current.onmessage = (res) => {
            const responseData = camelCaseKeys(JSON.parse(res.data), { deep: true });
            const { type, result, status } = responseData;

            if (status === 0) replay_msg(event, '⚠️ Error in: ' + JSON.stringify(responseData));

            if (type === WS_TYPE.SYNTHESIS) {
                const { requestId } = result || {};
                if (!requestId) return;

                // Nếu trạng thái là IN_PROGRESS, cập nhật requestId
                if (result.status === REQUEST_STATUS.IN_PROGRESS) {
                    for (const [tempId, { resolve, reject }] of requestMap.entries()) {
                        requestMap.set(requestId, { resolve, reject });
                        requestMap.delete(tempId);
                        replay_msg(event, `⏳ Request ${requestId} in progress...`);
                        break;
                    }
                    return;
                }

                if (!requestMap.has(requestId)) return;

                // Xử lý kết quả thành công hoặc thất bại
                switch (result?.status) {
                    case REQUEST_STATUS.SUCCESS:
                        replay_msg(event, '✅ SUCCESS', result);
                        requestMap.get(requestId)?.resolve(result);
                        requestMap.delete(requestId);
                        break;

                    case REQUEST_STATUS.FAILURE:
                        replay_msg(event, `❌ Request ${requestId} failed`);
                        requestMap.get(requestId)?.reject(new Error('Synthesis failed'));
                        requestMap.delete(requestId);
                        break;

                    default:
                        break;
                }
            }
        };

        ws.current.onclose = (event) => {
            replay_msg(event, '🔌 WebSocket closed', event);
            isConnected = false;
            clearInterval(ws.current?.pingInterval);
            ws.current = null;
        };

        ws.current.onerror = (err) => {
            replay_msg(event, '🚨 WebSocket error:', err);
        };

        return new Promise((resolve) => setTimeout(resolve, 1000));
    };

    const stopWs = () => {
        if (ws.current) {
            clearInterval(ws.current.pingInterval);
            ws.current.close();
            ws.current = null;
            replay_msg(event, '🛑 WebSocket stopped');
        }
    };

    const requestSynthesis = async (previewPayload) => {
        // replay_msg(event,`isToken ${!!accessToken}, ${JSON.stringify(S.VBWS.result)}`);
        return new Promise((resolve, reject) => {
            if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
                return reject(new Error('WebSocket is not connected'));
            }

            const tempId = crypto.randomUUID();
            requestMap.set(tempId, { resolve, reject });
            const payload = {
                type: WS_TYPE.SYNTHESIS,
                payload: previewPayload,
                accessToken: access_token,
            };
            ws.current.send(JSON.stringify(payload));

            setTimeout(() => {
                if (requestMap.has(tempId)) {
                    requestMap.get(tempId)?.reject(new Error('Timeout waiting for requestId'));
                    requestMap.delete(tempId);
                }
            }, 30000);
        });
    };

    return { isConnected, startWs, stopWs, requestSynthesis, setEvent };
}
const requestSynthesis = async (previewPayload) => {
    // replay_msg(event,`isToken ${!!accessToken}, ${JSON.stringify(S.VBWS.result)}`);
    return new Promise(async (resolve, reject) => {
        if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
            await F.initSocket()
            await F.sleep(3000)
        }

        const tempId = crypto.randomUUID();
        requestMap.set(tempId, { resolve, reject });
        const payload = {
            type: WS_TYPE.SYNTHESIS,
            payload: previewPayload,
            accessToken: access_token,
        };
        ws.current.send(JSON.stringify(payload));

        setTimeout(() => {
            if (requestMap.has(tempId)) {
                requestMap.get(tempId)?.reject(new Error('Timeout waiting for requestId'));
                requestMap.delete(tempId);
            }
        }, 45000);
    });
};
async function getTokenVbee(refresh_token) {
    const res = await axiosClient({
        method: 'POST',
        url: 'auth/refresh-token',
        headers: {
            'Content-Type': 'application/json',
            Origin: 'https://studio.vbee.vn',
            Referer: 'https://studio.vbee.vn/',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
            // Authorization: 'Bearer ',
            'Content-Type': 'application/json',
            Cookie: `aivoice_refresh_token=${refresh_token}`,
        },
        data: {
            clientId: 'aivoice-web-application',
        },
    });
    return res;
}

module.exports = {
    useWebSocketSynthesis,
    getTokenVbee,
    requestSynthesis,
};
