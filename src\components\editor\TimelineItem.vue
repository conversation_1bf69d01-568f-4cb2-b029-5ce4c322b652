<template>
  <div
    class="timeline-item absolute cursor-pointer select-none"
    :class="{
      'selected': isSelected,
      'dragging': isDragging,
      'resizing': isResizing,
      'hover': isHovered,
      'playing': isPlayingAudio,
      'synchronized': isPlayingAudio && state.isSynchronizedPlayback
    }"
    :style="itemStyle"
    @mousedown="handleMouseDown"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @contextmenu="handleContextMenu"
  >
    <!-- Left resize handle -->
    <div
      class="resize-handle resize-left absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-blue-500"
      @mousedown.stop="handleResizeStart('left', $event)"
    ></div>

    <!-- Audio waveform background -->
    <div v-if="hasAudio && showWaveform" class="absolute inset-0 opacity-30">
      <canvas
        ref="waveformCanvas"
        class="w-full h-full"
        @click.stop="handleWaveformClick"
      ></canvas>
    </div>

    <!-- Item content -->
    <div class="item-content h-full px-2 py-1 overflow-hidden relative z-10">
      <!-- Item header -->
      <div class="flex items-center justify-between text-xs mb-1">
        <div class="flex items-center gap-2">
          <span class="font-medium text-white">{{ item.id }}</span>
          <!-- Audio playback controls -->
          <div v-if="hasAudio" class="flex items-center gap-1">
            <button
              @click.stop="toggleAudioPlayback"
              class="audio-play-btn w-4 h-4 flex items-center justify-center rounded-full bg-blue-600 hover:bg-blue-500 transition-colors"
              :title="getPlayButtonTitle()"
            >
              <svg v-if="!isPlayingAudio" width="8" height="8" viewBox="0 0 24 24" fill="white">
                <polygon points="5,3 19,12 5,21"/>
              </svg>
              <svg v-else width="8" height="8" viewBox="0 0 24 24" fill="white">
                <rect x="6" y="4" width="4" height="16"/>
                <rect x="14" y="4" width="4" height="16"/>
              </svg>
            </button>
            <!-- Seek to start button -->
            <!-- <button
              @click.stop="seekToStart"
              class="seek-btn w-4 h-4 flex items-center justify-center rounded-full bg-gray-600 hover:bg-gray-500 transition-colors"
              title="Seek to Start"
            >
              <svg width="8" height="8" viewBox="0 0 24 24" fill="white">
                <polygon points="11,7 11,17 18,12"/>
                <rect x="6" y="7" width="2" height="10"/>
              </svg>
            </button> -->
          </div>
        </div>
        <span class="text-gray-300">{{ formatDuration(duration) }}</span>
      </div>

      <!-- Item text -->
      <div class="text-xs text-gray-200 leading-tight line-clamp-2">
        {{ displayText }}
      </div>

      <!-- Audio status indicators -->
      <div v-if="hasAudio" class="flex items-center gap-1 mt-1">
        <div
          v-if="item.isGenerated1"
          class="w-2 h-2 bg-green-500 rounded-full"
          title="Voice 1 generated"
        ></div>
        <div
          v-if="item.isGenerated2"
          class="w-2 h-2 bg-blue-500 rounded-full"
          title="Voice 2 generated"
        ></div>
        <div
          v-if="item.isGenerated3"
          class="w-2 h-2 bg-purple-500 rounded-full"
          title="Voice 3 generated"
        ></div>
      </div>
    </div>

    <!-- Audio element for playback -->
    <audio
      v-if="hasAudio"
      ref="audioElement"
      :src="currentAudioUrl"
      @ended="onAudioEnded"
      @timeupdate="onAudioTimeUpdate"
      @loadedmetadata="onAudioLoaded"
      preload="metadata"
    ></audio>

    <!-- Right resize handle -->
    <div
      class="resize-handle resize-right absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-blue-500"
      @mousedown.stop="handleResizeStart('right', $event)"
    ></div>

    <!-- Selection indicator -->
    <div
      v-if="isSelected"
      class="absolute inset-0 border-2 border-blue-500 pointer-events-none"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useTTSStore } from '@/stores/ttsStore'
import { state } from '@/lib/state'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'select',
  'drag-start',
  'drag',
  'drag-end',
  'resize-start',
  'resize',
  'resize-end',
  'context-menu',
  'audio-play',
  'audio-pause',
  'seek-to-time'
])

const timelineStore = useTimelineStore()
const ttsStore = useTTSStore()

// Local state
const isHovered = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)
const dragStartX = ref(0)

// Audio playback state
const isPlayingAudio = ref(false)
const audioElement = ref(null)
const waveformCanvas = ref(null)
const showWaveform = ref(true)
const audioDuration = ref(0)
const audioCurrentTime = ref(0)

// Computed
const isSelected = computed(() => {
  return timelineStore.isItemSelected(props.item.id)
})

const duration = computed(() => {
  return props.item.endTime - props.item.startTime
})

const itemStyle = computed(() => {
  const left = timelineStore.timeToPixel(props.item.startTime)
  const width = timelineStore.timeToPixel(duration.value)
  const minWidth = 20 // Minimum width in pixels

  return {
    left: left + 'px',
    width: Math.max(width, minWidth) + 'px',
    top: '5px',
    height: (timelineStore.trackHeight - 10) + 'px',
    backgroundColor: getItemColor(),
    border: `1px solid ${getItemBorderColor()}`,
    borderRadius: '4px',
    zIndex: isSelected.value ? 10 : 1
  }
})

const displayText = computed(() => {
  // Show translated text if available, otherwise original text
  return props.item.translatedText || props.item.text || `Subtitle ${props.item.id}`
})

const hasAudio = computed(() => {
  return props.item.isGenerated1 || props.item.isGenerated2 || props.item.isGenerated3
})

const currentAudioUrl = computed(() => {
  // Get the audio URL based on the selected voice
  if (props.item.isVoice === 1 && props.item.audioUrl1) {
    return props.item.audioUrl1
  } else if (props.item.isVoice === 2 && props.item.audioUrl2) {
    return props.item.audioUrl2
  } else if (props.item.isVoice === 3 && props.item.audioUrl3) {
    return props.item.audioUrl3
  } else if (props.item.audioUrl) {
    return props.item.audioUrl
  }
  return null
})

// Methods
const getItemColor = () => {
  if (isSelected.value) {
    return '#3b82f6' // Blue for selected
  } else if (props.item.status === 'translated') {
    return '#10b981' // Green for translated
  } else if (props.item.status === 'translating') {
    return '#f59e0b' // Yellow for translating
  } else if (props.item.status === 'error') {
    return '#ef4444' // Red for error
  } else {
    return '#6b7280' // Gray for pending
  }
}

const getItemBorderColor = () => {
  if (isSelected.value) {
    return '#1d4ed8'
  } else if (isHovered.value) {
    return '#60a5fa'
  } else {
    return '#374151'
  }
}

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0.0s'
  return seconds.toFixed(1) + 's'
}

const getPlayButtonTitle = () => {
  if (isPlayingAudio.value) {
    if (state.isSynchronizedPlayback) {
      return 'Pause Synchronized Playback (Audio + Video)'
    }
    return 'Pause Audio'
  } else {
    return 'Play Synchronized Audio + Video'
  }
}

// Audio playback methods
const toggleAudioPlayback = async () => {
  if (!currentAudioUrl.value) return

  try {
    if (isPlayingAudio.value) {
      // Stop audio playback
      if (audioElement.value) {
        audioElement.value.pause()
      }
      isPlayingAudio.value = false
      state.isSynchronizedPlayback = false
      emit('audio-pause', props.item)
    } else {
      // Start synchronized playback
      await startSynchronizedAudioPlayback()
    }
  } catch (error) {
    console.error('Error toggling audio playback:', error)
    isPlayingAudio.value = false
    state.isSynchronizedPlayback = false
  }
}

const startSynchronizedAudioPlayback = async () => {
  try {
    // Pause any other playing audio
    stopAllOtherAudio()

    // Mark as synchronized playback
    state.isSynchronizedPlayback = true

    // Emit audio play event (this will trigger video synchronization in parent)
    emit('audio-play', props.item)

    // Start audio playback
    if (audioElement.value) {
      // Sync with video timeline
      const videoCurrentTime = timelineStore.currentTime
      const itemStartTime = props.item.startTime
      const relativeTime = Math.max(0, videoCurrentTime - itemStartTime)

      if (relativeTime < audioDuration.value) {
        audioElement.value.currentTime = relativeTime
      } else {
        audioElement.value.currentTime = 0
      }

      await audioElement.value.play()
      isPlayingAudio.value = true
    }

    // Sync timeline to this item's start time if not already there
    if (Math.abs(timelineStore.currentTime - props.item.startTime) > 0.1) {
      timelineStore.setCurrentTime(props.item.startTime)
      emit('seek-to-time', props.item.startTime)
    }

    console.log('Synchronized audio playback started for item:', props.item.id)

  } catch (error) {
    console.error('Error starting synchronized audio playback:', error)
    isPlayingAudio.value = false
    state.isSynchronizedPlayback = false
    throw error
  }
}

const seekToStart = () => {
  timelineStore.setCurrentTime(props.item.startTime)
  emit('seek-to-time', props.item.startTime)

  if (audioElement.value) {
    audioElement.value.currentTime = 0
    audioCurrentTime.value = 0
  }
}

const stopAllOtherAudio = () => {
  // Stop all other audio elements in the timeline
  const allAudioElements = document.querySelectorAll('.timeline-item audio')
  allAudioElements.forEach(audio => {
    if (audio !== audioElement.value && !audio.paused) {
      audio.pause()
    }
  })
}

// Audio event handlers
const onAudioEnded = () => {
  isPlayingAudio.value = false
  audioCurrentTime.value = 0
  state.isSynchronizedPlayback = false
  emit('audio-pause', props.item)
}

const onAudioTimeUpdate = () => {
  if (audioElement.value) {
    audioCurrentTime.value = audioElement.value.currentTime

    // Only sync timeline if this is synchronized playback
    if (isPlayingAudio.value && state.isSynchronizedPlayback) {
      const newTimelineTime = props.item.startTime + audioCurrentTime.value
      if (Math.abs(timelineStore.currentTime - newTimelineTime) > 0.1) {
        timelineStore.setCurrentTime(newTimelineTime)
      }
    }
  }
}

const onAudioLoaded = () => {
  if (audioElement.value) {
    audioDuration.value = audioElement.value.duration
  }
}

// Waveform methods
const handleWaveformClick = (event) => {
  if (!audioElement.value || !audioDuration.value) return

  const rect = event.target.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const progress = clickX / rect.width
  const seekTime = progress * audioDuration.value

  audioElement.value.currentTime = seekTime
  audioCurrentTime.value = seekTime

  // Update timeline position
  const newTimelineTime = props.item.startTime + seekTime
  timelineStore.setCurrentTime(newTimelineTime)
  emit('seek-to-time', newTimelineTime)
}

const drawWaveform = async () => {
  if (!waveformCanvas.value || !currentAudioUrl.value) return

  try {
    const canvas = waveformCanvas.value
    const ctx = canvas.getContext('2d')
    const rect = canvas.getBoundingClientRect()

    // Set canvas size
    canvas.width = rect.width * window.devicePixelRatio
    canvas.height = rect.height * window.devicePixelRatio
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height)

    // Draw simple waveform representation
ctx.fillStyle = 'rgba(255, 191, 0, 0.6)'    // Cam vàng với độ mờ 0.6
ctx.strokeStyle = 'rgba(255, 191, 0, 0.8)'  // Cam vàng với độ mờ 0.8

    ctx.lineWidth = 1

    const barWidth = 2
    const barSpacing = 1
    const numBars = Math.floor(rect.width / (barWidth + barSpacing))

    for (let i = 0; i < numBars; i++) {
      const x = i * (barWidth + barSpacing)
      const height = Math.random() * rect.height * 0.8 + rect.height * 0.1
      const y = (rect.height - height) / 2

      ctx.fillRect(x, y, barWidth, height)
    }

    // Draw progress indicator if audio is playing
    if (isPlayingAudio.value && audioDuration.value > 0) {
      const progress = audioCurrentTime.value / audioDuration.value
      const progressX = progress * rect.width

      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      ctx.fillRect(0, 0, progressX, rect.height)
    }
  } catch (error) {
    console.error('Error drawing waveform:', error)
  }
}

const handleMouseDown = (event) => {
  if (event.target.classList.contains('resize-handle')) return

  emit('select', props.item, event)

  if (event.button === 0) { // Left mouse button
    isDragging.value = true
    dragStartX.value = event.clientX

    emit('drag-start', props.item, event)

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'grabbing'
    document.body.style.userSelect = 'none'

    // Prevent default to avoid text selection
    event.preventDefault()
  }
}

// Throttle function using requestAnimationFrame for better performance
const throttleRAF = (func) => {
  let rafId = null
  let lastArgs = null

  return function (...args) {
    lastArgs = args
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, lastArgs)
        rafId = null
      })
    }
  }
}

const handleMouseMove = (event) => {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStartX.value
  throttledDragEmit(deltaX)
}

const throttledDragEmit = throttleRAF((deltaX) => {
  emit('drag', deltaX)
})

const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false
    emit('drag-end')

    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
}

const handleResizeStart = (edge, event) => {
  event.preventDefault()
  event.stopPropagation()

  isResizing.value = true
  emit('resize-start', props.item, edge, event)

  const handleResizeMove = (moveEvent) => {
    const deltaX = moveEvent.clientX - event.clientX
    throttledResizeEmit(deltaX)
  }

  const throttledResizeEmit = throttleRAF((deltaX) => {
    emit('resize', deltaX)
  })

  const handleResizeEnd = () => {
    isResizing.value = false
    emit('resize-end')
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = 'ew-resize'
  document.body.style.userSelect = 'none'
}

const handleContextMenu = (event) => {
  emit('context-menu',  event, props.item)
}

// Lifecycle hooks
onMounted(() => {
  // Draw initial waveform
  if (hasAudio.value && showWaveform.value) {
    nextTick(() => {
      drawWaveform()
    })
  }
})

onUnmounted(() => {
  // Stop audio if playing
  if (audioElement.value && !audioElement.value.paused) {
    audioElement.value.pause()
  }
})

// Watchers
watch(currentAudioUrl, () => {
  // Reset audio state when URL changes
  isPlayingAudio.value = false
  audioCurrentTime.value = 0
  audioDuration.value = 0

  // Redraw waveform
  if (hasAudio.value && showWaveform.value) {
    nextTick(() => {
      drawWaveform()
    })
  }
})

watch(audioCurrentTime, () => {
  // Redraw waveform to show progress
  if (isPlayingAudio.value && showWaveform.value) {
    drawWaveform()
  }
})

// Watch for timeline time changes to sync audio
watch(() => timelineStore.currentTime, (newTime) => {
  if (!audioElement.value || !isPlayingAudio.value) return

  const itemStartTime = props.item.startTime
  const itemEndTime = props.item.endTime

  // Check if timeline is within this item's range
  if (newTime >= itemStartTime && newTime <= itemEndTime) {
    // Only sync audio if not in synchronized playback mode (to avoid feedback loop)
    if (!state.isSynchronizedPlayback) {
      const relativeTime = newTime - itemStartTime
      if (Math.abs(audioElement.value.currentTime - relativeTime) > 0.1) {
        audioElement.value.currentTime = relativeTime
      }
    }
  } else if (isPlayingAudio.value) {
    // Timeline moved outside this item, pause audio and stop synchronized playback
    audioElement.value.pause()
    isPlayingAudio.value = false
    state.isSynchronizedPlayback = false
    emit('audio-pause', props.item)
  }
})

// Watch for global audio stop events
watch(() => state.currentPlayingSubtitleId, (newId) => {
  if (state.syncTable && newId !== props.item.id && isPlayingAudio.value) {
    audioElement.value.pause()
    isPlayingAudio.value = false
  }
})
</script>

<style scoped>
.timeline-item {
  transition: all 0.1s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.timeline-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-item.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-item.dragging {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
  z-index: 100 !important;
  transform: scale(1.01);
}

.timeline-item.resizing {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  z-index: 100 !important;
}

.resize-handle {
  transition: all 0.15s ease;
  border-radius: 2px;
}

.timeline-item:hover .resize-handle {
  opacity: 0.7;
}

.resize-handle:hover {
  opacity: 1 !important;
  background-color: #60a5fa !important;
  transform: scaleY(1.1);
}

.timeline-item.resizing .resize-handle {
  opacity: 1 !important;
  background-color: #3b82f6 !important;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-content {
  pointer-events: none;
}

/* Audio controls */
.audio-play-btn,
.seek-btn {
  pointer-events: auto;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.audio-play-btn:hover,
.seek-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.audio-play-btn:active,
.seek-btn:active {
  transform: scale(0.95);
}

/* Waveform canvas */
canvas {
  cursor: pointer;
  border-radius: 2px;
}

/* Audio status indicators with animation */
.timeline-item .w-2.h-2 {
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Playing state indicator */
.timeline-item.playing {
  box-shadow: 0 0 0 2px #10b981, 0 2px 6px rgba(0, 0, 0, 0.4);
}

.timeline-item.playing .audio-play-btn {
  background-color: #10b981;
}

.timeline-item.playing .audio-play-btn:hover {
  background-color: #059669;
}

/* Synchronized playback state */
.timeline-item.synchronized {
  box-shadow: 0 0 0 2px #f59e0b, 0 0 10px rgba(245, 158, 11, 0.5);
  animation: synchronizedPulse 2s infinite;
}

.timeline-item.synchronized .audio-play-btn {
  background-color: #f59e0b;
}

.timeline-item.synchronized .audio-play-btn:hover {
  background-color: #d97706;
}

@keyframes synchronizedPulse {
  0%, 100% {
    box-shadow: 0 0 0 2px #f59e0b, 0 0 10px rgba(245, 158, 11, 0.5);
  }
  50% {
    box-shadow: 0 0 0 2px #f59e0b, 0 0 20px rgba(245, 158, 11, 0.8);
  }
}
</style>
