// routes/ttsAdmin.js
const express = require('express');
const router = express.Router();

// Middleware để khởi tạo TTSService
router.use((req, res, next) => {
  req.ttsService = S.db.TtsAbbreviations;
  req.knex = S.db.knex;
  next();
});

// GET /api/tts/abbreviations - L<PERSON>y <PERSON> s<PERSON>ch abbreviations
router.get('/abbreviations', async (req, res) => {
  try {
    const { category, page = 1, limit = 50 } = req.query;
    // console.log(S.db);
    
    let query = req.knex('tts_abbreviations')
      .select('*')
      .where('is_active', true)
      .orderBy('priority', 'asc');

    if (category) {
      query = query.where('category', category);
    }

    // Phân trang
    const offset = (page - 1) * limit;
    const abbreviations = await query.limit(limit).offset(offset);
    
    // Đ<PERSON>m tổng số records
    const totalQuery = req.knex('tts_abbreviations')
      .count('id as total')
      .where('is_active', true);
    
    if (category) {
      totalQuery.where('category', category);
    }
    
    const [{ total }] = await totalQuery;

    res.json({
      data: abbreviations,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(total),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching abbreviations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/tts/abbreviations - Thêm abbreviation mới
router.post('/abbreviations', async (req, res) => {
  try {
    const { pattern, replacement, category = 'general', priority = 100, description = '' } = req.body;

    // Validation
    if (!pattern || !replacement) {
      return res.status(400).json({ error: 'Pattern and replacement are required' });
    }

    // Kiểm tra regex hợp lệ
    try {
      new RegExp(pattern);
    } catch (error) {
      return res.status(400).json({ error: 'Invalid regex pattern' });
    }

    const id = await req.ttsService.addAbbreviation(pattern, replacement, category, priority, description);
    
    res.status(201).json({
      id,
      message: 'Abbreviation added successfully'
    });
  } catch (error) {
    console.error('Error adding abbreviation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/tts/abbreviations/:id - Cập nhật abbreviation
router.put('/abbreviations/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { pattern, replacement, category, priority, description, is_active } = req.body;

    // Validation
    if (pattern) {
      try {
        new RegExp(pattern);
      } catch (error) {
        return res.status(400).json({ error: 'Invalid regex pattern' });
      }
    }

    const updates = {};
    if (pattern !== undefined) updates.pattern = pattern;
    if (replacement !== undefined) updates.replacement = replacement;
    if (category !== undefined) updates.category = category;
    if (priority !== undefined) updates.priority = priority;
    if (description !== undefined) updates.description = description;
    if (is_active !== undefined) updates.is_active = is_active;

    const result = await req.ttsService.updateAbbreviation(id, updates);
    
    if (result === 0) {
      return res.status(404).json({ error: 'Abbreviation not found' });
    }

    res.json({ message: 'Abbreviation updated successfully' });
  } catch (error) {
    console.error('Error updating abbreviation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/tts/abbreviations/:id - Xóa abbreviation
router.delete('/abbreviations/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await req.ttsService.deleteAbbreviation(id);
    
    if (result === 0) {
      return res.status(404).json({ error: 'Abbreviation not found' });
    }

    res.json({ message: 'Abbreviation deleted successfully' });
  } catch (error) {
    console.error('Error deleting abbreviation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/tts/categories - Lấy danh sách categories
router.get('/categories', async (req, res) => {
  try {
    const categories = await req.knex('tts_abbreviations')
      .select('category')
      .count('id as count')
      .where('is_active', true)
      .groupBy('category')
      .orderBy('category', 'asc');

    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/tts/test - Test normalize text
router.post('/test', async (req, res) => {
  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text is required' });
    }

    const normalized = await req.ttsService.normalizeTextForTTS(text);
    
    res.json({
      original: text,
      normalized: normalized
    });
  } catch (error) {
    console.error('Error testing text:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/tts/clear-cache - Clear cache
router.post('/clear-cache', async (req, res) => {
  try {
    req.ttsService.clearCache();
    res.json({ message: 'Cache cleared successfully' });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/tts/bulk-import - Import từ file CSV
router.post('/bulk-import', async (req, res) => {
  try {
    const { abbreviations } = req.body; // Array of abbreviation objects
    
    if (!Array.isArray(abbreviations)) {
      return res.status(400).json({ error: 'Abbreviations must be an array' });
    }

    const results = [];
    
    for (const abbr of abbreviations) {
      try {
        const id = await req.ttsService.addAbbreviation(
          abbr.pattern,
          abbr.replacement,
          abbr.category || 'general',
          abbr.priority || 100,
          abbr.description || ''
        );
        results.push({ success: true, id, pattern: abbr.pattern });
      } catch (error) {
        results.push({ success: false, error: error.message, pattern: abbr.pattern });
      }
    }

    res.json({
      message: 'Bulk import completed',
      results
    });
  } catch (error) {
    console.error('Error bulk importing:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;

// Cách sử dụng trong app.js:
// const ttsAdminRoutes = require('./routes/ttsAdmin');
// app.use('/api/tts', ttsAdminRoutes);