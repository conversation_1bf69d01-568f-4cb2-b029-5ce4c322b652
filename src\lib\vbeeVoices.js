const voiceVbees = [
    {
        "id": "61947d641c159f3c2c313de2",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-ngochuyen-news-44k",
        "code": "hn_female_ngochuyen_full_48k-fhg",
        "default_sample_rate": 44100,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_ngochuyen_fast_news_48k-thg.mp3",
        "gender": "female",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "<PERSON><PERSON> - <PERSON>",
        "provider": "vbee",
        "rank": 1,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/ngoc-huyen.png",
        "sample": {
            "audio_link": "/audios/samples/ngoc-huyen.mp3",
            "style": "tourist",
            "text": "Xin chào quý vị và các bạn! Quy Nhơn là thành phố biển xinh đẹp của tỉnh Bình Định thuộc khu vực Duyên Hải Nam Trung Bộ. Thành phố này cách thủ đô Hà Nội 1065km."
        },
        "sample_rates": [
            8000,
            16000,
            22050,
            32000,
            44100
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/ngoc-huyen.png",
        "synthesis_function": "a2s-jets-prod-ngochuyen-news-44k-jets",
        "type": "Neural TTS"
    },
    {
        "id": "62d176ca0eeb959b36831b71",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-tuongvy-call-44k",
        "code": "sg_female_tuongvy_call_44k-fhg",
        "default_sample_rate": 44100,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_female_tuongvy_call_44k-fhg.mp3",
        "features": [],
        "gender": "female",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "SG - Tường Vy",
        "provider": "vbee",
        "rank": 2,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/tuong-vy.png",
        "sample": {
            "audio_link": "/audios/samples/tuong-vy.mp3",
            "style": "callCenter",
            "text": "Bệnh viện đa khoa Vbee kính chào anh/chị, đây là cuộc gọi nhắc lịch khám từ bệnh viện. Anh/chị có lịch hẹn khám vào lúc 2h chiều ngay 27/8. Xin vui lòng đến phòng P205 tầng 2 khoa da liễu để tiếp tục theo dõi và điều trị."
        },
        "sample_rates": [
            8000,
            16000,
            22050,
            32000,
            44100
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/tuong-vy.png",
        "styles": [
            "call-center"
        ],
        "synthesis_function": "a2s-jets-prod-tuongvy-call-44k-jets",
        "type": "Neural TTS"
    },
    {
        "id": "66a892b6aa50ebba0dc2d22c",
        "active": true,
        "beta": true,
        "code": "hn_male_vietbach_child_22k-vc",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_vietbach_child_22k-vc.mp3",
        "features": [
            "premium-vietnam-voice"
        ],
        "gender": "male",
        "has_dubbing": false,
        "is_sample": false,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "PREMIUM",
        "name": "HN - Việt Bách",
        "provider": "vbee",
        "rank": 2,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/viet-bach.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/viet-bach.png",
        "styles": [
            "child"
        ],
        "synthesis_function": "function:a2s-stable-prod-vietbach-22k-stable",
        "type": "Neural TTS"
    },
    {
        "id": "632c3cbde5a553c69f3207ed",
        "active": true,
        "beta": false,
        "code": "sg_female_thaotrinh_full_44k-phg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_female_thaotrinh_full_44k-phg.mp3",
        "features": [
            "emphasis"
        ],
        "gender": "female",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "SG - Thảo Trinh",
        "provider": "vbee",
        "rank": 3,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/thao-trinh.png",
        "sample": {
            "audio_link": "/audios/samples/thao-trinh.mp3",
            "style": "audioBook",
            "text": "Mình là cá, việc của mình là bơi - Trái tim tôi dường như đã tan vỡ. Tôi vốn là một người theo chủ nghĩa hoàn hảo và không tin tưởng người khác. Do đó, tôi thường ôm tất cả vào người và tự mình giải quyết."
        },
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/thao-trinh.png",
        "styles": [
            "emphasis"
        ],
        "synthesis_function": "a2s-phg-prod-thaotrinh-full-phg-py311",
        "type": "Neural TTS",
        "version": "1.1"
    },
    {
        "id": "6599f16ffe0be226cb42ed24",
        "active": true,
        "beta": true,
        "code": "sg_male_chidat_ebook_48k-phg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_male_chidat_ebook_48k-phg.wav",
        "features": [
            "premium-vietnam-voice"
        ],
        "gender": "male",
        "has_dubbing": true,
        "is_sample": false,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "PREMIUM",
        "name": "SG - Chí Đạt",
        "provider": "vbee",
        "rank": 3,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/chi-dat.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/chi-dat.png",
        "styles": [
            "story"
        ],
        "synthesis_function": "a2s-phg-prod-chidat-ebook-phg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "668c1197edd8550e34ca1ffc",
        "active": true,
        "beta": true,
        "code": "hn_female_hachi_book_22k-vc",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_hachi_book_22k-vc.mp3",
        "features": [
            "premium-vietnam-voice"
        ],
        "gender": "female",
        "has_dubbing": false,
        "is_sample": false,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "PREMIUM",
        "name": "HN - Hà Chi",
        "provider": "vbee",
        "rank": 3,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/ha-chi.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/ha-chi.png",
        "styles": [
            "narration"
        ],
        "synthesis_function": "function:a2s-stable-prod-fonos02-22k-stable",
        "type": "Neural TTS"
    },
    {
        "id": "646b478406d76f2addd3cefd",
        "active": true,
        "beta": false,
        "code": "hn_male_phuthang_stor80dt_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_phuthang_stor80dt_48k-fhg.mp3",
        "features": [],
        "gender": "male",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Anh Khôi",
        "provider": "vbee",
        "rank": 4,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/anh-khoi.png",
        "sample": {
            "audio_link": "/audios/samples/anh-khoi.mp3",
            "style": "story",
            "text": "Thời Tuyên Đức, trong vùng rất chuộng chơi chọi dế, hàng năm bắt dân gian cung tiến. Trò đó không phải nảy sinh từ đất Thiểm Tây, mà do viên quan huyện lệnh huyện Hoa Âm muốn lấy lòng quan trên đem tiến một con."
        },
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/anh-khoi.png",
        "styles": [
            "story"
        ],
        "synthesis_function": "a2s-phg-prod-phuthang-stor-phg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "64dca259665065790b992266",
        "active": true,
        "beta": true,
        "code": "hn_female_hermer_stor_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_hermer_stor_48k-fhg.mp3",
        "features": [],
        "gender": "female",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Ngọc Lan",
        "provider": "vbee",
        "rank": 4,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/ngoc-lan.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/ngoc-lan.png",
        "styles": [
            "story"
        ],
        "synthesis_function": "a2s-phg-prod-hermer-stor-phg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "655e82af2069128f951acefc",
        "active": true,
        "beta": true,
        "code": "hn_female_lenka_stor_48k-phg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_lenka_stor_48k-phg.wav",
        "features": [],
        "gender": "female",
        "has_dubbing": true,
        "is_sample": false,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Nguyệt Dương",
        "provider": "vbee",
        "rank": 4,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/nguyet-duong.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/nguyet-duong.png",
        "styles": [
            "story"
        ],
        "synthesis_function": "a2s-phg-prod-lenka-stor-phg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313dea",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-manhdung-news",
        "code": "hn_male_manhdung_news_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_manhdung_news_48k_cs-thg.mp3",
        "gender": "male",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Mạnh Dũng",
        "provider": "vbee",
        "rank": 5,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/manh-dung.png",
        "sample": {
            "audio_link": "/audios/samples/manh-dung.mp3",
            "style": "marketing",
            "text": "Kamito TA11 là một sự kết hợp giữa Kamito và nhà ảo thuật gia tiền vệ tài hoa Nguyễn Tuấn Anh của câu lạc bộ Hoàng Anh Gia Lai. Đây là mẫu giày mà nhà sản xuất đã mất rất nhiều thời gian để nghiên cứu, phát triển và hoàn thiện."
        },
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/manh-dung.png",
        "synthesis_function": "a2s-fhg-prod-manhdung-news-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61dceb25ad6e3f747603ba3a",
        "active": true,
        "code": "hn_male_thanhlong_talk_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_thanhlong_talk_48k-fhg.mp3",
        "gender": "male",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Thanh Long",
        "provider": "vbee",
        "rank": 5,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/thanh-long.png",
        "sample": {
            "audio_link": "/audios/samples/thanh-long.mp3",
            "style": "podcast",
            "text": "Trong khi vẫn đang loay hoay tìm câu trả lời cho câu hỏi “Tôi là ai”, “Tôi sinh ra để làm gì” Cậu đã vô tình tìm thấy một lá thư từ rất lâu, từ người cha yêu dấu gửi cho mình."
        },
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/thanh-long.png",
        "synthesis_function": "a2s-fhg-prod-thanhlong-talk-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de6",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-thaotrinh-full",
        "code": "sg_female_thaotrinh_full_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_female_thaotrinh_fast_news_48k_cs-thg.mp3",
        "gender": "female",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "SG - Thảo Trinh",
        "provider": "vbee",
        "rank": 6,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/thao-trinh.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/thao-trinh.png",
        "synthesis_function": "a2s-jets-prod-thaotrinh-full-jets",
        "type": "Neural TTS"
    },
    {
        "id": "61e2fd00b9c5be01b37b5e36",
        "active": true,
        "code": "hn_male_phuthang_news65dt_44k-fhg",
        "default_sample_rate": 44100,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_phuthang_news65dt_44k-fhg.mp3",
        "gender": "male",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Anh Khôi",
        "provider": "vbee",
        "rank": 7,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/anh-khoi.png",
        "sample_rates": [
            8000,
            16000,
            22050,
            32000,
            44100
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/anh-khoi.png",
        "synthesis_function": "a2s-fhg-prod-phuthang-news65-44k-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de5",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-huonggiang-full",
        "code": "hue_female_huonggiang_full_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hue_female_huonggiang_news_48k_cs-thg.mp3",
        "gender": "female",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "Huế - Hương Giang",
        "provider": "vbee",
        "rank": 8,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/huong-giang.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/huong-giang.png",
        "synthesis_function": "a2s-fhg-prod-huonggiang-full-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de3",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-maiphuong-vdts",
        "code": "hn_female_maiphuong_vdts_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_female_maiphuong_vdts_48k_cs-thg.mp3",
        "gender": "female",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Mai Phương",
        "provider": "vbee",
        "rank": 9,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/mai-phuong.png",
        "sample": {
            "audio_link": "/audios/samples/mai-phuong.mp3",
            "style": "news",
            "text": "Dự án cầu Vĩnh Tuy 2 khởi công vào tháng 1/2021 được xem là công trình có ý nghĩa quan trọng với thủ đô nhằm hoàn thiện toàn bộ đường vành đai 2 của thành phố Hà Nội."
        },
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/mai-phuong.png",
        "synthesis_function": "a2s-jets-prod-maiphuong-vdts-jets",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de4",
        "active": true,
        "code": "sg_female_lantrinh_vdts_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_female_lantrinh_fast_vdts_48k_cs-thg.mp3",
        "gender": "female",
        "has_dubbing": true,
        "is_sample": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "SG - Lan Trinh",
        "provider": "vbee",
        "rank": 10,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/lan-trinh.png",
        "sample": {
            "audio_link": "/audios/samples/lan-trinh.mp3",
            "style": "history",
            "text": "Ngày 26/9, Hồ Chủ tịch đọc lời kêu gọi: “Tôi tin vào đồng bào cả nước đều tin vào lòng kiên quyết, ái quốc của đồng bào Nam Bộ, thà chết tự do còn hơn sống nô lệ”"
        },
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/lan-trinh.png",
        "synthesis_function": "a2s-jets-prod-lantrinh-vdts-jets",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de7",
        "active": true,
        "code": "sg_male_trungkien_vdts_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_male_trungkien_vdts_48k-fhg.mp3",
        "gender": "male",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "SG - Trung Kiên",
        "provider": "vbee",
        "rank": 11,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/trung-kien.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/trung-kien.png",
        "synthesis_function": "a2s-fhg-prod-trungkien-vdts-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de8",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-duyphuong-full",
        "code": "hue_male_duyphuong_full_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_female_duyphuong_fast_news_48k_cs-thg.mp3",
        "gender": "male",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "Huế - Duy Phương",
        "provider": "vbee",
        "rank": 12,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/duy-phuong.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/duy-phuong.png",
        "synthesis_function": "a2s-fhg-prod-duyphuong-full-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "61947d641c159f3c2c313de9",
        "active": true,
        "caching_function": "vbee_cloud_tts_cache-viterbi-minhhoang-full",
        "code": "sg_male_minhhoang_full_48k-fhg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/sg_male_minhhoang_fast_news_48k_cs-thg.mp3",
        "gender": "male",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "SG - Minh Hoàng",
        "provider": "vbee",
        "rank": 13,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/minh-hoang.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/minh-hoang.png",
        "synthesis_function": "a2s-fhg-prod-minhhoang-full-fhg-py311",
        "type": "Neural TTS"
    },
    {
        "id": "6384175ff7210c49c4e87843",
        "active": true,
        "beta": false,
        "code": "hn_male_manhdung_news_48k-phg",
        "default_sample_rate": 22050,
        "demo": "https://vbee.s3.ap-southeast-1.amazonaws.com/audios/demo/vbee/hn_male_manhdung_news_48k-phg.mp3",
        "features": [
            "emphasis"
        ],
        "gender": "male",
        "has_dubbing": true,
        "language": {
            "code": "vi-VN",
            "global_name": "Vietnamese",
            "rank": 1,
            "rectangle_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/rectangle/vn.png",
            "round_image": "https://vbee.s3.ap-southeast-1.amazonaws.com/images/nations/round/vn.png",
            "vietnamese_name": "Việt Nam"
        },
        "language_code": "vi-VN",
        "level": "STANDARD",
        "name": "HN - Mạnh Dũng",
        "provider": "vbee",
        "rank": 14,
        "round_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/round/manh-dung.png",
        "sample_rates": [
            8000,
            16000,
            22050
        ],
        "square_image": "https://vbee-studio-data.s3.ap-southeast-1.amazonaws.com/images/voices/square/manh-dung.png",
        "styles": [
            "emphasis"
        ],
        "synthesis_function": "a2s-phg-prod-manhdung-news-phg-py311",
        "type": "Neural TTS",
        "version": "1.1"
    }
]

const voices = voiceVbees.map(v => ({
    ...v,
  id: v.code,
}));    



export default voices;