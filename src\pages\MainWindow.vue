<template>
    <div class="wrapper">
        <div class="stack stack-left">
            <SubtitleInfoTable v-model:current-frame="currentFrame" v-model:subtitle-infos="subtitleInfos" />
            <button class="primary-button" @click="saveASS">Save subtitles</button>
        </div>
        <div class="stack stack-right">
            <VideoPlayer v-model:current-frame="currentFrame" :video-properties="videoProperties" :videoPath="videoPath"
                @prev-subtitle="prevSubtitleEvent" @next-subtitle="nextSubtitleEvent" ref="videoPlayer" />
            <Timeline v-model:current-frame="currentFrame" :fps="fps" :total-frame="videoProperties.lastFrame"
                v-model:subtitle-infos="subtitleInfos" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import SubtitleInfoTable from '@/components/subtitle/SubtitleInfoTable.vue'
import VideoPlayer from '@/components/subtitle/VideoPlayer2.vue'
import Timeline from '@/components/subtitle/Timeline.vue'

import { SubtitleInfo } from '@/lib/SubtitleInfo'
import { VideoProperties } from '@/lib/VideoProperties'
import { useDialogStore } from '@/stores/dialog'

const route = useRoute()
const dialogStore = useDialogStore()
const videoPlayer = ref(null)

const subtitleInfos = ref<SubtitleInfo[]>([])
const videoProperties = ref(new VideoProperties(0, [1, 1], [1, 1], 0, 0))
const currentFrame_ = ref(0)
const videoPath = ref('')

const currentFrame = computed({
    get: () => currentFrame_.value,
    set: (value: number) => {
        if (value < 0) value = 0
        if (value > videoProperties.value.lastFrame) {
            value = videoProperties.value.lastFrame
        }
        currentFrame_.value = value
    },
})

const fps = computed(() => videoProperties.value.fps[0] / videoProperties.value.fps[1])

function prevSubtitleEvent() {
    for (const i of subtitleInfos.value.keys()) {
        if (subtitleInfos.value[i].endFrame >= currentFrame.value) {
            currentFrame.value = subtitleInfos.value[Math.max(0, i - 1)].startFrame
            break
        }
    }
}

function nextSubtitleEvent() {
    for (const i of subtitleInfos.value.keys()) {
        if (subtitleInfos.value[i].startFrame > currentFrame.value) {
            currentFrame.value = subtitleInfos.value[i].startFrame
            break
        }
    }
}

async function saveASS() {
    const path = (await electronAPI.invoke('CommonIpc:SaveASSDialog')) as string | null
    if (path != null) {
        await electronAPI.invoke(
            'ASSGenerator:GenerateAndSave',
            subtitleInfos.value,
            videoProperties.value,
            path
        )
    }
}

async function openVideo(path?: string) {
    //   if (!path) throw new Error('Cannot load video from path')
    const files = await dialogStore.showOpenFiles(['mp4'])
    console.log('files', files);

    if (!files) return
    path = files[0]
    const props = { "duration": 1280512, "timeBase": [1, 15360], "fps": [30, 1], "width": 720, "height": 1280 }//(await electronAPI.invoke('VideoPlayer:OpenVideo', path)) as VideoProperties | null
    console.log('props', props);

    if (props) {
        videoProperties.value = new VideoProperties(props)
        videoPath.value = path;
        (videoPlayer.value as any).setVideoPath(path)
        currentFrame.value = 0
    }
}

onMounted(async () => {
    const path = route.params.path as string | undefined
    await openVideo(path)

    const storedInfos = [
  {
    texts: ['察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察'],
    id: '64730044-6ee8-4082-8803-a8544917e8d7',
    startFrame: 8,
    endFrame: 23,
    fps: 60,
    startTime: '00:00:00.13',
    endTime: '00:00:00.38',
  },
  {
    texts: ['察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察', '察'],
    id: 'a89ce17b-b57b-4e83-867d-184cc5204b6f',
    startFrame: 30,
    endFrame: 43,
    fps: 60,
    startTime: '00:00:00.50',
    endTime: '00:00:00.71',
  },
  {
    texts: ['察', '察'],
    id: 'd6834cdf-685f-4d00-880f-b2963d44d19e',
    startFrame: 52,
    endFrame: 54,
    fps: 60,
    startTime: '00:00:00.86',
    endTime: '00:00:00.90',
  },
  {
    texts: [
      '察',
      '察',
      '察',
    ],
    id: '2e9009e0-0368-4425-96dc-83b608046c97',
    startFrame: 74,
    endFrame: 161,
    fps: 60,
    startTime: '00:00:01.23',
    endTime: '00:00:02.68',
  },
  {
    texts: ['察', '察'],
    id: '3ebf86de-b13f-49e2-a171-78b4cec2f6e4',
    startFrame: 268,
    endFrame: 270,
    fps: 60,
    startTime: '00:00:04.46',
    endTime: '00:00:04.50',
  },
  {
    texts: [
      '察',
      '察',
      '察',
      '察',
      '察',
    ],
    id: '69f69f3f-add2-436a-ac6e-6b27f0ea8e25',
    startFrame: 285,
    endFrame: 397,
    fps: 60,
    startTime: '00:00:04.75',
    endTime: '00:00:06.61',
  },
  {
    texts: ['察', '察', '察', '察', '察'],
    id: '7c212df5-a83d-4ccf-9d66-112b5254298a',
    startFrame: 414,
    endFrame: 419,
    fps: 60,
    startTime: '00:00:06.90',
    endTime: '00:00:06.98',
  },
  {
    texts: [
      '察',
      '察',
      '察',
      '察',
    ],
    id: 'dc2e5a41-6a42-414c-9166-14c9b7e22854',
    startFrame: 441,
    endFrame: 549,
    fps: 60,
    startTime: '00:00:07.35',
    endTime: '00:00:09.15',
  },
  {
    texts: [
      '察',
      '察',
      '察',
      '察',
      '察',
      '察',
    ],
    id: '66e65b27-ffa8-4b08-aed6-ab59af20375d',
    startFrame: 584,
    endFrame: 608,
    fps: 60,
    startTime: '00:00:09.73',
    endTime: '00:00:10.13',
  },
];

    subtitleInfos.value = storedInfos.map((t) => {
        const info = new SubtitleInfo(t)
        info.fps = fps.value
        return info
    })
})
</script>


<style scoped>
.stack {
    width: 50%;
    display: flex;
    flex-direction: column;
}

.save-sub {
    padding: 12px 16px;
    margin-top: 16px;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.16);
    border-radius: 2px;
    border: none;
    transition: 0.2s all;
    background: #0d1f2d;
    color: rgba(#fff, 0.7);
}

.save-sub:hover {
    background: #18a1b420;
    border: #18a1b420;
}

.stack-left {
    min-width: 430px;
    margin: 40px 16px 40px 40px;
}

.stack-right {
    min-width: 520px;
    margin: 40px 40px 40px 16px;
}

.wrapper {
    background: #162835;
    display: flex;
    flex-direction: row;
    position: fixed;
    /* top: 36px; */
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
</style>
