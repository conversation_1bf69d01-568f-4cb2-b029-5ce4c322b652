const axios = require("axios");
const cheerio = require("cheerio");

/**
 * SnapSave Clone - Download videos/images from Facebook and Instagram
 * @param {string} url - The URL of the Facebook or Instagram post
 * @returns {Promise<Object>} - Promise resolving to download data
 */
module.exports = function snapsaveClone(url) {
  return new Promise(async (resolve) => {
    try {
      // Validate URL for Facebook and Instagram
      const facebookRegex = /(?:https?:\/\/(web\.|www\.|m\.)?(facebook|fb)\.(com|watch)\S+)?$/;
      const instagramRegex = /(https|http):\/\/www.instagram.com\/(p|reel|tv|stories)/gi;
      
      if (!url.match(facebookRegex) && !url.match(instagramRegex)) {
        return resolve({
          status: false,
          msg: "Invalid URL - Only Facebook and Instagram URLs are supported"
        });
      }

      /**
       * Custom decoder function for snapsave response
       * @param {Array} params - Array of parameters for decoding
       * @returns {string} - Decoded string
       */
      function customDecoder(params) {
        let [data, base1, alphabet, base2, offset, result] = params;
        
        function baseConverter(input, fromBase, toBase) {
          const chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ+/".split('');
          let fromChars = chars.slice(0, fromBase);
          let toChars = chars.slice(0, toBase);
          
          let decimal = input.split('').reverse().reduce(function (acc, char, index) {
            if (fromChars.indexOf(char) !== -1) {
              return acc += fromChars.indexOf(char) * Math.pow(fromBase, index);
            }
          }, 0);
          
          let converted = '';
          while (decimal > 0) {
            converted = toChars[decimal % toBase] + converted;
            decimal = (decimal - decimal % toBase) / toBase;
          }
          return converted || '0';
        }

        result = '';
        let index = 0;
        
        for (let i = data.length; index < i; index++) {
          let segment = '';
          while (data[index] !== alphabet[offset]) {
            segment += data[index];
            index++;
          }
          
          for (let j = 0; j < alphabet.length; j++) {
            segment = segment.replace(new RegExp(alphabet[j], 'g'), j.toString());
          }
          
          result += String.fromCharCode(baseConverter(segment, offset, 10) - base2);
        }
        
        return decodeURIComponent(encodeURIComponent(result));
      }

      /**
       * Extract parameters from encoded response
       * @param {string} encodedData - The encoded response data
       * @returns {Array} - Array of parameters for decoder
       */
      function extractDecoderParams(encodedData) {
        return encodedData
          .split("decodeURIComponent(escape(r))}(")[1]
          .split('))')[0]
          .split(',')
          .map(param => param.replace(/"/g, '').trim());
      }

      // Request headers to mimic browser
      const headers = {
        accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
        "content-type": "application/x-www-form-urlencoded",
        origin: "https://snapsave.app",
        referer: "https://snapsave.app/id",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36"
      };

      // Make request to snapsave API
      const response = await axios.post(
        "https://snapsave.app/action.php?lang=id", 
        'url=' + url, 
        { headers }
      );

      const responseData = response.data;
      
      // Decode the response
      const decodedData = customDecoder(extractDecoderParams(responseData));
      
      // Extract HTML content
      const htmlContent = decodedData
        .split("getElementById(\"download-section\").innerHTML = \"")[1]
        .split("\"; document.getElementById(\"inputData\").remove(); ")[0]
        .replace(/\\(\\)?/g, '');

      // Parse HTML with cheerio
      const $ = cheerio.load(htmlContent);
      const downloadItems = [];

      // Check for table format (Facebook videos)
      if ($("table.table").length || $("article.media > figure").length) {
        const thumbnail = $("article.media > figure").find("img").attr("src");
        
        $("tbody > tr").each((index, element) => {
          const row = $(element);
          const cells = row.find('td');
          const resolution = cells.eq(0).text();
          let downloadUrl = cells.eq(2).find('a').attr("href") || cells.eq(2).find("button").attr("onclick");
          
          // Check if URL needs special handling
          const needsRendering = /get_progressApi/ig.test(downloadUrl || '');
          if (needsRendering) {
            downloadUrl = /get_progressApi\('(.*?)'\)/.exec(downloadUrl || '')?.[1] || downloadUrl;
          }
          
          downloadItems.push({
            resolution: resolution,
            thumbnail: thumbnail,
            url: downloadUrl,
            shouldRender: needsRendering
          });
        });
      } 
      // Handle Instagram format
      else {
        $("div.download-items__thumb").each((thumbIndex, thumbElement) => {
          const thumbnail = $(thumbElement).find("img").attr("src");
          
          $("div.download-items__btn").each((btnIndex, btnElement) => {
            let downloadUrl = $(btnElement).find('a').attr('href');
            
            // Ensure absolute URL
            if (!/https?:\/\//.test(downloadUrl || '')) {
              downloadUrl = "https://snapsave.app" + downloadUrl;
            }
            
            downloadItems.push({
              thumbnail: thumbnail,
              url: downloadUrl
            });
          });
        });
      }

      // Check if we got any results
      if (!downloadItems.length) {
        return resolve({
          status: false,
          msg: "No download links found"
        });
      }

      // Return successful response
      return resolve({
        status: true,
        data: downloadItems
      });

    } catch (error) {
      return resolve({
        status: false,
        msg: error.message
      });
    }
  });
};

/**
 * Usage example:
 * 
 * const snapsave = require('./snapsave_clone');
 * 
 * // For Facebook
 * snapsave('https://www.facebook.com/watch?v=123456789')
 *   .then(result => console.log(result))
 *   .catch(err => console.error(err));
 * 
 * // For Instagram
 * snapsave('https://www.instagram.com/p/ABC123/')
 *   .then(result => console.log(result))
 *   .catch(err => console.error(err));
 */
