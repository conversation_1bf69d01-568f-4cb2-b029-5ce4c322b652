<template>
  <div class="space-y-4">
    <h2 class="text-xl font-semibold">
      {{ transcriptionEngine === 'edgetts' ? 'EdgeTTS Audio Transcription' : 'Whisper Audio Transcription' }}
    </h2>

    <!-- File/Folder upload area -->
    <div v-if="mediaFiles.length === 0">
      <div class="space-y-4">
        <!-- Multiple files upload -->
        <DragDropUpload
          accept="video/*,audio/*"
          :max-size="10 * 1024 * 1024 * 1024"
          :show-preview="false"
          :multiple="true"
          drag-text="Drag and drop video or audio files here"
          drop-text="Drop files here"
          click-text="or click to select files"
          @files-selected="handleMediaSelected"
        />
        
        <!-- Folder selection -->
        <div class="text-center">
          <a-button @click="selectFolder" size="large" type="dashed">
            Select Folder
          </a-button>
          <p class="text-sm text-gray-500 mt-2">Choose a folder containing audio/video files</p>
        </div>
      </div>
    </div>

    <!-- Media files list and controls -->
    <div v-if="mediaFiles.length > 0" class="space-y-4">
      <!-- Files summary -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          <span class="font-medium">{{ mediaFiles.length }}</span> files selected
          <span v-if="processedCount > 0" class="ml-2">
            ({{ processedCount }}/{{ mediaFiles.length }} processed)
          </span>
        </div>
        <div class="space-x-2">
          <a-button @click="addMoreFiles" size="small">Add More Files</a-button>
          <a-button @click="resetMedia" size="small">Reset All</a-button>
        </div>
      </div>

      <!-- Files list -->
      <div class="max-h-60 overflow-y-auto border border-gray-500 rounded-md">
        <div
          v-for="(file, index) in mediaFiles"
          :key="index"
          class="flex items-center justify-between p-3 border-b border-gray-400 last:border-b-0"
          :class="{
            'bg-green-200': file.status === 'completed',
            'bg-red-200': file.status === 'error',
            'bg-blue-200': file.status === 'processing',
            'bg-gray-600': file.status === 'pending'
          }"
        >
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium truncate">{{ file.name }}</div>
            <div class="text-xs text-gray-500">
              {{ file.type || 'Unknown type' }}
              <span v-if="file.status === 'processing'" class="ml-2 text-blue-600">
                {{ file.processingStatus || 'Processing...' }}
              </span>
              <span v-if="file.status === 'completed'" class="ml-2 text-green-600">
                Completed
              </span>
              <span v-if="file.status === 'error'" class="ml-2 text-red-600">
                Error: {{ file.error }}
              </span>
            </div>
          </div>
          
          <div class="flex items-center space-x-2 ml-4">
            <!-- Status indicator -->
            <div class="w-3 h-3 rounded-full flex-shrink-0"
                 :class="{
                   'bg-green-500': file.status === 'completed',
                   'bg-red-500': file.status === 'error',
                   'bg-blue-500 animate-pulse': file.status === 'processing',
                   'bg-gray-300': file.status === 'pending'
                 }">
            </div>
            
            <!-- Action buttons -->
            <div class="flex space-x-1">
              <a-button 
                v-if="file.status === 'completed' && file.srtPath"
                size="small"
                @click="openSrtFile(file.srtPath)"
              >
                Open SRT
              </a-button>
              <a-button 
                v-if="file.status === 'completed' && file.srtPath"
                size="small"
                @click="importToSrtTable(file.srtPath)"
              >
                Import
              </a-button>
              <a-button 
                size="small"
                danger
                @click="removeFile(index)"
                :disabled="file.status === 'processing'"
              >
                Remove
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview area for current file -->
      <div v-if="currentPreviewFile" class="space-y-2">
        <h4 class="text-sm font-medium">Preview: {{ currentPreviewFile.name }}</h4>
        
        <!-- Video preview -->
        <div v-if="isVideoFile(currentPreviewFile)" class="relative rounded-md overflow-hidden bg-black">
          <video
            ref="videoRef"
            :src="currentPreviewFile.url"
            controls
            class="w-full max-h-[300px]"
          ></video>
        </div>

        <!-- Audio preview -->
        <div v-if="isAudioFile(currentPreviewFile)" class="relative rounded-md overflow-hidden bg-gray-100 p-4">
          <audio
            ref="audioRef"
            :src="currentPreviewFile.url"
            controls
            class="w-full"
          ></audio>
        </div>
      </div>

      <!-- Whisper settings -->
      <div class="space-y-4 p-4 border border-gray-200 rounded-md">
        <h3 class="text-lg font-medium">Transcription Settings</h3>

        <!-- Language selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Language</label>
          <a-select v-model:value="language" style="width: 100%">
            <a-select-option value="auto">Auto-detect</a-select-option>
            <a-select-option value="en">English</a-select-option>
            <a-select-option value="zh">Chinese</a-select-option>
            <a-select-option value="ja">Japanese</a-select-option>
            <a-select-option value="ko">Korean</a-select-option>
            <a-select-option value="fr">French</a-select-option>
            <a-select-option value="de">German</a-select-option>
            <a-select-option value="es">Spanish</a-select-option>
            <a-select-option value="it">Italian</a-select-option>
            <a-select-option value="ru">Russian</a-select-option>
            <a-select-option value="pt">Portuguese</a-select-option>
            <a-select-option value="vi">Vietnamese</a-select-option>
          </a-select>
        </div>

        <!-- Transcription Engine -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Transcription Engine</label>
          <a-select v-model:value="transcriptionEngine" style="width: 100%" @change="onEngineChange">
            <a-select-option value="whisper">Whisper (Local)</a-select-option>
            <a-select-option value="edgetts">EdgeTTS (Online)</a-select-option>
          </a-select>
        </div>

        <!-- Model selection (for Whisper) -->
        <div v-if="transcriptionEngine === 'whisper'" class="space-y-1">
          <label class="text-sm font-medium">Whisper Model</label>
          <a-select v-model:value="model" style="width: 100%">
            <a-select-option value="tiny">Tiny (fastest, least accurate)</a-select-option>
            <a-select-option value="base">Base</a-select-option>
            <a-select-option value="small">Small</a-select-option>
            <a-select-option value="medium">Medium</a-select-option>
            <a-select-option value="large-v2">Large v2</a-select-option>
            <a-select-option value="large-v3">Large v3 (slowest, most accurate)</a-select-option>
          </a-select>
        </div>

        <!-- EdgeTTS Model selection -->
        <div v-if="transcriptionEngine === 'edgetts'" class="space-y-1">
          <label class="text-sm font-medium">EdgeTTS Model</label>
          <a-select v-model:value="edgeTtsModel" style="width: 100%">
            <a-select-option value="tiny">Tiny</a-select-option>
            <a-select-option value="base">Base</a-select-option>
            <a-select-option value="small">Small</a-select-option>
            <a-select-option value="medium">Medium</a-select-option>
            <a-select-option value="large-v2">Large v2</a-select-option>
            <a-select-option value="large-v3">Large v3 (Recommended)</a-select-option>
          </a-select>
        </div>

        <!-- Model directory (for Whisper) -->
        <div v-if="transcriptionEngine === 'whisper'" class="space-y-1">
          <label class="text-sm font-medium">Model Directory</label>
          <div class="flex space-x-2">
            <a-input v-model:value="srtStore.modelDir" placeholder="Path to Whisper models directory" />
            <a-button @click="selectModelDir">Browse</a-button>
          </div>
          <p class="text-xs text-gray-500">Directory containing Whisper model files</p>
        </div>

        <!-- Batch processing options -->
        <div class="space-y-2">
          <div v-if="transcriptionEngine === 'whisper'" class="flex items-center space-x-2">
            <a-checkbox v-model:checked="convertToWav">Convert to high-quality WAV first</a-checkbox>
            <a-tooltip title="Recommended for better transcription quality">
              <InfoCircleOutlined />
            </a-tooltip>
          </div>

          <div class="flex items-center space-x-2">
            <a-checkbox v-model:checked="stopOnError">Stop batch processing on first error</a-checkbox>
            <a-tooltip title="If unchecked, will continue processing other files even if one fails">
              <InfoCircleOutlined />
            </a-tooltip>
          </div>

          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium">Concurrent processes:</label>
            <a-input-number
              v-model:value="maxConcurrentProcesses"
              :min="1"
              :max="transcriptionEngine === 'edgetts' ? 2 : 4"
              style="width: 80px"
            />
            <a-tooltip :title="`Number of files to process simultaneously (1-${transcriptionEngine === 'edgetts' ? 2 : 4})`">
              <InfoCircleOutlined />
            </a-tooltip>
          </div>

          <!-- EdgeTTS specific options -->
          <div v-if="transcriptionEngine === 'edgetts'" class="space-y-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div class="flex items-center space-x-2">
              <InfoCircleOutlined class="text-blue-500" />
              <span class="text-sm text-blue-700 font-medium">EdgeTTS Options</span>
            </div>
            <div class="flex items-center space-x-2">
              <a-checkbox v-model:checked="skipVocalSeparation">Skip vocal separation (faster)</a-checkbox>
              <a-tooltip title="EdgeTTS can handle mixed audio better than Whisper">
                <InfoCircleOutlined />
              </a-tooltip>
            </div>
            <p class="text-xs text-blue-600">
              EdgeTTS is an online service that may be faster but requires internet connection.
            </p>
          </div>
        </div>
      </div>

      <!-- Process buttons -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          <span v-if="isProcessing">
            Processing {{ currentlyProcessing.length }} files...
          </span>
          <span v-else-if="processedCount > 0">
            {{ processedCount }} files completed, {{ errorCount }} errors
          </span>
        </div>
        
        <div class="space-x-2">
          <a-button
            v-if="isProcessing"
            danger
            @click="stopAllProcessing"
          >
            Stop All Processing
          </a-button>
          <a-button
            type="primary"
            @click="processAllMedia"
            :loading="isProcessing"
            :disabled="mediaFiles.length === 0 || isProcessing || (transcriptionEngine === 'whisper' && !srtStore.modelDir)"
          >
            {{ isProcessing ? 'Processing...' : `${transcriptionEngine === 'edgetts' ? 'Transcribe with EdgeTTS' : 'Transcribe with Whisper'} (${pendingFiles.length} files)` }}
          </a-button>
        </div>
      </div>

      <!-- Overall processing status -->
      <div v-if="isProcessing" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="space-y-2">
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
            <p class="text-blue-700">
              Batch processing in progress... 
              ({{ processedCount + currentlyProcessing.length }}/{{ mediaFiles.length }})
            </p>
          </div>
          <div class="w-full bg-blue-200 rounded-full h-2">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(processedCount / mediaFiles.length) * 100}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Batch results summary -->
      <div v-if="!isProcessing && processedCount > 0" class="mt-4 p-3 rounded-md"
           :class="errorCount > 0 ? 'bg-yellow-50 border border-yellow-200' : 'bg-green-50 border border-green-200'">
        <div class="flex justify-between items-center">
          <div>
            <p class="font-medium"
               :class="errorCount > 0 ? 'text-yellow-700' : 'text-green-700'">
              Batch processing completed!
            </p>
            <p class="text-sm text-gray-600">
              {{ completedFiles.length }} files completed successfully
              <span v-if="errorCount > 0">, {{ errorCount }} errors</span>
            </p>
          </div>
          <div class="space-x-2">
            <a-button 
              v-if="completedFiles.length > 0"
              type="primary" 
              @click="openOutputFolder"
            >
              Open Output Folder
            </a-button>
            <a-button 
              v-if="completedFiles.length > 0"
              @click="importAllToSrtTable"
            >
              Import All to SRT Table
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import DragDropUpload from './DragDropUpload.vue';
import { useSRTStore } from '../stores/srtStore';
import { useTTSStore } from '../stores/ttsStore';
import { edgeTts } from '@/logic/edgeTts';

const srtStore = useSRTStore();
const ttsStore = useTTSStore();

// Reactive state
const mediaFiles = ref([]);
const language = ref('zh');
const model = ref('large-v3');
const transcriptionEngine = ref('whisper');
const edgeTtsModel = ref('large-v3');
const convertToWav = ref(true);
const skipVocalSeparation = ref(false);
const stopOnError = ref(false);
const maxConcurrentProcesses = ref(1);
const isProcessing = ref(false);
const currentlyProcessing = ref([]);
const currentPreviewFile = ref(null);

// Computed properties
const pendingFiles = computed(() => 
  mediaFiles.value.filter(file => file.status === 'pending')
);

const completedFiles = computed(() => 
  mediaFiles.value.filter(file => file.status === 'completed')
);

const processedCount = computed(() => 
  mediaFiles.value.filter(file => file.status === 'completed' || file.status === 'error').length
);

const errorCount = computed(() => 
  mediaFiles.value.filter(file => file.status === 'error').length
);

// Helper functions
const isVideoFile = (file) => {
  return file && file.type && file.type.startsWith('video/');
};

const isAudioFile = (file) => {
  return file && file.type && file.type.startsWith('audio/');
};

const isMediaFile = (file) => {
  return file && file.type && (file.type.startsWith('video/') || file.type.startsWith('audio/'));
};

// Handle engine change
const onEngineChange = (engine) => {
  // Adjust max concurrent processes based on engine
  if (engine === 'edgetts') {
    maxConcurrentProcesses.value = Math.min(maxConcurrentProcesses.value, 2);
  }
};

// Handle media selection
const handleMediaSelected = (files) => {
  if (files && files.length > 0) {
    const validFiles = files.filter(file => isMediaFile(file));
    if (validFiles.length !== files.length) {
      message.warning(`${files.length - validFiles.length} non-media files were skipped`);
    }
    addFiles(validFiles);
  }
};

// Add files to the list
const addFiles = (files) => {
  const newFiles = files.map(file => ({
    name: file.name,
    type: file.type,
    path: file.path,
    url: `file://${file.path}`,
    status: 'pending',
    processingStatus: '',
    processId: null,
    srtPath: null,
    error: null
  }));

  mediaFiles.value.push(...newFiles);
  
  // Set first file as preview if none selected
  if (!currentPreviewFile.value && newFiles.length > 0) {
    currentPreviewFile.value = newFiles[0];
  }

  message.success(`Added ${newFiles.length} files`);
};

// Select folder
const selectFolder = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      const folderPath = result.filePaths[0];
      
      // Get all media files from the folder
      const folderResult = await window.electronAPI.invoke('get-media-files-from-folder', folderPath);
      
      if (folderResult.success && folderResult.files.length > 0) {
        // Convert to file objects
        const files = folderResult.files.map(filePath => {
          const name = filePath.split(/[\/\\]/).pop();
          return {
            name,
            path: filePath,
            type: getFileType(name)
          };
        });
        
        addFiles(files);
      } else {
        message.warning('No media files found in the selected folder');
      }
    }
  } catch (err) {
    console.error('Error selecting folder:', err);
    message.error('Error selecting folder');
  }
};

// Get file type based on extension
const getFileType = (filename) => {
  const ext = filename.split('.').pop().toLowerCase();
  const videoExts = ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', 'wmv'];
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'];
  
  if (videoExts.includes(ext)) return `video/${ext}`;
  if (audioExts.includes(ext)) return `audio/${ext}`;
  return 'application/octet-stream';
};

// Add more files
const addMoreFiles = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Media Files', extensions: ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', 'wmv', 'mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'] }
      ]
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      const files = result.filePaths.map(filePath => {
        const name = filePath.split(/[\/\\]/).pop();
        return {
          name,
          path: filePath,
          type: getFileType(name)
        };
      });
      
      addFiles(files);
    }
  } catch (err) {
    console.error('Error selecting files:', err);
    message.error('Error selecting files');
  }
};

// Remove file
const removeFile = (index) => {
  const file = mediaFiles.value[index];
  
  // Stop processing if it's currently being processed
  if (file.status === 'processing' && file.processId) {
    stopProcessing(file.processId);
  }
  
  // Revoke URL to avoid memory leaks
  if (file.url) {
    URL.revokeObjectURL(file.url);
  }
  
  mediaFiles.value.splice(index, 1);
  
  // Update preview if this was the preview file
  if (currentPreviewFile.value === file) {
    currentPreviewFile.value = mediaFiles.value.length > 0 ? mediaFiles.value[0] : null;
  }
};

// Reset all media
const resetMedia = () => {
  // Stop all processing
  if (isProcessing.value) {
    stopAllProcessing();
  }
  
  // Clean up URLs
  mediaFiles.value.forEach(file => {
    if (file.url) {
      URL.revokeObjectURL(file.url);
    }
  });
  
  mediaFiles.value = [];
  currentPreviewFile.value = null;
  currentlyProcessing.value = [];
};

// Select model directory
const selectModelDir = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      srtStore.modelDir = result.filePaths[0];
    }
  } catch (err) {
    console.error('Error selecting model directory:', err);
    message.error('Error selecting model directory');
  }
};

// Process single file with EdgeTTS
const processSingleFileWithEdgeTTS = async (file) => {
  try {
    file.status = 'processing';
    file.processingStatus = 'Starting EdgeTTS transcription...';

    let inputFile = file.path;

    // Step 1: Separate vocals using Demucs (optional for EdgeTTS)
    if (!skipVocalSeparation.value) {
      file.processingStatus = 'Separating vocals...';

      const res = await window.electronAPI.checkHtdemucsFileExists(inputFile);
      if (res.vocalsFile) {
        inputFile = res.vocalsFile;
        file.processingStatus = 'Vocals separated';
      } else {
        const demucsResult = await window.electronAPI.demucs({
          fileInput: inputFile
        });

        if (demucsResult.success) {
          file.processId = demucsResult.processId;
          inputFile = demucsResult.vocalsFile;

          await checkProcessStatus(file);
        } else {
          throw new Error(demucsResult.error || 'Failed to run Demucs');
        }
      }
    }

    // Step 2: Process with EdgeTTS
    file.processingStatus = 'Transcribing with EdgeTTS...';

    let edgeTtsResult = await edgeTts.audioToText(inputFile, language.value, edgeTtsModel.value, 'json');
    console.log('Raw edgeTtsResult:', edgeTtsResult);
    console.log('Type of edgeTtsResult:', typeof edgeTtsResult);

    // Handle different response formats
    if (typeof edgeTtsResult === 'string') {
      try {
        // Try to parse as JSON
        edgeTtsResult = JSON.parse(edgeTtsResult);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.log('Raw string content:', edgeTtsResult);

        // Try to extract JSON from wrapped text (e.g., if it's in code blocks)
        const jsonMatch = edgeTtsResult.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            edgeTtsResult = JSON.parse(jsonMatch[0]);
            console.log('Extracted and parsed JSON:', edgeTtsResult);
          } catch (extractError) {
            throw new Error(`Failed to parse EdgeTTS response: ${parseError.message}`);
          }
        } else {
          throw new Error(`EdgeTTS response is not valid JSON: ${edgeTtsResult}`);
        }
      }
    }

    console.log('Final edgeTtsResult:', edgeTtsResult);
    if (edgeTtsResult && edgeTtsResult.segments && Array.isArray(edgeTtsResult.segments)) {
      console.log(`Found ${edgeTtsResult.segments.length} segments`);

      // Convert EdgeTTS response to SRT format
      const srtContent = convertEdgeTtsToSrt(edgeTtsResult);
      console.log('Generated SRT content:', srtContent.substring(0, 200) + '...');

      // Generate SRT file path
      const srtPath = inputFile.replace(/\.[^/.]+$/, '.srt');

      // Save SRT file
      const saveResult = await window.electronAPI.writeFile({
        filePath: srtPath,
        content: srtContent
      });

      if (saveResult.success) {
        file.srtPath = srtPath;
        file.status = 'completed';
        file.processingStatus = 'Completed';
        console.log('SRT file saved successfully:', srtPath);
      } else {
        throw new Error('Failed to save SRT file: ' + (saveResult.error || 'Unknown error'));
      }
    } else {
      console.error('Invalid EdgeTTS response structure:', edgeTtsResult);
      throw new Error(`No valid transcription segments received from EdgeTTS. Response: ${JSON.stringify(edgeTtsResult)}`);
    }
  } catch (err) {
    console.error('Error processing file with EdgeTTS:', err);
    file.status = 'error';
    file.error = err.message || 'Unknown error occurred';
    file.processingStatus = 'Error';

    if (stopOnError.value) {
      throw err; // Re-throw to stop batch processing
    }
  } finally {
    // Remove from currently processing
    const index = currentlyProcessing.value.findIndex(f => f === file);
    if (index !== -1) {
      currentlyProcessing.value.splice(index, 1);
    }
  }
};

// Convert EdgeTTS response to SRT format
const convertEdgeTtsToSrt = (edgeTtsResponse) => {
  if (!edgeTtsResponse.segments || !Array.isArray(edgeTtsResponse.segments)) {
    throw new Error('Invalid EdgeTTS response format');
  }

  let srtContent = '';

  edgeTtsResponse.segments.forEach((segment, index) => {
    const startTime = formatTimeForSrt(segment.start);
    const endTime = formatTimeForSrt(segment.end);

    srtContent += `${index + 1}\n`;
    srtContent += `${startTime} --> ${endTime}\n`;
    srtContent += `${segment.text.trim()}\n\n`;
  });

  return srtContent.trim();
};

// Format time for SRT (HH:MM:SS,mmm)
const formatTimeForSrt = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

// Process single file (unified method)
const processSingleFile = async (file) => {
  if (transcriptionEngine.value === 'edgetts') {
    return await processSingleFileWithEdgeTTS(file);
  } else {
    return await processSingleFileWithWhisper(file);
  }
};

// Process single file with Whisper (original method)
const processSingleFileWithWhisper = async (file) => {
  try {
    file.status = 'processing';
    file.processingStatus = 'Starting...';

    let inputFile = file.path;

    // Step 1: Separate vocals using Demucs
    file.processingStatus = 'Separating vocals...';

    const res = await window.electronAPI.checkHtdemucsFileExists(inputFile);
    if (res.vocalsFile) {
      inputFile = res.vocalsFile;
      file.processingStatus = 'Vocals separated';
    } else {
      const demucsResult = await window.electronAPI.demucs({
        fileInput: inputFile
      });

      if (demucsResult.success) {
        file.processId = demucsResult.processId;
        inputFile = demucsResult.vocalsFile;

        await checkProcessStatus(file);
      } else {
        throw new Error(demucsResult.error || 'Failed to run Demucs');
      }
    }

    // Step 2: Process with Whisper
    file.processingStatus = 'Transcribing with Whisper...';

    const whisperResult = await window.electronAPI.processAudioWhisper({
      fileInput: inputFile,
      language: language.value,
      model: model.value,
      model_dir: srtStore.modelDir,
      outputFile: file.path
    });

    if (whisperResult.success) {
      file.processId = whisperResult.processId;
      file.srtPath = whisperResult.srtFilePath;

      await checkProcessStatus(file);

      file.status = 'completed';
      file.processingStatus = 'Completed';
    } else {
      throw new Error(whisperResult.error || 'Failed to process with Whisper');
    }
  } catch (err) {
    file.status = 'error';
    file.error = err.message || 'Unknown error occurred';
    file.processingStatus = 'Error';

    if (stopOnError.value) {
      throw err; // Re-throw to stop batch processing
    }
  } finally {
    // Remove from currently processing
    const index = currentlyProcessing.value.findIndex(f => f === file);
    if (index !== -1) {
      currentlyProcessing.value.splice(index, 1);
    }
  }
};

// Process all media files
const processAllMedia = async () => {
  if (pendingFiles.value.length === 0 || !srtStore.modelDir) return;

  isProcessing.value = true;
  currentlyProcessing.value = [];

  try {
    const filesToProcess = [...pendingFiles.value];
    let processedFiles = 0;

    // Process files in batches based on maxConcurrentProcesses
    while (filesToProcess.length > 0 && !stopOnError.value) {
      const batch = filesToProcess.splice(0, maxConcurrentProcesses.value);
      currentlyProcessing.value = [...batch];

      // Process batch concurrently
      const promises = batch.map(file => processSingleFile(file));
      
      try {
        await Promise.all(promises);
        processedFiles += batch.length;
      } catch (err) {
        if (stopOnError.value) {
          message.error('Batch processing stopped due to error: ' + err.message);
          break;
        }
      }
    }

    const completed = completedFiles.value.length;
    const errors = errorCount.value;
    
    if (errors === 0) {
      message.success(`All ${completed} files processed successfully!`);
    } else {
      message.warning(`Processing completed: ${completed} successful, ${errors} errors`);
    }
  } catch (err) {
    message.error('Batch processing error: ' + err.message);
  } finally {
    isProcessing.value = false;
    currentlyProcessing.value = [];
  }
};

// Stop processing for a specific file
const stopProcessing = async (processId) => {
  if (!processId) return;

  try {
    const result = await window.electronAPI.stopProcess(processId);
    if (!result.success) {
      console.error('Failed to stop process:', result.error);
    }
  } catch (err) {
    console.error('Error stopping process:', err);
  }
};

// Stop all processing
const stopAllProcessing = async () => {
  try {
    // Stop all currently processing files
    const stopPromises = currentlyProcessing.value
      .filter(file => file.processId)
      .map(file => stopProcessing(file.processId));
    
    await Promise.all(stopPromises);
    
    // Reset status for all processing files
    currentlyProcessing.value.forEach(file => {
      file.status = 'pending';
      file.processingStatus = '';
      file.processId = null;
    });
    
    isProcessing.value = false;
    currentlyProcessing.value = [];
    
    message.success('All processing stopped');
  } catch (err) {
    message.error('Error stopping processes: ' + err.message);
  }
};

// Check process status
const checkProcessStatus = async (file) => {
  if (!file.processId || file.status !== 'processing') return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      const isRunning = result.processes.some(p => p.processId === file.processId);

      if (!isRunning) {
        return; // Process completed
      } else {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await checkProcessStatus(file);
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await checkProcessStatus(file);
  }
};

// Open SRT file
const openSrtFile = async (srtPath) => {
  if (srtPath) {
    await window.electronAPI.openFile(srtPath);
  }
};

// Import single SRT to table
const importToSrtTable = async (srtPath) => {
  if (!srtPath) return;

  try {
    const result = await window.electronAPI.readFile({ filePath: srtPath });

    if (result.success) {
      const fileName = srtPath.split(/[\/\\]/).pop();
      const pseudoFile = {
        name: fileName,
        type: 'application/x-subrip',
        content: result.content,
        path: srtPath,
      };

      await srtStore.processSrtFile(pseudoFile);
      message.success(`Imported ${fileName} to SRT Table`);
    } else {
      throw new Error(result.error || 'Failed to read SRT file');
    }
  } catch (err) {
    message.error('Error importing to SRT Table: ' + err.message);
  }
};

// Import all completed SRT files to table
const importAllToSrtTable = async () => {
  const completedSrtFiles = completedFiles.value.filter(file => file.srtPath);
  
  if (completedSrtFiles.length === 0) {
    message.warning('No completed SRT files to import');
    return;
  }

  try {
    for (const file of completedSrtFiles) {
      await importToSrtTable(file.srtPath);
    }
    message.success(`Imported ${completedSrtFiles.length} SRT files to table`);
  } catch (err) {
    message.error('Error importing SRT files: ' + err.message);
  }
};

// Open output folder
const openOutputFolder = async () => {
  if (completedFiles.value.length > 0 && completedFiles.value[0].srtPath) {
    const folderPath = completedFiles.value[0].srtPath.replace(/[^\/\\]*$/, '');
    await window.electronAPI.openFile(folderPath);
  }
};

// Clean up on component unmount
onBeforeUnmount(() => {
  resetMedia();
});
</script>