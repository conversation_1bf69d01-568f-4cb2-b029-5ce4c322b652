<template>
  <a-drawer
    :open="visible"
    :title="t('voiceConfig.title')"
    placement="right"
    width="400"
    @close="onClose"
  >
    <div class="space-y-6">
      <!-- Language Selection -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {{ t('voiceConfig.language') }}
        </label>

        <a-select
          v-model:value="config.language"
          class="w-full"
          @change="onLanguageChange"
        >
          <a-select-option value="All">All Languages ({{ allVoicesWithEngine.length }} voices)</a-select-option>
          <a-select-option value="Tiếng Việt">Tiế<PERSON> Việ<PERSON></a-select-option>
          <a-select-option value="English">English</a-select-option>
          <a-select-option value="中文">中文</a-select-option>
        </a-select>
      </div>

      <!-- Voice 1 Configuration -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice1') }}
          </h3>
          <a-checkbox v-model:checked="config.voice1.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice1.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice1.speaker"
              class="w-full"
              :disabled="!config.voice1.enabled"
              @change="testConfig(config.voice1)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                <div class="flex items-center justify-between">
                  <span>{{ speaker.name }}</span>
                  <div class="flex items-center gap-1">
                    <a-tag :color="getEngineColor(speaker.engine)" size="small">
                      {{ speaker.engineName }}
                    </a-tag>
                    <a-tag v-if="speaker.gender" :color="speaker.gender === 'female' ? 'pink' : 'cyan'" size="small">
                      {{ speaker.gender }}
                    </a-tag>
                    <a-tag v-if="speaker.level" :color="getLevelColor(speaker.level)" size="small">
                      {{ speaker.level }}
                    </a-tag>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice1.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice1.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice1.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Voice 2 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice2') }}
          </h3>
          <a-checkbox v-model:checked="config.voice2.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice2.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice2.speaker"
              class="w-full"
              :disabled="!config.voice2.enabled"
              @change="testConfig(config.voice2)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                <div class="flex items-center justify-between">
                  <span>{{ speaker.name }}</span>
                  <div class="flex items-center gap-1">
                    <a-tag :color="getEngineColor(speaker.engine)" size="small">
                      {{ speaker.engineName }}
                    </a-tag>
                    <a-tag v-if="speaker.gender" :color="speaker.gender === 'female' ? 'pink' : 'cyan'" size="small">
                      {{ speaker.gender }}
                    </a-tag>
                    <a-tag v-if="speaker.level" :color="getLevelColor(speaker.level)" size="small">
                      {{ speaker.level }}
                    </a-tag>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 -->
         <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice2.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice2.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice2.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Voice 3 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice3') }}
          </h3>
          <a-checkbox v-model:checked="config.voice3.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice3.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice3.speaker"
              class="w-full"
              :disabled="!config.voice3.enabled"
              @change="testConfig(config.voice3)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                <div class="flex items-center justify-between">
                  <span>{{ speaker.name }}</span>
                  <div class="flex items-center gap-1">
                    <a-tag :color="getEngineColor(speaker.engine)" size="small">
                      {{ speaker.engineName }}
                    </a-tag>
                    <a-tag v-if="speaker.gender" :color="speaker.gender === 'female' ? 'pink' : 'cyan'" size="small">
                      {{ speaker.gender }}
                    </a-tag>
                    <a-tag v-if="speaker.level" :color="getLevelColor(speaker.level)" size="small">
                      {{ speaker.level }}
                    </a-tag>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice3.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice3.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice3.enabled"
            />
          </div>
        </div>
      </div>
      <!-- Voice 4 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice4') }}
          </h3>
          <a-checkbox v-model:checked="config.voice4.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice4.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice4.speaker"
              class="w-full"
              :disabled="!config.voice4.enabled"
              @change="testConfig(config.voice4)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                <div class="flex items-center justify-between">
                  <span>{{ speaker.name }}</span>
                  <div class="flex items-center gap-1">
                    <a-tag :color="getEngineColor(speaker.engine)" size="small">
                      {{ speaker.engineName }}
                    </a-tag>
                    <a-tag v-if="speaker.gender" :color="speaker.gender === 'female' ? 'pink' : 'cyan'" size="small">
                      {{ speaker.gender }}
                    </a-tag>
                    <a-tag v-if="speaker.level" :color="getLevelColor(speaker.level)" size="small">
                      {{ speaker.level }}
                    </a-tag>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice4.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice4.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice4.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice4.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice4.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice4.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice4.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice4.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice4.enabled"
            />
          </div>
        </div>
      </div>

            <!-- Voice 5 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice5') }}
          </h3>
          <a-checkbox v-model:checked="config.voice5.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice5.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice5.speaker"
              class="w-full"
              :disabled="!config.voice5.enabled"
              @change="testConfig(config.voice5)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                <div class="flex items-center justify-between">
                  <span>{{ speaker.name }}</span>
                  <div class="flex items-center gap-1">
                    <a-tag :color="getEngineColor(speaker.engine)" size="small">
                      {{ speaker.engineName }}
                    </a-tag>
                    <a-tag v-if="speaker.gender" :color="speaker.gender === 'female' ? 'pink' : 'cyan'" size="small">
                      {{ speaker.gender }}
                    </a-tag>
                    <a-tag v-if="speaker.level" :color="getLevelColor(speaker.level)" size="small">
                      {{ speaker.level }}
                    </a-tag>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice5.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice5.speed"
              :min="0"
              :max="2"
              :step="0.1"
              :disabled="!config.voice5.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice5.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice5.pitch"
              :min="pitch_rate_min"
              :max="pitch_rate_max"
              :step="1"
              :disabled="!config.voice5.enabled"
            />
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.reverb') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice5.reverb }}</span>
            </div>
            <a-slider
              v-model:value="config.voice5.reverb"
              :min="0"
              :max="100"
              :step="10"
              :disabled="!config.voice5.enabled"
            />
          </div>
        </div>
      </div>
      <!-- Volume Control -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-3">
          {{ t('voiceConfig.masterVolume') }}
        </h3>
        <div class="flex justify-between mb-1">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ t('voiceConfig.volume') }}
          </label>
          <span class="text-sm text-gray-500">{{ config.masterVolume }}%</span>
        </div>
        <a-slider
          v-model:value="config.masterVolume"
          :min="0"
          :max="200"
          :step="5"
        />
      </div>

      <!-- Apply Button -->
      <div class="flex justify-end">
        <a-button type="primary" @click="applyConfig">
          {{ t('common.apply') }}
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { useTTSStore } from '@/stores/ttsStore';
import { useI18n } from '@/i18n/i18n';
import edgeVoices from '@/assets/edge-voices.json';
import { message } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'apply']);
const ttsStore = useTTSStore();
const { t } = useI18n();

// Helper function to convert locale to language display name
const getLanguageFromLocale = (locale) => {
  const localeMap = {
    'vi-VN': 'Tiếng Việt',
    'en-US': 'English',
    'en-GB': 'English',
    'en-AU': 'English',
    'en-CA': 'English',
    'zh-CN': '中文',
    'zh-TW': '中文',
    'ja-JP': '日本語',
    'ko-KR': '한국어',
    'fr-FR': 'Français',
    'de-DE': 'Deutsch',
    'es-ES': 'Español',
    'it-IT': 'Italiano',
    'pt-BR': 'Português',
    'ru-RU': 'Русский',
    'ar-SA': 'العربية',
    'hi-IN': 'हिन्दी',
    'th-TH': 'ไทย'
  };

  // Try exact match first
  if (localeMap[locale]) {
    return localeMap[locale];
  }

  // Try language code match (e.g., 'en' from 'en-XX')
  const langCode = locale.split('-')[0];
  const matchingLocale = Object.keys(localeMap).find(key => key.startsWith(langCode + '-'));
  if (matchingLocale) {
    return localeMap[matchingLocale];
  }

  // Fallback to locale itself
  return locale;
};

// Reactive configuration object
const config = reactive({ ...ttsStore.selectedVoiceConfig });


const pitch_rate_max = 12;
const pitch_rate_min = -12;


// Get all voices from all engines with engine information - COMPUTED
const allVoicesWithEngine = computed(() => {
  const allVoices = [];

  // CapCut voices
  if (ttsStore.speakers && ttsStore.speakers.length > 0) {
    const capCutVoices = ttsStore.speakers.map(voice => ({
      id: voice.id,
      name: voice.name,
      engine: 'capcut',
      engineName: 'CapCut TTS',
      gender: voice.gender,
      language: voice.language
    }));
    allVoices.push(...capCutVoices);
  }
  // TikTok voices
  // if (ttsStore.speakers && ttsStore.speakers.length > 0) {
  //   const capCutVoices = ttsStore.speakers.map(voice => ({
  //     id: voice.id,
  //     name: voice.name,
  //     engine: 'tiktok',
  //     engineName: 'TikTok TTS',
  //     gender: voice.gender,
  //     language: voice.language
  //   }));
  //   allVoices.push(...capCutVoices);
  // }
  // OpenAI voices
  const languageMap = {
    'Tiếng Việt': 'vi',
    'English': 'en',
    '中文': 'zh'
  };

  Object.entries(languageMap).forEach(([displayLang, langCode]) => {
    const openaiVoices = ttsStore.getOpenAIVoicesByLanguage(langCode).map(voice => ({
      id: `openai_${voice.id}_${langCode}`,
      name: `${voice.name} (OpenAI)`,
      engine: 'openai',
      engineName: 'OpenAI TTS',
      originalId: voice.id,
      language: displayLang
    }));
    allVoices.push(...openaiVoices);
  });

  // Mimimax voices
  const mimimaxVoices = ttsStore.getMimimaxVoices().map(voice => ({
    id: `mimimax_${voice.id}`,
    name: `${voice.name} (Mimimax)`,
    engine: 'mimimax',
    engineName: 'Mimimax TTS',
    originalId: voice.id,
    gender: voice.gender,
    level: voice.isDefault ? 'DEFAULT' : 'CUSTOM',
    language: voice.language
  }));
  allVoices.push(...mimimaxVoices);

  // VBee voices
  const vbeeVoices = ttsStore.getVbeeVoices()
    .filter(voice => voice.active)
    .map(voice => ({
      id: `vbee_${voice.id}`,
      name: `${voice.name} (VBee)`,
      engine: 'vbee',
      engineName: 'VBee TTS',
      originalId: voice.id,
      gender: voice.gender,
      level: voice.level,
      language: 'Tiếng Việt'
    }));
  allVoices.push(...vbeeVoices);

  // Edge TTS voices
  const edgeTTSVoices = edgeVoices.map(voice => ({
    id: voice.ShortName,
    name: voice.FriendlyName,
    engine: 'edge',
    engineName: 'Edge TTS',
    originalId: voice.ShortName,
    gender: voice.Gender?.toLowerCase(),
    locale: voice.Locale,
    language: getLanguageFromLocale(voice.Locale)
  }));
  allVoices.push(...edgeTTSVoices);
  console.log('All voices:', mimimaxVoices);
  return allVoices;
});

// Filtered speakers based on selected language
const filteredSpeakers = computed(() => {
  const allVoices = allVoicesWithEngine.value;

  // Filter by language if specified
  if (config.language && config.language !== 'All') {
    const filtered = allVoices.filter(voice => {
      if (config.language === 'Tiếng Việt') {
        return voice.language === 'Tiếng Việt' ||
               voice.language === 'Vietnamese' || voice.language === 'vi' ||
               voice.name.includes('Việt') || 
               voice.name.includes('VN') ||
               voice.name.includes('Vietnamese') || 
               voice.engine === 'vbee';
      } else if (config.language === 'English') {
        return voice.language === 'English' || voice.language === 'en' ||
               voice.name.includes('EN') ||
               voice.name.includes('English');
      } else if (config.language === '中文') {
        return voice.language === '中文' ||
               voice.language === 'Chinese' || voice.language === 'zh' ||
               voice.name.includes('Chinese');
      }
      return true;
    });
    return filtered;
  }

  return allVoices;
});

// Helper functions for colors
const getEngineColor = (engine) => {
  const colors = {
    'capcut': 'blue',
    'tiktok': 'purple',
    'openai': 'green',
    'mimimax': 'orange',
    'vbee': 'red',
    'edge': 'cyan'
  };
  return colors[engine] || 'default';
};

const getLevelColor = (level) => {
  const colors = {
    'PREMIUM': 'gold',
    'STANDARD': 'blue',
    'DEFAULT': 'green',
    'CUSTOM': 'purple'
  };
  return colors[level] || 'default';
};

// Watch for language changes to update default speakers
watch(() => config.language, (newLanguage) => {
  if (filteredSpeakers.value.length > 0) {
    config.voice1.speaker = filteredSpeakers.value[0].id;
    config.voice2.speaker = filteredSpeakers.value.length > 1 ? filteredSpeakers.value[1].id : filteredSpeakers.value[0].id;
    config.voice3.speaker = filteredSpeakers.value.length > 2 ? filteredSpeakers.value[2].id : filteredSpeakers.value[0].id;
    config.voice4.speaker = filteredSpeakers.value.length > 3 ? filteredSpeakers.value[3].id : filteredSpeakers.value[0].id;
    config.voice5.speaker = filteredSpeakers.value.length > 4 ? filteredSpeakers.value[4].id : filteredSpeakers.value[0].id;
  }
});

// Handle language change
function onLanguageChange(value) {
  config.language = value;
}

// Close drawer
function onClose() {
  emit('close');
}

// Apply configuration
function applyConfig() {
  // console.log('Applying config:', config);
  ttsStore.selectedVoiceConfig = config;
  // emit('apply', { ...config });
  message.success(t('voiceConfig.configApplied'));
  emit('close');
}

onMounted(async () => {
  // Load CapCut speakers if not already loaded
  if (!ttsStore.speakers || ttsStore.speakers.length === 0) {
    console.log('Loading CapCut/TikTok speakers...');
    await ttsStore.fetchCapCutSpeakers();
  }

  // Initialize with current config
  applyConfig();
});


// test listener if config changed
async function testConfig(params) {
  const text =  `Tìm hiểu về các giọng nói tổng hợp khác nhau có sẵn để sử dụng trong Chuyển văn bản thành giọng nói`
      const audio_config= {}
      if(params.pitch > 0) audio_config.pitch_rate = params.pitch
      if(params.speech > 0) audio_config.speech_rate = params.speech

      try {
        let requestConfig = {
          text,
          speaker: params.speaker,
          typeEngine: ttsStore.typeEngine,
          audio_config
        };

        // Add OpenAI specific config if using OpenAI TTS
        if (ttsStore.typeEngine === 'openai') {
          ttsStore.openaiConfig.selectedVoice = params.speaker
          requestConfig.openaiConfig = {
            apiKey: ttsStore.openaiTTS.apiKey,
            baseURL: ttsStore.openaiTTS.baseURL,
            speed: params.speed || ttsStore.openaiTTS.speed,
            format: ttsStore.openaiTTS.format
          };
        }

        // Add Mimimax specific config if using Mimimax TTS
        if (ttsStore.typeEngine === 'mimimax') {
          ttsStore.mimimaxTTS.selectedVoice = params.speaker
          requestConfig.mimimaxConfig = {
            apiKey: ttsStore.mimimaxTTS.apiKey,
            groupId: ttsStore.mimimaxTTS.groupId,
            selectedVoice: ttsStore.mimimaxTTS.selectedVoice,
            voiceSettings: {
              speed: params.speed || ttsStore.mimimaxTTS.voiceSettings.speed,
              vol: ttsStore.mimimaxTTS.voiceSettings.vol,
              pitch: params.pitch || ttsStore.mimimaxTTS.voiceSettings.pitch
            },
            audioSettings: ttsStore.mimimaxTTS.audioSettings
          };
        }

        // const response = await electronAPI.generateTTS(JSON.parse(JSON.stringify(requestConfig)));

        // if (response.success) {
        //   const audio = new Audio(response.audioUrl);
        //   audio.play();
        // }else{
        //   message.error('Error generating TTS: ' + response.message);
        // }
      } catch (error) {
        console.error('Error generating TTS:', error);
        message.error('Error generating TTS: ' + error.message);
      } finally {
      }
}


</script>
