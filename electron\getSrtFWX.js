const { execFile } = require('child_process');
const path = require('path');
const fs = require('fs');
const { execWithLog, sanitizeDangerousCharacters } = require('./utils');


async function getSrtFWX(event,{fileInput, language = 'zh',model = 'large-v3',model_dir,outputFile }) {
  // const projectPath = global.ROOT_DIR || `${__dirname}/../`
  const dbPath = await S.db.Config.getValueByName('whisper_path')
  const exePath = path.join(dbPath, 'faster-whisper-xxl.exe');
  const outputDirectory = path.dirname(outputFile)
  console.log('exePath', exePath);
  console.log('outputDirectory', outputDirectory);
  event.sender.send('get-srt-task-res', { data: 'Running Faster Whisper...', code: 0 });
  fileInput = sanitizeDangerousCharacters(fileInput)
  const baseArgs = [
    fileInput,
    '--language', language,
    '--output_dir', outputDirectory,
    '--model', model,
    '--model_dir', model_dir,
    '--beam_size', '10',
    '--best_of', '5',
    '--patience', '1.0',
    '--length_penalty', '1.8',
    '--temperature', '0.4',
    '--compression_ratio_threshold', '2.4',
    '--logprob_threshold', '-1.0',
    '--no_speech_threshold', '1.0',
    '--condition_on_previous_text', 'False',
    '--initial_prompt', 'None',
    '--prefix', 'None',
    '--suppress_blank', 'False',
    '--suppress_tokens', '-1',
    '--without_timestamps', 'False',
    '--max_initial_timestamp', '1.0',
    '--word_timestamps', 'False',
    '--prepend_punctuations', `'¿([{-"`,
    '--append_punctuations', `'.。,，!！?？:：")]}、`,
    '--repetition_penalty', '1.0',
    '--no_repeat_ngram_size', '0',
    '--prompt_reset_on_temperature', '0.5'
  ];
  // if language is auto, remove --language argument
  if (language === 'auto') {
    baseArgs.splice(baseArgs.indexOf('--language'), 2);
  }
  const cb = (done)=>{
    if(done){
      // cp file from htdemucs to outputDirectory
      const baseNameInput = path.basename(fileInput, path.extname(fileInput));
      const baseNameOutput = path.basename(outputFile, path.extname(outputFile));
      const sourceFile = path.join(outputDirectory, `${baseNameInput}.srt`);
      const destinationFile = path.join(outputDirectory, `${baseNameOutput}.srt`);
      // if file not exist, return
      if(!fs.existsSync(sourceFile)){
        event.sender.send('get-srt-task-res', { data: `File not found: ${sourceFile}`, code: 1 });
        return;
      }
      fs.renameSync(sourceFile, destinationFile);
    }


  }
//   let result = await runExternalProcess(exePath, baseArgs);
  let result = await execWithLog.bind({type: 'get-srt-task-res',cb})(event, exePath, baseArgs);

  if (result.code !== 0) {
    console.warn('Retrying with --device cpu');
    event.sender.send('get-srt-task-res', { data: 'Retrying with --device cpu', code: 1 });
    const retryArgs = [...baseArgs];
    retryArgs.splice(2, 0, '--device', 'cpu');
    result = await execWithLog.bind({type: 'get-srt-task-res',cb})(event, exePath, retryArgs);
    console.log('result', result);
    event.sender.send('get-srt-task-res', { data: 'Done', code: 0 });
  }

  if (result.code !== 0) {
    console.error('Execution failed:', result.stderr);
  }

  return result;
}

module.exports = getSrtFWX

// getSrtFWX({
//   fileInput: "F:\\ReviewDao\\11-05\\ran-quy\\qqq-80.mp4",
//   language: 'zh',
//   model: 'large-v3',
//   model_dir: 'D:\\AI-SD\\qbwh_latest_version\\qbwh_latest_version\\Faster-Whisper-XXL\\_models'
// }).then(result => {
//   console.log(result);
// })