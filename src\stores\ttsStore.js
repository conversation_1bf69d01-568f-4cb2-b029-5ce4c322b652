import { defineStore } from 'pinia';
import axios from 'axios';
import { message } from 'ant-design-vue';
import debounce from 'lodash.debounce'
import { parseSRT } from '../lib/utils';
import { getOpenAIModels, getGeminiModels, validateApiKey } from '../lib/apiUtils';
import voiceVbees from '../lib/vbeeVoices';

const debouncedStorage = {
  getItem: (key) => localStorage.getItem(key),
  setItem: debounce(
    (key, value) => localStorage.setItem(key, value),
    300
  ),
  removeItem: (key) => localStorage.removeItem(key),
}

export const useTTSStore = defineStore('tts', {
  state: () => ({
    cookie: '',//localStorage.getItem('cookie') || '',
    speakers: [],
    selectedSpeaker: 'BV075_streaming',//localStorage.getItem('selectedSpeaker') || 'BV075_streaming',
    workspaceId: '',//localStorage.getItem('workspaceId') || '7417632273850214593',
    tiktokSessionId:'',
    text: '',
    audioUrl: '',
    audioDuration: 0,
    isLoading: false,
    error: null,
    generatedAudios: [],//JSON.parse(localStorage.getItem('generatedAudios') || '[]'),
    srtFile: null,
    srtContent: null,
    srtAudios: [],
    processingStatus: '',
    typeEngine: 'capcut', // 'capcut' or 'volcengine' or 'openai'
    language: 'vi',
    params: {
      speech_rate: 0,
      pitch_rate: 0,
      volume: 1,
      sample_rate: 24000,
    },
    srtLists: [],
    currentSrtList: null,
    activeTab: 'tts',
    activeTabChild: 'subtitle',
    // AI Service Configuration
    aiServices: {
      openai: {
        name: 'OpenAI',
        apiKey: '',
        baseURL: 'https://api.openai.com/v1',
        modelsEndpoint: '/models',
        models: [], // Will be loaded dynamically
        defaultModels: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4o', 'gpt-4o-mini'], // Fallback
        enabled: false,
        loading: false
      },
      deepseek: {
        name: 'DeepSeek',
        apiKey: '',
        baseURL: 'https://api.deepseek.com/v1',
        modelsEndpoint: '/models',
        models: [],
        defaultModels: ['deepseek-chat', 'deepseek-reasoner'],
        enabled: true,
        loading: false
      },
      gemini: {
        name: 'Google Gemini',
        apiKey: '',
        baseURL: 'https://generativelanguage.googleapis.com/v1beta',
        modelsEndpoint: '/models',
        models: [],
        defaultModels: ['gemini-2.0-flash-exp', 'gemini-1.5-pro', 'gemini-1.5-flash'],
        enabled: false,
        loading: false
      },
      claude: {
        name: 'Anthropic Claude',
        apiKey: '',
        baseURL: 'https://api.anthropic.com/v1',
        modelsEndpoint: '/models',
        models: [],
        defaultModels: ['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022'],
        enabled: false,
        loading: false
      },
      openrouter: {
        name: 'OpenRouter',
        apiKey: '',
        baseURL: 'https://openrouter.ai/api/v1',
        modelsEndpoint: '/models',
        models: [],
        freeModels: [],
        paidModels: [],
        defaultModels: ['deepseek/deepseek-r1:free', 'openai/gpt-4o', 'anthropic/claude-3.5-sonnet', 'google/gemini-pro-1.5', 'meta-llama/llama-3.1-8b-instruct:free'],
        defaultFreeModels: ['deepseek/deepseek-r1:free', 'meta-llama/llama-3.1-8b-instruct:free', 'microsoft/phi-3-mini-128k-instruct:free', 'google/gemma-7b-it:free'],
        showFreeOnly: false,
        enabled: false,
        loading: false
      },
       nebula: {
        name: 'Nebula Block',
        apiKey: '',
        baseURL: 'https://inference.nebulablock.com/v1',
        modelsEndpoint: '/models',
        models: [],
        freeModels: [],
        paidModels: [],
        defaultModels: ['openai/gpt-4o-mini', 'deepseek-ai/DeepSeek-R1-0528:free', 'deepseek-ai/DeepSeek-V3-0324:free', 'deepseek-ai/DeepSeek-R1:free', 'gemini/gemini-2.0-flash'],
        defaultFreeModels: ['openai/gpt-4o-mini', 'deepseek-ai/DeepSeek-R1-0528:free', 'deepseek-ai/DeepSeek-V3-0324:free', 'deepseek-ai/DeepSeek-R1:free'],
        showFreeOnly: false,
        enabled: false,
        loading: false
      }
    },
    selectedAiService: 'deepseek',
    selectedModel: 'deepseek-chat',
    // Legacy support
    openaiKey: '',
    deepseekKey: '',
    geminiKey: '',
    model: 'deepseek-chat',
    terminologyDictionary: {},
    selectedVoiceConfig:{
      language: 'Tiếng Việt',
      voice1: {
        enabled: true,
        speaker: 'BV075_streaming',
        speed: 1,
        pitch: 0,
        rate: 1,
      },
      voice2: {
        enabled: true,
        speaker: 'BV074_streaming',
        speed: 1,
        pitch: 0,
        rate: 1,
      },
      voice3: {
        enabled: true,
        speaker: 'BV562_streaming',
        speed: 1,
        pitch: 0,
        rate: 1,
      },
      voice4: {
        enabled: true,
        speaker: 'BV560_streaming',
        speed: 1,
        pitch: 0,
        rate: 1,
      },
      voice5: {
        enabled: true,
        speaker: 'BV421_vivn_streaming',
        speed: 1,
        pitch: 0,
        rate: 1,
      },
      masterVolume: 100
    },
    // OpenAI TTS Configuration
    openaiTTS: {
      enabled: false,
      apiKey: '',
      baseURL: 'https://api.openai.com/v1',
      selectedVoice: 'alloy',
      selectedLanguage: 'en',
      voices: {
        en: [
          { id: 'alloy', name: 'Alloy (English)', language: 'en' },
          { id: 'echo', name: 'Echo (English)', language: 'en' },
          { id: 'fable', name: 'Fable (English)', language: 'en' },
          { id: 'onyx', name: 'Onyx (English)', language: 'en' },
          { id: 'nova', name: 'Nova (English)', language: 'en' },
          { id: 'shimmer', name: 'Shimmer (English)', language: 'en' }
        ],
        vi: [
          { id: 'alloy', name: 'Alloy (Vietnamese)', language: 'vi' },
          { id: 'echo', name: 'Echo (Vietnamese)', language: 'vi' },
          { id: 'fable', name: 'Fable (Vietnamese)', language: 'vi' },
          { id: 'onyx', name: 'Onyx (Vietnamese)', language: 'vi' },
          { id: 'nova', name: 'Nova (Vietnamese)', language: 'vi' },
          { id: 'shimmer', name: 'Shimmer (Vietnamese)', language: 'vi' }
        ],
        zh: [
          { id: 'alloy', name: 'Alloy (Chinese)', language: 'zh' },
          { id: 'echo', name: 'Echo (Chinese)', language: 'zh' },
          { id: 'fable', name: 'Fable (Chinese)', language: 'zh' },
          { id: 'onyx', name: 'Onyx (Chinese)', language: 'zh' },
          { id: 'nova', name: 'Nova (Chinese)', language: 'zh' },
          { id: 'shimmer', name: 'Shimmer (Chinese)', language: 'zh' }
        ]
      },
      speed: 1.0,
      format: 'mp3'
    },
    // VBee TTS Configuration
    vbee: {
      enabled: false,
      apiKey: '',
      baseURL: 'https://vbee.vn/api/v1',
      selectedVoice: '61947d641c159f3c2c313de2', // Default to first voice (HN - Ngọc Huyền)
      selectedLanguage: 'vi-VN',
      voices: voiceVbees,
      test: false,
      voiceSettings: {
        speed: 1,
        pitch: 0,
        volume: 1
      },
      audioSettings: {
        sample_rate: 22050,
        format: 'mp3'
      }
    },
    // Mimimax TTS Configuration
    mimimaxTTS: {
      enabled: false,
      apiKey: '',
      baseURL: 'https://api.minimax.io/v1',
      groupId: '',
      selectedVoice: 'moss_audio_5214a9b1-1545-11f0-9885-1a8691f98463',
      selectedLanguage: 'vi',
      model: 'speech-02-hd', // or 'speech-02-turbo'
      voices: {
        default: [
          {
            id: 'moss_audio_5214a9b1-1545-11f0-9885-1a8691f98463',
            name: 'Female Voice (Default)',
            language: 'vi',
            gender: 'female',
            isDefault: true
          },
          {
            id: 'moss_audio_e662b4eb-4fe9-11f0-9e4a-3ee691490080',
            name: 'Male Voice (Default)',
            language: 'vi',
            gender: 'male',
            isDefault: true
          }
        ],
        custom: [] // Custom voice clones will be stored here
      },
      voiceSettings: {
        speed: 1,
        vol: 1,
        pitch: 0
      },
      audioSettings: {
        sample_rate: 32000,
        bitrate: 128000,
        format: 'mp3',
        channel: 1
      }
    },
    concurrency: 1,
    timeOut: 30000, // Default timeout for API requests
    // Novel translation settings
    novelTranslation: {
      memory: '', // Memory for novel translation context
      progress: {}, // Progress tracking for each SRT list
      isTranslating: false,
      currentBatch: 0,
      totalBatches: 0
    },
    batchSize: 20,
    
  }),
  persist: {
    storage: debouncedStorage,
    pick: ['cookie', 'selectedSpeaker', 'workspaceId', 'generatedAudios','typeEngine', 'srtLists', 'tiktokSessionId', 'aiServices', 'selectedAiService', 'selectedModel', 'openaiKey', 'deepseekKey', 'geminiKey', 'model','activeTabChild','selectedVoiceConfig','concurrency','openaiTTS','novelTranslation','mimimaxTTS','vbee','batchSize']
  },
  actions: {
    setCookie(cookie) {
      this.cookie = cookie;
      // localStorage.setItem('cookie', cookie);
      
    },
    
    setSelectedSpeaker(speaker) {
      this.selectedSpeaker = speaker;
      // localStorage.setItem('selectedSpeaker', speaker);
    },
    
    setWorkspaceId(id) {
      this.workspaceId = id;
      // localStorage.setItem('workspaceId', id);
    },
    setTiktokSessionId(id) {
      this.tiktokSessionId = id;
    },

    // Get tiktokSessionId from database
    async getTiktokSessionIdFromDB() {
      try {
        const sessionId = await electronAPI.invoke("database", 'Config.getValueByName', "tiktokSessionId");
        if (sessionId) {
          this.tiktokSessionId = sessionId;
        }
        return sessionId;
      } catch (error) {
        console.error('Error getting tiktokSessionId from database:', error);
        return '';
      }
    },

    // Save tiktokSessionId to database
    async saveTiktokSessionIdToDB(sessionId) {
      try {
        await electronAPI.invoke("database", 'Config.updateValueByName', "tiktokSessionId", sessionId);
        this.tiktokSessionId = sessionId;
        return true;
      } catch (error) {
        console.error('Error saving tiktokSessionId to database:', error);
        return false;
      }
    },
    setText(text) {
      this.text = text;
    },
    setTypeEngine(engine) {
      this.typeEngine = engine;
    },
    updateTTSParams (params) {
      if(params.speech_rate) this.params.speech_rate = params.speech_rate;
      if(params.pitch_rate) this.params.pitch_rate = params.pitch_rate;
      if(params.volume) this.params.volume = params.volume;
      if(params.sample_rate) this.params.sample_rate = params.sample_rate;
    },
    async fetchSpeakers() {
      try {
        const response = await electronAPI.getSpeakers(this.typeEngine); //axios.get('http://localhost:4200/api/tts/speakers');

        if (response.success) {
          this.speakers = response.data;
          this.selectedSpeaker = this.speakers[0].id;
        } else {
          message.error('Error fetching speakers: ' + response.message);
        }
      } catch (error) {
        this.error = error.message;
        console.error('Error fetching speakers:', error);
      }
    },

    // Fetch CapCut/TikTok speakers specifically
    async fetchCapCutSpeakers() {
      try {
        console.log('Fetching CapCut speakers...');
        const response = await electronAPI.getSpeakers('capcut');

        if (response.success) {
          this.speakers = response.data;
          console.log('CapCut speakers loaded:', this.speakers.length);
          if (this.speakers.length > 0 && !this.selectedSpeaker) {
            this.selectedSpeaker = this.speakers[0].id;
          }
        } else {
          console.error('Error fetching CapCut speakers:', response.message);
        }
      } catch (error) {
        console.error('Error fetching CapCut speakers:', error);
      }
    },
    
    async generateTTS() {
      this.isLoading = true;
      this.error = null;
      const audio_config= {}
      if(this.params.pitch_rate > 0) audio_config.pitch_rate = this.params.pitch_rate
      if(this.params.speech_rate > 0) audio_config.speech_rate = this.params.speech_rate
      
      try {
        let requestConfig = {
          text: this.text,
          speaker: this.selectedSpeaker,
          workspaceId: this.workspaceId,
          cookie: this.cookie,
          typeEngine: this.typeEngine,
          language: this.language,
          audio_config
        };

        // Add OpenAI specific config if using OpenAI TTS
        if (this.typeEngine === 'openai') {
          requestConfig.openaiConfig = {
            apiKey: this.openaiTTS.apiKey,
            baseURL: this.openaiTTS.baseURL,
            speed: this.openaiTTS.speed,
            format: this.openaiTTS.format
          };
        }

        // Add Mimimax specific config if using Mimimax TTS
        if (this.typeEngine === 'mimimax') {
          requestConfig.mimimaxConfig = {
            apiKey: this.mimimaxTTS.apiKey,
            groupId: this.mimimaxTTS.groupId,
            selectedVoice: this.selectedSpeaker,
            voiceSettings: this.mimimaxTTS.voiceSettings,
            audioSettings: this.mimimaxTTS.audioSettings
          };
        }

        // Add VBee specific config if using VBee TTS
        if (this.typeEngine === 'vbee') {
          requestConfig.vbeeConfig = {
            apiKey: this.vbee.apiKey,
            baseURL: this.vbee.baseURL,
            selectedVoice: this.selectedSpeaker,
            voiceSettings: this.vbee.voiceSettings,
            audioSettings: this.vbee.audioSettings
          };
        }

        const response = await electronAPI.generateTTS(JSON.parse(JSON.stringify(requestConfig)));
        
        if (response.success) {
          this.audioUrl = response.audioUrl;
          this.audioDuration = response.duration;
          
          // Add to generated audios
          const newAudio = {
            id: Date.now(),
            text: this.text,
            speaker: this.selectedSpeaker,
            url: this.audioUrl,
            duration: this.audioDuration,
            timestamp: new Date().toISOString(),
            voice: this.speakers.find(s => s.id === this.selectedSpeaker)?.name || ''
          };
          
          this.generatedAudios.push(newAudio);
          // localStorage.setItem('generatedAudios', JSON.stringify(this.generatedAudios));
        }else{
          message.error('Error generating TTS: ' + response.message);
        }
      } catch (error) {
        this.error = error.message;
        console.error('Error generating TTS:', error);
        message.error('Error generating TTS: ' + error.message);
      } finally {
        this.isLoading = false;
      }
    },
    
    async processSRTFile(file) {
      this.isLoading = true;
      this.error = null;
      this.srtFile = file;
      this.processingStatus = 'Reading SRT file...';
      
      try {
        // Read the SRT file
        const reader = new FileReader();
        const content = await new Promise((resolve, reject) => {
          reader.onload = (e) => resolve(e.target.result);
          reader.onerror = (e) => reject(e);
          reader.readAsText(file);
        });
        
        this.srtContent = content;
        
        // Parse SRT content
        const srtItems = parseSRT(content);
        this.processingStatus = `Parsed ${srtItems.length} items from SRT file`;
        
        // Generate TTS for each item
        this.srtAudios = [];
        for (let i = 0; i < srtItems.length; i++) {
          const item = srtItems[i];
          this.processingStatus = `Generating TTS for item ${i+1}/${srtItems.length}...`;
          
          try {
            const response = await electronAPI.processSRT(/*//axios.post('http://localhost:4200/api/tts/generate',*/ {
              text: item.text,
              speaker: this.selectedSpeaker,
              workspaceId: this.workspaceId,
              cookie: this.cookie
            });
            
            if (response.success) {
              this.srtAudios.push({
                ...item,
                audioUrl: response.audioUrl,
                duration: response.duration
              });
            }
          } catch (error) {
            console.error(`Error generating TTS for item ${i+1}:`, error);
          }
        }
        
        this.processingStatus = 'All TTS items generated successfully';
      } catch (error) {
        this.error = error.message;
        console.error('Error processing SRT file:', error);
      } finally {
        this.isLoading = false;
      }
    },
    // openai, deepseek, gemini set
    setOpenaiKey(key) {
      this.openaiKey = key;
    },
    setDeepseekKey(key) {
      this.deepseekKey = key;
    },
    setGeminiKey(key) {
      this.geminiKey = key;
    },
    setModel(model) {
      this.model = model;
    },

    // AI Service Management
    setAiServiceConfig(serviceKey, config) {
      if (this.aiServices[serviceKey]) {
        this.aiServices[serviceKey] = { ...this.aiServices[serviceKey], ...config };
      }
    },

    setSelectedAiService(serviceKey) {
      if (this.aiServices[serviceKey]) {
        this.selectedAiService = serviceKey;
        // Auto-select first available model
        const service = this.aiServices[serviceKey];
        if (service.models && service.models.length > 0) {
          this.selectedModel = service.models[0];
        }
        // Update legacy model field
        this.model = this.selectedModel;
      }
    },

    setSelectedModel(model) {
      this.selectedModel = model;
      this.model = model; // Keep legacy support
    },

    enableAiService(serviceKey, enabled = true) {
      if (this.aiServices[serviceKey]) {
        this.aiServices[serviceKey].enabled = enabled;
      }
    },

    getActiveAiService() {
      return {
        ...this.aiServices[this.selectedAiService],
        model: this.model
      }
    },

    getAvailableModels() {
      const service = this.getActiveAiService();
      if (!service) return [];

      // For OpenRouter, filter by free/paid preference
      if (this.selectedAiService === 'openrouter' && service.showFreeOnly) {
        return service.freeModels.length > 0 ? service.freeModels : service.defaultFreeModels;
      }

      // Return loaded models or fallback to default models
      return service.models.length > 0 ? service.models : service.defaultModels;
    },

    // Toggle free-only mode for OpenRouter
    toggleOpenRouterFreeOnly(showFreeOnly) {
      if (this.aiServices.openrouter) {
        this.aiServices.openrouter.showFreeOnly = showFreeOnly;

        // If switching to free-only and current model is paid, switch to first free model
        if (showFreeOnly && this.selectedAiService === 'openrouter') {
          const freeModels = this.aiServices.openrouter.freeModels;
          const currentModel = this.selectedModel;

          if (freeModels.length > 0 && !freeModels.includes(currentModel)) {
            this.setSelectedModel(freeModels[0]);
          }
        }
      }
    },

    // Get OpenRouter model categories
    getOpenRouterModelCategories() {
      const service = this.aiServices.openrouter;
      if (!service) return { free: [], paid: [] };

      return {
        free: service.freeModels.length > 0 ? service.freeModels : service.defaultFreeModels,
        paid: service.paidModels.length > 0 ? service.paidModels : service.defaultModels.filter(m => !service.defaultFreeModels.includes(m)),
        all: service.models.length > 0 ? service.models : service.defaultModels
      };
    },

    // Load models from API
    async loadModels(serviceKey) {
      const service = this.aiServices[serviceKey];
      if (!service || !service.apiKey || service.loading) {
        // return false;
      }

      try {
        service.loading = true;
        const modelsData = await this.fetchModelsFromAPI(serviceKey);

        if (modelsData && modelsData.length > 0) {
          if (serviceKey === 'openrouter') {
            // For OpenRouter, categorize models by pricing
            this.categorizeOpenRouterModels(service, modelsData);
          } else {
            // For other services, just set models array
            service.models = Array.isArray(modelsData) ? modelsData : modelsData.map(m => m.id || m.name);
          }
          console.log(`Loaded ${service.models.length} models for ${service.name}`);
          return true;
        } else {
          console.warn(`No models loaded for ${service.name}, using defaults`);
          this.setDefaultModels(service, serviceKey);
          return false;
        }
      } catch (error) {
        console.error(`Failed to load models for ${service.name}:`, error);
        this.setDefaultModels(service, serviceKey);
        return false;
      } finally {
        service.loading = false;
      }
    },

    // Categorize OpenRouter models by :free suffix
    categorizeOpenRouterModels(service, modelsData) {
      const freeModels = [];
      const paidModels = [];
      const allModels = [];

      modelsData.forEach(model => {
        const modelId = model.id || model.name;
        if (modelId) {
          allModels.push(modelId);

          // Check if model is free by :free suffix in ID
          const isFree = modelId.endsWith(':free');

          if (isFree) {
            freeModels.push(modelId);
          } else {
            paidModels.push(modelId);
          }
        }
      });

      service.models = allModels;
      service.freeModels = freeModels;
      service.paidModels = paidModels;

      console.log(`OpenRouter models categorized: ${freeModels.length} free, ${paidModels.length} paid`);
      console.log(`🆓 Free models:`, freeModels.slice(0, 5)); // Show first 5 for debugging
      console.log(`💰 Paid models:`, paidModels.slice(0, 5)); // Show first 5 for debugging
    },

    // Set default models for fallback
    setDefaultModels(service, serviceKey) {
      if (serviceKey === 'openrouter') {
        service.models = [...service.defaultModels];
        service.freeModels = [...service.defaultFreeModels];
        service.paidModels = service.defaultModels.filter(m => !service.defaultFreeModels.includes(m));
      } else {
        service.models = [...service.defaultModels];
      }
    },

    // Fetch models from specific service API
    async fetchModelsFromAPI(serviceKey) {
      const service = this.aiServices[serviceKey];
      if (!service) return [];

      // Validate API key first
      const validation = validateApiKey(serviceKey, service.apiKey);
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      try {
        console.log(`Fetching models from ${service.name} API...`,service,serviceKey);
        
        switch (serviceKey) {
          case 'openai':
          case 'deepseek':
          case 'claude':
          case 'nebula':
          case 'openrouter':
            return await getOpenAIModels(service.baseURL, service.apiKey);

          case 'gemini':
            return await getGeminiModels(service.baseURL, service.apiKey);

          default:
            throw new Error(`Unsupported service: ${serviceKey}`);
        }
      } catch (error) {
        console.error(`API request failed for ${serviceKey}:`, error);
        throw error;
      }
    },

    // Validate API key format
    validateApiKey(serviceKey, apiKey) {
      return validateApiKey(serviceKey, apiKey);
    },
    
    clearGeneratedAudios() {
      this.generatedAudios = [];
      // localStorage.removeItem('generatedAudios');
    },

    // OpenAI TTS Actions
    setOpenAITTSConfig(config) {
      this.openaiTTS = { ...this.openaiTTS, ...config };
    },

    setOpenAITTSApiKey(apiKey) {
      this.openaiTTS.apiKey = apiKey;
    },

    setOpenAITTSVoice(voice) {
      this.openaiTTS.selectedVoice = voice;
    },

    setOpenAITTSLanguage(language) {
      this.openaiTTS.selectedLanguage = language;
    },

    setOpenAITTSSpeed(speed) {
      this.openaiTTS.speed = speed;
    },

    toggleOpenAITTS(enabled) {
      this.openaiTTS.enabled = enabled;
    },

    getOpenAIVoicesByLanguage(language) {
      return this.openaiTTS.voices[language] || [];
    },

    // Mimimax TTS Actions
    setMimimaxTTSConfig(config) {
      this.mimimaxTTS = { ...this.mimimaxTTS, ...config };
    },

    setMimimaxTTSApiKey(apiKey) {
      this.mimimaxTTS.apiKey = apiKey;
    },

    setMimimaxTTSGroupId(groupId) {
      this.mimimaxTTS.groupId = groupId;
    },

    setMimimaxTTSVoice(voiceId) {
      this.mimimaxTTS.selectedVoice = voiceId;
    },

    setMimimaxTTSModel(model) {
      this.mimimaxTTS.model = model;
    },

    setMimimaxVoiceSettings(settings) {
      this.mimimaxTTS.voiceSettings = { ...this.mimimaxTTS.voiceSettings, ...settings };
    },

    setMimimaxAudioSettings(settings) {
      this.mimimaxTTS.audioSettings = { ...this.mimimaxTTS.audioSettings, ...settings };
    },

    toggleMimimaxTTS(enabled) {
      this.mimimaxTTS.enabled = enabled;
    },

    // Voice Clone Management
    addMimimaxVoiceClone(voice) {
      if (!this.mimimaxTTS.voices.custom.find(v => v.id === voice.id)) {
        this.mimimaxTTS.voices.custom.push(voice);
      }
    },

    removeMimimaxVoiceClone(voiceId) {
      this.mimimaxTTS.voices.custom = this.mimimaxTTS.voices.custom.filter(v => v.id !== voiceId);
    },

    updateMimimaxVoiceClone(voiceId, updates) {
      const index = this.mimimaxTTS.voices.custom.findIndex(v => v.id === voiceId);
      if (index !== -1) {
        this.mimimaxTTS.voices.custom[index] = { ...this.mimimaxTTS.voices.custom[index], ...updates };
      }
    },

    getMimimaxVoices() {
      return [
        ...this.mimimaxTTS.voices.default,
        ...this.mimimaxTTS.voices.custom
      ];
    },

    getMimimaxVoiceById(voiceId) {
      const allVoices = this.getMimimaxVoices();
      return allVoices.find(v => v.id === voiceId);
    },

    removeGeneratedAudio(id) {
      this.generatedAudios = this.generatedAudios.filter(audio => audio.id !== id);
      // localStorage.setItem('generatedAudios', JSON.stringify(this.generatedAudios));
    },

    // VBee TTS Actions
    setVbeeTTSConfig(config) {
      this.vbee = { ...this.vbee, ...config };
    },

    setVbeeTTSApiKey(apiKey) {
      this.vbee.apiKey = apiKey;
    },

    setVbeeTTSVoice(voiceId) {
      this.vbee.selectedVoice = voiceId;
    },

    setVbeeTTSLanguage(language) {
      this.vbee.selectedLanguage = language;
    },

    setVbeeVoiceSettings(settings) {
      this.vbee.voiceSettings = { ...this.vbee.voiceSettings, ...settings };
    },

    setVbeeAudioSettings(settings) {
      this.vbee.audioSettings = { ...this.vbee.audioSettings, ...settings };
    },

    toggleVbeeTTS(enabled) {
      this.vbee.enabled = enabled;
    },

    getVbeeVoices() {
      // this.vbee.voices = voiceVbees
      return this.vbee.voices || [];
    },

    getVbeeVoiceById(voiceId) {
      return this.vbee.voices.find(v => v.id === voiceId);
    },

    getVbeeVoicesByGender(gender) {
      return this.vbee.voices.filter(v => v.gender === gender);
    },

    getVbeeVoicesByLevel(level) {
      return this.vbee.voices.filter(v => v.level === level);
    },

    getVbeeVoicesByLanguage(languageCode) {
      return this.vbee.voices.filter(v => v.language_code === languageCode);
    },

    // Novel Translation Actions
    setNovelMemory(memory) {
      this.novelTranslation.memory = memory;
    },

    updateNovelProgress(srtListName, progress) {
      this.novelTranslation.progress[srtListName] = progress;
    },

    getNovelProgress(srtListName) {
      return this.novelTranslation.progress[srtListName] || {
        translatedCount: 0,
        totalCount: 0,
        lastTranslatedIndex: -1,
        memory: ''
      };
    },

    setNovelTranslating(isTranslating) {
      this.novelTranslation.isTranslating = isTranslating;
    },

    setNovelBatchProgress(currentBatch, totalBatches) {
      this.novelTranslation.currentBatch = currentBatch;
      this.novelTranslation.totalBatches = totalBatches;
    },

    // Initialize memory for current SRT list if not exists
    initializeNovelMemoryForCurrentSrt() {
      if (this.currentSrtList && !this.currentSrtList.memory) {
        this.currentSrtList.memory = '';
      }
    },

    // Update memory for current SRT list
    updateCurrentSrtMemory(memory) {
      if (this.currentSrtList) {
        this.currentSrtList.memory = memory;

        // Also update in srtLists array
        const index = this.srtLists.findIndex(
          (item) => item.name === this.currentSrtList.name
        );
        if (index !== -1) {
          this.srtLists[index].memory = memory;
        }
      }
    },

    // Get novel progress for specific SRT by name
    getNovelProgressBySrtName(srtName) {
      return this.novelTranslation.progress[srtName] || {
        translatedCount: 0,
        totalCount: 0,
        lastTranslatedIndex: -1,
        memory: '',
        translatedTexts: []
      };
    },

    // Set novel progress for specific SRT by name
    setNovelProgressBySrtName(srtName, progress) {
      this.novelTranslation.progress[srtName] = progress;
    },

    // Clear novel progress for specific SRT
    clearNovelProgressBySrtName(srtName) {
      if (this.novelTranslation.progress[srtName]) {
        delete this.novelTranslation.progress[srtName];
      }
    },

    // Get all SRT names with novel progress
    getNovelProgressSrtNames() {
      return Object.keys(this.novelTranslation.progress);
    },

    // Check if SRT has novel progress
    hasNovelProgress(srtName) {
      return !!this.novelTranslation.progress[srtName];
    }
  }
});
