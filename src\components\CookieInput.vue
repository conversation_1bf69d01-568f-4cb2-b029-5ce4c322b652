<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { message } from "ant-design-vue";
import { useTTSStore } from "../stores/ttsStore";
import { configureFromStore, getCurrentConfig } from "../lib/allTranslateService";
import MimimaxVoiceCloneModal from "./MimimaxVoiceCloneModal.vue";

const ttsStore = useTTSStore();
const cookie = ref(ttsStore.cookie);
const workspaceId = ref(ttsStore.workspaceId);
const tiktokSessionId = ref(ttsStore.tiktokSessionId);
const showCookie = ref(false);
const speechRate = ref(0);
const pitchRate = ref(0);

// AI Service Configuration
const selectedService = ref(ttsStore.selectedAiService);
const selectedModel = ref(ttsStore.selectedModel);
const showApiKeys = ref({});
const showOpenAIKey = ref(false);
const testingOpenAI = ref(false);
const showMimimaxKey = ref(false);
const testingMimimax = ref(false);
const showVbeeKey = ref(false);
const testingVbee = ref(false);
const showVoiceCloneModal = ref(false);

// Computed properties
const availableServices = computed(() => {
  return Object.entries(ttsStore.aiServices).map(([key, service]) => ({
    key,
    ...service,
  }));
});

const availableModels = computed(() => {
  const service = ttsStore.aiServices[selectedService.value];
  return service ? service.models : [];
});

const currentServiceConfig = computed(() => {
  return ttsStore.aiServices[selectedService.value];
});

const availableOpenAIVoices = computed(() => {
  return ttsStore.getOpenAIVoicesByLanguage(ttsStore.openaiTTS.selectedLanguage);
});

const availableMimimaxVoices = computed(() => {
  return ttsStore.getMimimaxVoices();
});

const availableVbeeVoices = computed(() => {
  return ttsStore.getVbeeVoices().filter(voice => voice.active);
});

// Transform speech rate value to a multiplier format (1x, 1.2x, etc.)
function formatSpeechRate(value) {
  // Convert from slider value (-50 to 100) to multiplier (0.5x to 2x)
  const multiplier = 1 + value / 100;
  return `${multiplier.toFixed(1)}x`;
}

function parseSpeechRate(value) {
  // Convert from multiplier format (e.g., "1.2x") back to slider value
  const multiplier = parseFloat(value.replace("x", ""));
  return Math.round((multiplier - 1) * 100);
}

function formatPitchRate(value) {
  return `${value}`;
}

function parsePitchRate(value) {
  return parseInt(value);
}

async function saveCookie() {
  ttsStore.setCookie(cookie.value);
  ttsStore.setWorkspaceId(workspaceId.value);
  ttsStore.setTypeEngine(ttsStore.typeEngine);

  // Save TikTok session ID to both store and database
  ttsStore.setTiktokSessionId(tiktokSessionId.value);
  await ttsStore.saveTiktokSessionIdToDB(tiktokSessionId.value);

  electronAPI.setSession({
    cookie: cookie.value,
    workspaceId: workspaceId.value,
    tiktokSessionId: tiktokSessionId.value,
  });

  // Auto-configure translation service
  updateTranslationService();

  message.success("Configuration saved!");
}

// AI Service Functions
function onServiceChange(serviceKey) {
  ttsStore.setSelectedAiService(serviceKey);
  selectedService.value = serviceKey;
  selectedModel.value = ttsStore.selectedModel;
  updateTranslationService();
}

function onModelChange(model) {
  ttsStore.setSelectedModel(model);
  selectedModel.value = model;
  updateTranslationService();
}

async function updateApiKey(serviceKey, apiKey) {
  ttsStore.setAiServiceConfig(serviceKey, { apiKey });

  // Validate API key format
  if (apiKey && apiKey.trim()) {
    const validation = ttsStore.validateApiKey(serviceKey, apiKey);
    if (!validation.valid) {
      message.warning(validation.message);
      return;
    }

    // Auto-load models when valid API key is provided
    try {
      const success = await ttsStore.loadModels(serviceKey);
      if (success) {
        message.success(
          `${ttsStore.aiServices[serviceKey].models.length} models loaded for ${ttsStore.aiServices[serviceKey].name}`
        );
      } else {
        message.warning(
          `Using default models for ${ttsStore.aiServices[serviceKey].name}`
        );
      }
    } catch (error) {
      message.error(`Failed to load models: ${error.message}`);
    }
  }

  updateTranslationService();
}

function updateBaseURL(serviceKey, baseURL) {
  ttsStore.setAiServiceConfig(serviceKey, { baseURL });
  updateTranslationService();
}

function toggleService(serviceKey, enabled) {
  ttsStore.enableAiService(serviceKey, enabled);
  if (enabled && !ttsStore.aiServices[serviceKey].apiKey) {
    message.warning(`Please enter API key for ${ttsStore.aiServices[serviceKey].name}`);
  }
}

function updateTranslationService() {
  try {
    const success = configureFromStore(ttsStore);
    if (success) {
      console.log("Translation service configured:", getCurrentConfig());
    }
  } catch (error) {
    console.error("Failed to configure translation service:", error);
  }
}

function toggleApiKeyVisibility(serviceKey) {
  showApiKeys.value[serviceKey] = !showApiKeys.value[serviceKey];
}

// OpenRouter specific functions
function toggleOpenRouterFreeOnly(showFreeOnly) {
  ttsStore.toggleOpenRouterFreeOnly(showFreeOnly);
}

function getOpenRouterModelStats() {
  const categories = ttsStore.getOpenRouterModelCategories();
  return {
    free: categories.free.length,
    paid: categories.paid.length,
    total: categories.all.length,
  };
}

function getDisplayModels(service) {
  if (service.key === "openrouter") {
    if (service.showFreeOnly) {
      return service.freeModels.length > 0
        ? service.freeModels
        : service.defaultFreeModels || [];
    }
    return service.models.length > 0 ? service.models : service.defaultModels;
  }
  return service.models.length > 0 ? service.models : service.defaultModels;
}

function isOpenRouterFreeModel(model) {
  const service = ttsStore.aiServices.openrouter;
  if (!service) return false;

  const freeModels =
    service.freeModels.length > 0 ? service.freeModels : service.defaultFreeModels || [];
  return freeModels.includes(model);
}

function getModelTagColor(model, service) {
  // Selected model
  if (model === selectedModel.value && service.key === selectedService.value) {
    return "blue";
  }

  // OpenRouter free models
  if (service.key === "openrouter" && isOpenRouterFreeModel(model)) {
    return "green";
  }

  return "default";
}

async function loadModelsManually(serviceKey) {
  try {
    const success = await ttsStore.loadModels(serviceKey);
    if (success) {
      message.success(`Models refreshed for ${ttsStore.aiServices[serviceKey].name}`);
    } else {
      message.warning(`Using default models for ${ttsStore.aiServices[serviceKey].name}`);
    }
  } catch (error) {
    message.error(`Failed to load models: ${error.message}`);
  }
}

async function testApiConnection(serviceKey) {
  const service = ttsStore.aiServices[serviceKey];
  if (!service.apiKey) {
    message.warning("Please enter API key first");
    return;
  }

  try {
    message.loading("Testing connection...", 0);
    const success = await ttsStore.loadModels(serviceKey);
    message.destroy();

    if (success) {
      message.success(`Connection successful! Loaded ${service.models.length} models`);
    } else {
      message.warning("Connection successful but no models loaded");
    }
  } catch (error) {
    message.destroy();
    message.error(`Connection failed: ${error.message}`);
  }
}

function callChangeTypeEngine() {
  setTimeout(() => {
    ttsStore.fetchSpeakers();
  }, 500);
}

function updateSpeechRate(value) {
  speechRate.value = value;
  ttsStore.updateTTSParams({ speech_rate: value });
}

function updatePitchRate(value) {
  pitchRate.value = value;
  ttsStore.updateTTSParams({ pitch_rate: value });
}

function openFolder() {
  electronAPI.openFolder();
}

// OpenAI TTS Functions
function onOpenAILanguageChange(language) {
  ttsStore.setOpenAITTSLanguage(language);
  // Auto-select first voice for the new language
  const voices = ttsStore.getOpenAIVoicesByLanguage(language);
  if (voices.length > 0) {
    ttsStore.setOpenAITTSVoice(voices[0].id);
  }
}

function onOpenAIVoiceChange(voice) {
  ttsStore.setOpenAITTSVoice(voice);
}

async function testOpenAIVoice() {
  if (!ttsStore.openaiTTS.apiKey) {
    message.warning("Please enter OpenAI API key first");
    return;
  }

  testingOpenAI.value = true;
  const testText = "Hello, this is a test of OpenAI text-to-speech synthesis.";

  try {
    const response = await electronAPI.generateTTS({
      text: testText,
      speaker: ttsStore.openaiTTS.selectedVoice,
      typeEngine: 'openai',
      openaiConfig: {
        apiKey: ttsStore.openaiTTS.apiKey,
        baseURL: ttsStore.openaiTTS.baseURL,
        speed: ttsStore.openaiTTS.speed,
        format: ttsStore.openaiTTS.format
      }
    });

    if (response.success) {
      const audio = new Audio(response.audioUrl);
      audio.play();
      message.success("OpenAI TTS test successful!");
    } else {
      message.error(`OpenAI TTS test failed: ${response.message}`);
    }
  } catch (error) {
    console.error('OpenAI TTS test error:', error);
    message.error(`OpenAI TTS test error: ${error.message}`);
  } finally {
    testingOpenAI.value = false;
  }
}

// Mimimax TTS Functions
async function testMimimaxVoice() {
  if (!ttsStore.mimimaxTTS.apiKey || !ttsStore.mimimaxTTS.groupId) {
    message.warning("Please enter Mimimax API key and Group ID first");
    return;
  }

  if (!ttsStore.mimimaxTTS.selectedVoice) {
    message.warning("Please select a voice first");
    return;
  }

  testingMimimax.value = true;
  const testText = "Xin chào, đây là bài kiểm tra tổng hợp văn bản thành giọng nói của Mimimax.";

  try {
    const data = JSON.parse(JSON.stringify({
      text: testText,
      speaker: ttsStore.mimimaxTTS.selectedVoice,
      typeEngine: 'mimimax',
      mimimaxConfig: {
        apiKey: ttsStore.mimimaxTTS.apiKey,
        groupId: ttsStore.mimimaxTTS.groupId,
        selectedVoice: ttsStore.mimimaxTTS.selectedVoice,
        voiceSettings: ttsStore.mimimaxTTS.voiceSettings,
        audioSettings: ttsStore.mimimaxTTS.audioSettings
      }
    }))
    console.log('data',data);

    const response = await electronAPI.generateTTS(data);

    if (response.success) {
      const audio = new Audio(response.audioUrl);
      audio.play();
      const selectedVoiceName = getSelectedVoiceName();
      message.success(`Mimimax TTS test successful with voice: ${selectedVoiceName}`);
    } else {
      message.error(`Mimimax TTS test failed: ${response.error}`);
    }
  } catch (error) {
    console.error('Mimimax TTS test error:', error);
    message.error(`Mimimax TTS test error: ${error.message}`);
  } finally {
    testingMimimax.value = false;
  }
}

function getSelectedVoiceName() {
  const selectedVoice = availableMimimaxVoices.value.find(
    voice => voice.id === ttsStore.mimimaxTTS.selectedVoice
  );
  return selectedVoice ? selectedVoice.name : 'Unknown Voice';
}

// VBee TTS Functions
async function testVbeeVoice() {
  if (!ttsStore.vbee.apiKey) {
    message.warning("Please enter VBee API key first");
    return;
  }

  testingVbee.value = true;
  const testText = "Xin chào, đây là bài kiểm tra giọng nói VBee TTS.";

  try {
    const response = await electronAPI.generateTTS(JSON.parse(JSON.stringify({
      text: testText,
      speaker: ttsStore.vbee.selectedVoice,
      typeEngine: 'vbee',
      vbeeConfig: {
        apiKey: ttsStore.vbee.apiKey,
        baseURL: ttsStore.vbee.baseURL,
        selectedVoice: ttsStore.vbee.selectedVoice,
        voiceSettings: ttsStore.vbee.voiceSettings,
        audioSettings: ttsStore.vbee.audioSettings
      }
    })));

    if (response.success) {
      const audio = new Audio(response.audioUrl);
      audio.play();
      const selectedVoiceName = getSelectedVbeeVoiceName();
      message.success(`VBee TTS test successful with voice: ${selectedVoiceName}`);
    } else {
      message.error(`VBee TTS test failed: ${response.message}`);
    }
  } catch (error) {
    console.error('VBee TTS test error:', error);
    message.error(`VBee TTS test error: ${error.message}`);
  } finally {
    testingVbee.value = false;
  }
}

function getSelectedVbeeVoiceName() {
  const selectedVoice = availableVbeeVoices.value.find(
    voice => voice.id === ttsStore.vbee.selectedVoice
  );
  return selectedVoice ? selectedVoice.name : 'Unknown Voice';
}

function onVbeeVoiceChange(voiceId) {
  ttsStore.setVbeeTTSVoice(voiceId);
}

// Watchers
watch(selectedService, (newService) => {
  if (newService !== ttsStore.selectedAiService) {
    onServiceChange(newService);
  }
});

watch(selectedModel, (newModel) => {
  if (newModel !== ttsStore.selectedModel) {
    onModelChange(newModel);
  }
});

async function initializeServices() {
  // Auto-load models for services with API keys
  const loadPromises = Object.entries(ttsStore.aiServices)
    .filter(
      ([key, service]) => service.enabled && service.apiKey && service.models.length === 0
    )
    .map(async ([key, service]) => {
      try {
        await ttsStore.loadModels(key);
        console.log(`Auto-loaded models for ${service.name}`);
      } catch (error) {
        console.warn(`Failed to auto-load models for ${service.name}:`, error.message);
      }
    });

  await Promise.allSettled(loadPromises);
}

onMounted(async () => {
  cookie.value = ttsStore.cookie;
  workspaceId.value = ttsStore.workspaceId;
  speechRate.value = ttsStore.params?.speech_rate || 0;
  pitchRate.value = ttsStore.params?.pitch_rate || 0;

  // Load TikTok session ID from database
  const dbTiktokSessionId = await ttsStore.getTiktokSessionIdFromDB();
  if (dbTiktokSessionId) {
    tiktokSessionId.value = dbTiktokSessionId;
  } else {
    tiktokSessionId.value = ttsStore.tiktokSessionId;
  }

  // Initialize AI service configuration
  selectedService.value = ttsStore.selectedAiService;
  selectedModel.value = ttsStore.selectedModel;

  // Initialize API key visibility
  Object.keys(ttsStore.aiServices).forEach((key) => {
    showApiKeys.value[key] = false;
  });

  // Auto-configure translation service on mount
  updateTranslationService();

  // Auto-load models for configured services
  await initializeServices();
});



async function vbeeApiChange() {
  await electronAPI.invoke('database', 'Config.updateValueByName', 'vbee_refresh_token', ttsStore.vbee.apiKey);
}

async function vbeeTestChange() {
  await electronAPI.invoke('database', 'Config.updateValueByName', 'vbee_test', ttsStore.vbee.test);
}




</script>

<template>
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4"> <a-button type="primary" danger class="mb-1 mr-2" @click="S.$router.replace('/')" v-if="$route.path =='/configs'">Back</a-button>Configuration</h2>

    <div class="space-y-4">
      <div>
        <label
          for="typeEngine"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Type Engine
        </label>
        <a-radio-group
          v-model:value="ttsStore.typeEngine"
          @change="callChangeTypeEngine"
          class="mt-1"
        >
          <a-radio value="capcut">CapCut TTS</a-radio>
          <a-radio value="tiktok">Tiktok TTS</a-radio>
          <a-radio value="openai">OpenAI TTS</a-radio>
          <a-radio value="mimimax">Minimax TTS</a-radio>
          <a-radio value="vbee">Vbee TTS</a-radio>
        </a-radio-group>
      </div>

      <div v-if="ttsStore.typeEngine === 'capcut'">
        <label
          for="workspaceId"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Workspace ID
        </label>
        <a-input
          id="workspaceId"
          v-model:value="workspaceId"
          placeholder="Enter your CapCut workspace ID"
          class="mt-1"
        />
      </div>
      <div v-if="ttsStore.typeEngine === 'tiktok'">
        <label
          for="tiktokSessionId"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Tiktok Session ID
        </label>
        <a-input
          id="tiktokSessionId"
          v-model:value="tiktokSessionId"
          placeholder="Enter your Tiktok Session ID"
          class="mt-1"
        />
      </div>
      <div v-if="ttsStore.typeEngine === 'capcut'">
        <label
          for="cookie"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Cookie
        </label>
        <div class="mt-1 relative">
          <a-input
            id="cookie"
            v-model:value="cookie"
            :type="showCookie ? 'text' : 'password'"
            placeholder="Enter your CapCut cookie"
            :rows="3"
          />
          <button
            type="button"
            @click="showCookie = !showCookie"
            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            <span v-if="showCookie">Hide</span>
            <span v-else>Show</span>
          </button>
        </div>
      </div>

      <!-- Mimimax TTS Configuration -->
      <div v-if="ttsStore.typeEngine === 'mimimax'" class="border-t pt-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
          Mimimax TTS Configuration
        </h3>

        <!-- API Key -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Mimimax API Key
          </label>
          <div class="relative">
            <a-input
              v-model:value="ttsStore.mimimaxTTS.apiKey"
              :type="showMimimaxKey ? 'text' : 'password'"
              placeholder="Enter your Mimimax API key"
              class="pr-10"
            />
            <button
              type="button"
              @click="showMimimaxKey = !showMimimaxKey"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              <span v-if="showMimimaxKey">Hide</span>
              <span v-else>Show</span>
            </button>
          </div>
        </div>

        <!-- Group ID -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Group ID
          </label>
          <a-input
            v-model:value="ttsStore.mimimaxTTS.groupId"
            placeholder="Enter your Mimimax Group ID"
          />
        </div>

        <!-- Model Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Model
          </label>
          <a-select
            v-model:value="ttsStore.mimimaxTTS.model"
            class="w-full"
          >
            <a-select-option value="speech-02-hd">Speech-02-HD (High Quality)</a-select-option>
            <a-select-option value="speech-02-turbo">Speech-02-Turbo (Fast)</a-select-option>
          </a-select>
        </div>

        <!-- Voice Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Voice
          </label>
          <a-select
            v-model:value="ttsStore.mimimaxTTS.selectedVoice"
            class="w-full"
            placeholder="Select a voice"
          >
            <a-select-option
              v-for="voice in availableMimimaxVoices"
              :key="voice.id"
              :value="voice.id"
            >
              <div class="flex items-center justify-between">
                <span>{{ voice.name }}</span>
                <div class="flex items-center gap-1">
                  <a-tag v-if="voice.isDefault" color="blue" size="small">Default</a-tag>
                  <a-tag v-else color="green" size="small">Custom</a-tag>
                  <a-tag :color="voice.gender === 'female' ? 'pink' : 'cyan'" size="small">
                    {{ voice.gender }}
                  </a-tag>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </div>

        <!-- Voice Settings -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Voice Settings</h4>

          <!-- Speed -->
          <div class="mb-3">
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Speed: {{ ttsStore.mimimaxTTS.voiceSettings.speed }}x
            </label>
            <a-slider
              v-model:value="ttsStore.mimimaxTTS.voiceSettings.speed"
              :min="0.5"
              :max="2.0"
              :step="0.1"
              :marks="{
                '0.5': '0.5x',
                '1': '1x',
                '1.5': '1.5x',
                '2': '2x'
              }"
            />
          </div>

          <!-- Volume -->
          <div class="mb-3">
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Volume: {{ ttsStore.mimimaxTTS.voiceSettings.vol }}
            </label>
            <a-slider
              v-model:value="ttsStore.mimimaxTTS.voiceSettings.vol"
              :min="0.1"
              :max="2.0"
              :step="0.1"
              :marks="{
                '0.1': '0.1',
                '1': '1',
                '2': '2'
              }"
            />
          </div>

          <!-- Pitch -->
          <div class="mb-3">
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Pitch: {{ ttsStore.mimimaxTTS.voiceSettings.pitch }}
            </label>
            <a-slider
              v-model:value="ttsStore.mimimaxTTS.voiceSettings.pitch"
              :min="-12"
              :max="12"
              :step="1"
              :marks="{
                '-12': '-12',
                '0': '0',
                '12': '12'
              }"
            />
          </div>
        </div>

        <!-- Voice Clone Management -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Voice Clones
            </label>
            <a-button
              size="small"
              type="primary"
              @click="showVoiceCloneModal = true"
            >
              Manage Voices
            </a-button>
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ ttsStore.mimimaxTTS.voices.custom.length }} custom voice(s) configured
          </div>
        </div>

        <!-- Test Voice Button -->
        <div class="mb-4">
          <a-button
            type="primary"
            @click="testMimimaxVoice"
            :loading="testingMimimax"
            :disabled="!ttsStore.mimimaxTTS.apiKey || !ttsStore.mimimaxTTS.groupId || !ttsStore.mimimaxTTS.selectedVoice"
          >
            Test Voice
          </a-button>
        </div>
      </div>
      <!-- OpenAI TTS Configuration -->
      <div v-if="ttsStore.typeEngine === 'openai'" class="border-t pt-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
          OpenAI TTS Configuration
        </h3>

        <!-- API Key -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            OpenAI API Key
          </label>
          <div class="relative">
            <a-input
              v-model:value="ttsStore.openaiTTS.apiKey"
              :type="showOpenAIKey ? 'text' : 'password'"
              placeholder="Enter your OpenAI API key (sk-...)"
              class="pr-10"
            />
            <button
              type="button"
              @click="showOpenAIKey = !showOpenAIKey"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              <span v-if="showOpenAIKey">Hide</span>
              <span v-else>Show</span>
            </button>
          </div>
        </div>

        <!-- Language Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Language
          </label>
          <a-select
            v-model:value="ttsStore.openaiTTS.selectedLanguage"
            class="w-full"
            @change="onOpenAILanguageChange"
          >
            <a-select-option value="en">English</a-select-option>
            <a-select-option value="vi">Tiếng Việt</a-select-option>
            <a-select-option value="zh">中文</a-select-option>
          </a-select>
        </div>

        <!-- Voice Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Voice
          </label>
          <a-select
            v-model:value="ttsStore.openaiTTS.selectedVoice"
            class="w-full"
            @change="onOpenAIVoiceChange"
          >
            <a-select-option
              v-for="voice in availableOpenAIVoices"
              :key="voice.id"
              :value="voice.id"
            >
              {{ voice.name }}
            </a-select-option>
          </a-select>
        </div>

        <!-- Speed Control -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Speed: {{ ttsStore.openaiTTS.speed }}x
          </label>
          <a-slider
            v-model:value="ttsStore.openaiTTS.speed"
            :min="0.25"
            :max="4.0"
            :step="0.25"
            :marks="{
              '0.25': '0.25x',
              '1': '1x',
              '2': '2x',
              '4': '4x'
            }"
            class="mb-2"
          />
        </div>

        <!-- Test Voice Button -->
        <div class="mb-4">
          <a-button
            type="primary"
            @click="testOpenAIVoice"
            :loading="testingOpenAI"
            :disabled="!ttsStore.openaiTTS.apiKey"
          >
            Test Voice
          </a-button>
        </div>
      </div>

      <!-- VBee TTS Configuration -->
      <div v-if="ttsStore.typeEngine === 'vbee'" class="border-t pt-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
          VBee TTS Configuration Web API
        </h3>

        <!-- API Key -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            API Key or Access Token
            
          </label>
          
          <div class="relative">
            <a-input
              v-model:value="ttsStore.vbee.apiKey"
              :type="showVbeeKey ? 'text' : 'password'"
              placeholder="Enter your VBee API key"
              class="pr-16"
              @change="vbeeApiChange"
            />
            <button
              type="button"
              @click="showVbeeKey = !showVbeeKey"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              <span v-if="showVbeeKey">Hide</span>
              <span v-else>Show</span>
            </button>

          </div>
          
        </div>
        <!-- Voice Test Mode -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Voice
          </label>
          <a-checkbox v-model:checked="ttsStore.vbee.test" @change="vbeeTestChange">Test Mode</a-checkbox>
        </div>
        <!-- Voice Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Voice
          </label>
          <a-select
            v-model:value="ttsStore.vbee.selectedVoice"
            class="w-full"
            placeholder="Select a voice"
            @change="onVbeeVoiceChange"
          >
            <a-select-option
              v-for="voice in availableVbeeVoices"
              :key="voice.id"
              :value="voice.id"
            >
              <div class="flex items-center justify-between">
                <span>{{ voice.name }}</span>
                <div class="flex items-center gap-1">
                  <a-tag :color="voice.level === 'PREMIUM' ? 'gold' : 'blue'" size="small">
                    {{ voice.level }}
                  </a-tag>
                  <a-tag :color="voice.gender === 'female' ? 'pink' : 'cyan'" size="small">
                    {{ voice.gender }}
                  </a-tag>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </div>

        <!-- Voice Settings -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Voice Settings</h4>

          <!-- Speed -->
          <div class="mb-3">
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Speed: {{ ttsStore.vbee.voiceSettings.speed }}x
            </label>
            <a-slider
              v-model:value="ttsStore.vbee.voiceSettings.speed"
              :min="0.5"
              :max="2.0"
              :step="0.1"
              :marks="{
                '0.5': '0.5x',
                '1': '1x',
                '1.5': '1.5x',
                '2': '2x'
              }"
            />
          </div>

          <!-- Volume -->
          <div class="mb-3">
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Volume: {{ ttsStore.vbee.voiceSettings.volume }}
            </label>
            <a-slider
              v-model:value="ttsStore.vbee.voiceSettings.volume"
              :min="0.1"
              :max="2.0"
              :step="0.1"
              :marks="{
                '0.1': '0.1',
                '1': '1',
                '2': '2'
              }"
            />
          </div>

          <!-- Pitch -->
          <div class="mb-3">
            <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Pitch: {{ ttsStore.vbee.voiceSettings.pitch }}
            </label>
            <a-slider
              v-model:value="ttsStore.vbee.voiceSettings.pitch"
              :min="-12"
              :max="12"
              :step="1"
              :marks="{
                '-12': '-12',
                '0': '0',
                '12': '12'
              }"
            />
          </div>
        </div>

        <!-- Test Voice Button -->
        <div class="mb-4">
          <a-button
            type="primary"
            @click="testVbeeVoice"
            :loading="testingVbee"
            :disabled="!ttsStore.vbee.apiKey"
          >
            Test Voice
          </a-button>
        </div>
      </div>

      <!-- AI Services Configuration -->
      <div class="border-t pt-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
          AI Translation Services
        </h3>

        <!-- Service Selector -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Active Service
          </label>
          <a-select
            v-model:value="selectedService"
            class="w-full"
            placeholder="Select AI service"
            @change="onServiceChange"
          >
            <a-select-option
              v-for="service in availableServices"
              :key="service.key"
              :value="service.key"
              :disabled="!service.enabled"
            >
              <div class="flex items-center justify-between">
                <span>{{ service.name }}</span>
                <div class="flex items-center gap-1">
                  <a-spin v-if="service.loading" size="small" />
                  <a-tag v-if="!service.enabled" color="red" size="small">Disabled</a-tag>
                  <a-tag v-else-if="!service.apiKey" color="orange" size="small"
                    >No API Key</a-tag
                  >
                  <a-tag v-else-if="service.models.length > 0" color="green" size="small">
                    {{ service.models.length }} models
                  </a-tag>
                  <a-tag v-else color="blue" size="small">Ready</a-tag>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </div>

        <!-- Model Selector -->
        <div class="mb-4" v-if="availableModels.length > 0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Model
          </label>
          <a-select
            v-model:value="selectedModel"
            class="w-full"
            placeholder="Select model"
            @change="onModelChange"
          >
            <a-select-option v-for="model in availableModels" :key="model" :value="model">
              {{ model }}
            </a-select-option>
          </a-select>
        </div>

        <!-- Service Configuration Cards -->
        <div class="space-y-4">
          <div
            v-for="service in availableServices"
            :key="service.key"
            class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-700"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <h4 class="font-medium text-gray-900 dark:text-white">
                  {{ service.name }}
                </h4>
                <a-spin v-if="service.loading" size="small" />
                <a-tag v-else-if="!service.enabled" color="red" size="small"
                  >Disabled</a-tag
                >
                <a-tag v-else-if="!service.apiKey" color="orange" size="small"
                  >No API Key</a-tag
                >
                <a-tag v-else-if="service.models.length > 0" color="green" size="small">
                  {{ service.models.length }} models
                </a-tag>
                <a-tag v-else color="blue" size="small">Default models</a-tag>
              </div>
              <a-switch
                :checked="service.enabled"
                @change="(checked) => toggleService(service.key, checked)"
                size="small"
              />
            </div>

            <div v-if="service.enabled" class="space-y-3">
              <!-- API Key -->
              <div>
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  API Key
                </label>
                <div class="flex gap-2">
                  <div class="relative flex-1">
                    <a-input
                      :value="service.apiKey"
                      :type="showApiKeys[service.key] ? 'text' : 'password'"
                      :placeholder="`Enter your ${service.name} API key`"
                      @input="(e) => updateApiKey(service.key, e.target.value)"
                      @blur="(e) => updateApiKey(service.key, e.target.value)"
                      class="pr-10"
                      :status="
                        service.apiKey &&
                        !ttsStore.validateApiKey(service.key, service.apiKey).valid
                          ? 'error'
                          : ''
                      "
                    />
                    <button
                      type="button"
                      @click="toggleApiKeyVisibility(service.key)"
                      class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      <span v-if="showApiKeys[service.key]">Hide</span>
                      <span v-else>Show</span>
                    </button>
                  </div>
                  <a-button
                    v-if="service.apiKey"
                    @click="testApiConnection(service.key)"
                    :loading="service.loading"
                    size="small"
                    type="primary"
                    ghost
                  >
                    Test
                  </a-button>
                </div>
              </div>

              <!-- Base URL (for custom endpoints) -->
              <div v-if="service.key !== 'gemini'">
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Base URL
                </label>
                <a-input
                  :value="service.baseURL"
                  :placeholder="`${service.name} API endpoint`"
                  @input="(e) => updateBaseURL(service.key, e.target.value)"
                />
              </div>

              <!-- Available Models -->
              <div>
                <div class="flex items-center justify-between mb-1">
                  <label
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Available Models
                  </label>
                  <div class="flex gap-1">
                    <a-button
                      v-if="service.apiKey"
                      @click="loadModelsManually(service.key)"
                      :loading="service.loading"
                      size="small"
                      type="link"
                    >
                      <template v-if="service.loading">Loading...</template>
                      <template v-else>Refresh</template>
                    </a-button>
                    <a-tag v-if="service.models.length > 0" color="green" size="small">
                      {{ service.models.length }} loaded
                    </a-tag>
                    <a-tag
                      v-else-if="service.defaultModels.length > 0"
                      color="orange"
                      size="small"
                    >
                      {{ service.defaultModels.length }} default
                    </a-tag>
                  </div>
                </div>

                <!-- OpenRouter Free/Paid Filter -->
                <div v-if="service.key === 'openrouter'" class="mb-3">
                  <div class="flex items-center gap-2 mb-2">
                    <a-switch
                      :checked="service.showFreeOnly"
                      @change="(checked) => toggleOpenRouterFreeOnly(checked)"
                      size="small"
                    />
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      Show free models only
                    </span>
                  </div>
                  <div class="flex gap-2 text-xs">
                    <a-tag color="green" size="small">
                      {{ getOpenRouterModelStats().free }} free
                    </a-tag>
                    <a-tag color="blue" size="small">
                      {{ getOpenRouterModelStats().paid }} paid
                    </a-tag>
                    <a-tag color="purple" size="small">
                      {{ getOpenRouterModelStats().total }} total
                    </a-tag>
                  </div>
                </div>

                <div class="flex flex-wrap gap-1">
                  <a-tag
                    v-for="model in getDisplayModels(service)"
                    :key="model"
                    :color="getModelTagColor(model, service)"
                    size="small"
                    class="cursor-pointer"
                    @click="onModelChange(model)"
                  >
                    {{ model }}
                    <span
                      v-if="service.key === 'openrouter' && isOpenRouterFreeModel(model)"
                      class="ml-1"
                    >
                      🆓
                    </span>
                  </a-tag>
                </div>
                <div
                  v-if="getDisplayModels(service).length === 0"
                  class="text-xs text-gray-500 mt-1"
                >
                  No models available
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Current Configuration Status -->
      <div class="border-t pt-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Current Configuration
          </h4>
          <div class="text-xs text-blue-700 dark:text-blue-300 space-y-1">
            <div>
              <strong>Service:</strong> {{ currentServiceConfig?.name || "None" }}
            </div>
            <div><strong>Model:</strong> {{ selectedModel || "None" }}</div>
            <div>
              <strong>API Key:</strong>
              {{ currentServiceConfig?.apiKey ? "✓ Configured" : "✗ Missing" }}
            </div>
            <div v-if="currentServiceConfig?.baseURL">
              <strong>Endpoint:</strong> {{ currentServiceConfig.baseURL }}
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-4 pt-4">
        <a-button type="primary" @click="openFolder"> Open folder audio </a-button>
        <a-button type="primary" @click="saveCookie"> Save Configuration </a-button>
      </div>
    </div>

    <!-- Mimimax Voice Clone Modal -->
    <MimimaxVoiceCloneModal
      v-model:open="showVoiceCloneModal"
    />
  </div>
</template>
