<template>
  <a-form-item label="Profile Templates" class="border border-gray-600 p-2 rounded-lg">
    <!-- Quick Actions Row -->
    <div class="profile-actions mb-3">
      <a-row :gutter="8">
        <a-col :span="8">
          <a-button type="primary" size="small" block @click="showSaveModal = true">
            <save-outlined />
            Save Profile
          </a-button>
        </a-col>
        <a-col :span="8">
          <a-button size="small" block @click="showLoadModal = true">
            <folder-open-outlined />
            Load Profile
          </a-button>
        </a-col>
        <a-col :span="8">
          <a-button size="small" block @click="showManagerModal = true">
            <setting-outlined />
            Manage
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- Current Profile Display -->
    <div v-if="profileStore.currentProfile" class="current-profile-display mb-3">
      <a-alert
        :message="`Current: ${profileStore.currentProfile.name}`"
        :description="profileStore.currentProfile.description"
        type="info"
        size="small"
        show-icon
        closable
        @close="clearCurrentProfile"
      />
    </div>

    <!-- Quick Load Dropdown -->
    <div class="quick-load">
      <a-select
        v-model:value="selectedProfileId"
        placeholder="Quick load a profile..."
        allow-clear
        size="small"
        style="width: 100%"
        @change="handleQuickLoad"
      >
        <a-select-opt-group v-for="category in categories" :key="category" :label="category">
          <a-select-option
            v-for="profile in getProfilesByCategory(category)"
            :key="profile.id"
            :value="profile.id"
          >
            {{ profile.name }}
          </a-select-option>
        </a-select-opt-group>
      </a-select>
    </div>

    <!-- Save Profile Modal -->
    <a-modal
      v-model:open="showSaveModal"
      title="Save Current Settings as Profile"
      @ok="handleSaveProfile"
      @cancel="handleCancelSave"
      :confirm-loading="isSaving"
      width="500px"
    >
      <a-form :model="saveForm" layout="vertical">
        <a-form-item label="Profile Name" required>
          <a-input v-model:value="saveForm.name" placeholder="Enter profile name" />
        </a-form-item>
        
        <a-form-item label="Description">
          <a-textarea 
            v-model:value="saveForm.description" 
            placeholder="Optional description"
            :rows="2"
          />
        </a-form-item>
        
        <a-form-item label="Category">
          <a-select 
            v-model:value="saveForm.category" 
            placeholder="Select category"
            mode="combobox"
          >
            <a-select-option value="General">General</a-select-option>
            <a-select-option value="Gaming">Gaming</a-select-option>
            <a-select-option value="Education">Education</a-select-option>
            <a-select-option value="Entertainment">Entertainment</a-select-option>
            <a-select-option value="Business">Business</a-select-option>
          </a-select>
        </a-form-item>

        <!-- Settings Preview -->
        <a-form-item label="Settings to Save">
          <div class="settings-preview p-2 bg-gray-50 rounded text-xs">
            <a-space size="small" wrap>
              <a-tag v-if="currentSettings.textAnimation?.showText" color="blue" size="small">
                Text Animation
              </a-tag>

              <!-- Multiple Overlays Display -->
              <a-tag v-if="currentSettings.overlays?.logos?.length > 0" color="orange" size="small">
                {{ currentSettings.overlays.logos.length }} Logo{{ currentSettings.overlays.logos.length > 1 ? 's' : '' }}
              </a-tag>
              <a-tag v-if="currentSettings.overlays?.images?.length > 0" color="green" size="small">
                {{ currentSettings.overlays.images.length }} Image{{ currentSettings.overlays.images.length > 1 ? 's' : '' }}
              </a-tag>
              <a-tag v-if="currentSettings.overlays?.texts?.length > 0" color="purple" size="small">
                {{ currentSettings.overlays.texts.length }} Text{{ currentSettings.overlays.texts.length > 1 ? 's' : '' }}
              </a-tag>

              <!-- Legacy Overlays Display (Backward Compatibility) -->
              <a-tag v-if="currentSettings.mediaOverlay?.showLogo && !currentSettings.overlays?.logos?.length" color="orange" size="small">
                Legacy Logo
              </a-tag>
              <a-tag v-if="currentSettings.mediaOverlay?.showImage && !currentSettings.overlays?.images?.length" color="green" size="small">
                Legacy Image
              </a-tag>
              <a-tag v-if="currentSettings.mediaOverlay?.showFixedText && !currentSettings.overlays?.texts?.length" color="purple" size="small">
                Legacy Text
              </a-tag>

              <a-tag v-if="currentSettings.subtitle?.showSubtitle" color="cyan" size="small">
                Subtitle
              </a-tag>
              <a-tag v-if="currentSettings.audio?.addBackgroundMusic" color="red" size="small">
                Background Music
              </a-tag>
              <a-tag color="default" size="small">
                Output Settings
              </a-tag>
            </a-space>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Load Profile Modal -->
    <a-modal
      v-model:open="showLoadModal"
      title="Load Profile"
      @ok="handleLoadProfile"
      @cancel="handleCancelLoad"
      :confirm-loading="isLoading"
      width="600px"
    >
      <div class="profile-list">
        <a-input
          v-model:value="searchQuery"
          placeholder="Search profiles..."
          size="small"
          class="mb-3"
        >
          <template #prefix>
            <search-outlined />
          </template>
        </a-input>

        <div class="profiles-grid" style="max-height: 400px; overflow-y: auto;">
          <a-row :gutter="[8, 8]">
            <a-col :span="24" v-for="profile in filteredProfiles" :key="profile.id">
              <a-card 
                size="small" 
                hoverable
                :class="{ 'selected-profile': selectedLoadProfileId === profile.id }"
                @click="selectedLoadProfileId = profile.id"
              >
                <template #title>
                  <div class="flex justify-between items-center">
                    <span class="text-sm font-medium">{{ profile.name }}</span>
                    <a-tag :color="getCategoryColor(profile.category)" size="small">
                      {{ profile.category }}
                    </a-tag>
                  </div>
                </template>
                
                <div class="profile-info">
                  <p class="text-xs text-gray-600 mb-2">{{ profile.description || 'No description' }}</p>
                  <div class="profile-features">
                    <a-space size="small" wrap>
                      <a-tag v-if="profile.settings.textAnimation?.showText" color="blue" size="small">
                        Text Animation
                      </a-tag>

                      <!-- Multiple Overlays Display -->
                      <a-tag v-if="profile.settings.overlays?.logos?.length > 0" color="orange" size="small">
                        {{ profile.settings.overlays.logos.length }} Logo{{ profile.settings.overlays.logos.length > 1 ? 's' : '' }}
                      </a-tag>
                      <a-tag v-if="profile.settings.overlays?.images?.length > 0" color="green" size="small">
                        {{ profile.settings.overlays.images.length }} Image{{ profile.settings.overlays.images.length > 1 ? 's' : '' }}
                      </a-tag>
                      <a-tag v-if="profile.settings.overlays?.texts?.length > 0" color="purple" size="small">
                        {{ profile.settings.overlays.texts.length }} Text{{ profile.settings.overlays.texts.length > 1 ? 's' : '' }}
                      </a-tag>

                      <!-- Legacy Overlays Display (Backward Compatibility) -->
                      <a-tag v-if="profile.settings.mediaOverlay?.showLogo && !profile.settings.overlays?.logos?.length" color="orange" size="small">
                        Legacy Logo
                      </a-tag>
                      <a-tag v-if="profile.settings.mediaOverlay?.showImage && !profile.settings.overlays?.images?.length" color="green" size="small">
                        Legacy Image
                      </a-tag>
                      <a-tag v-if="profile.settings.mediaOverlay?.showFixedText && !profile.settings.overlays?.texts?.length" color="purple" size="small">
                        Legacy Text
                      </a-tag>

                      <a-tag v-if="profile.settings.subtitle?.showSubtitle" color="cyan" size="small">
                        Subtitle
                      </a-tag>
                      <a-tag v-if="profile.settings.audio?.addBackgroundMusic" color="red" size="small">
                        Music
                      </a-tag>
                    </a-space>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    Updated: {{ formatDate(profile.updatedAt) }}
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <div v-if="filteredProfiles.length === 0" class="text-center py-4">
          <a-empty description="No profiles found" size="small" />
        </div>
      </div>
    </a-modal>

    <!-- Profile Manager Modal -->
    <a-modal
      v-model:open="showManagerModal"
      title="Profile Manager"
      :footer="null"
      width="90%"
      :body-style="{ maxHeight: '70vh', overflow: 'auto' }"
    >
      <ProfileManager />
    </a-modal>
  </a-form-item>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SaveOutlined,
  FolderOpenOutlined,
  SettingOutlined,
  SearchOutlined
} from '@ant-design/icons-vue';
import { useProfileStore } from '@/stores/profile-store';
import ProfileManager from './ProfileManager.vue';

// Define emits
const emit = defineEmits(['profile-applied']);

const profileStore = useProfileStore();

// Reactive state
const showSaveModal = ref(false);
const showLoadModal = ref(false);
const showManagerModal = ref(false);
const isSaving = ref(false);
const isLoading = ref(false);
const selectedProfileId = ref(null);
const selectedLoadProfileId = ref(null);
const searchQuery = ref('');

// Form data
const saveForm = reactive({
  name: '',
  description: '',
  category: 'General'
});

// Computed
const currentSettings = computed(() => {
  return profileStore.getCurrentSettings();
});

const categories = computed(() => {
  return profileStore.getAllCategories;
});

const filteredProfiles = computed(() => {
  if (!searchQuery.value) {
    return profileStore.profiles;
  }
  return profileStore.profiles.filter(profile =>
    profile.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    profile.description.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    profile.category.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// Methods
function getProfilesByCategory(category) {
  return profileStore.getProfilesByCategory(category);
}

function getCategoryColor(category) {
  const colors = {
    'General': 'default',
    'Gaming': 'red',
    'Education': 'blue',
    'Entertainment': 'purple',
    'Business': 'green'
  };
  return colors[category] || 'default';
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString();
}

async function handleQuickLoad(profileId) {
  if (!profileId) return;
  
  try {
    const success = profileStore.applyProfile(profileId);
    if (success) {
      message.success('Profile loaded successfully!');
      emit('profile-applied');
    } else {
      message.error(profileStore.error || 'Failed to load profile');
    }
  } catch (error) {
    message.error('Error loading profile: ' + error.message);
  }
  selectedProfileId.value = null;
}

async function handleSaveProfile() {
  if (!saveForm.name.trim()) {
    message.error('Please enter a profile name');
    return;
  }

  isSaving.value = true;
  try {
    const newProfile = profileStore.createProfile(
      saveForm.name,
      saveForm.description,
      saveForm.category
    );
    if (newProfile) {
      message.success('Profile saved successfully!');
      handleCancelSave();
    } else {
      message.error('Failed to save profile');
    }
  } catch (error) {
    message.error('Error saving profile: ' + error.message);
  } finally {
    isSaving.value = false;
  }
}

function handleCancelSave() {
  showSaveModal.value = false;
  saveForm.name = '';
  saveForm.description = '';
  saveForm.category = 'General';
}

async function handleLoadProfile() {
  if (!selectedLoadProfileId.value) {
    message.error('Please select a profile to load');
    return;
  }

  isLoading.value = true;
  try {
    const success = profileStore.applyProfile(selectedLoadProfileId.value);
    if (success) {
      message.success('Profile loaded successfully!');
      emit('profile-applied');
      handleCancelLoad();
    } else {
      message.error(profileStore.error || 'Failed to load profile');
    }
  } catch (error) {
    message.error('Error loading profile: ' + error.message);
  } finally {
    isLoading.value = false;
  }
}

function handleCancelLoad() {
  showLoadModal.value = false;
  selectedLoadProfileId.value = null;
  searchQuery.value = '';
}

function clearCurrentProfile() {
  profileStore.currentProfile = null;
}

// Initialize
onMounted(() => {
  profileStore.init();
});
</script>

<style scoped>
.profile-actions {
  margin-bottom: 12px;
}

.current-profile-display {
  margin-bottom: 12px;
}

.selected-profile {
  border: 2px solid #1890ff;
  background-color: #f6ffed;
}

.profile-info {
  font-size: 12px;
}

.settings-preview {
  min-height: 30px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.text-xs {
  font-size: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-gray-600 {
  color: #666;
}

.text-gray-500 {
  color: #8c8c8c;
}

.font-medium {
  font-weight: 500;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mt-1 {
  margin-top: 4px;
}
</style>
