<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useTTSStore } from '../stores/ttsStore';
import edgeVoices from '@/assets/edge-voices.json';
import { edgeTts } from '@/logic/edgeTts';
import { message } from 'ant-design-vue';

const ttsStore = useTTSStore();
const text = ref(ttsStore.text);
const isCapcutVoice = ref(false);
// const selectedSpeaker = ref(ttsStore.selectedSpeaker);

// Helper function to convert locale to language display name
const getLanguageFromLocale = (locale) => {
  const localeMap = {
    'vi-VN': 'Vietnamese',
    'en-US': 'English',
    'en-GB': 'English',
    'en-AU': 'English',
    'en-CA': 'English',
    'zh-CN': 'Chinese',
    'zh-TW': 'Chinese',
    'ja-JP': 'Japanese',
    'ko-KR': 'Korean',
    'fr-FR': 'French',
    'de-DE': 'German',
    'es-ES': 'Spanish',
    'it-IT': 'Italian',
    'pt-BR': 'Portuguese',
    'ru-RU': 'Russian',
    'ar-SA': 'Arabic',
    'hi-IN': 'Hindi',
    'th-TH': 'Thai'
  };

  // Try exact match first
  if (localeMap[locale]) {
    return localeMap[locale];
  }

  // Try language code match (e.g., 'en' from 'en-XX')
  const langCode = locale.split('-')[0];
  const matchingLocale = Object.keys(localeMap).find(key => key.startsWith(langCode + '-'));
  if (matchingLocale) {
    return localeMap[matchingLocale];
  }

  // Fallback to locale itself
  return locale;
};

// Get all voices from all engines with engine information - COMPUTED
const allVoicesWithEngine = computed(() => {
  const allVoices = [];

  // CapCut/TikTok voices
  if (ttsStore.speakers && ttsStore.speakers.length > 0) {
    const capCutVoices = ttsStore.speakers.map(voice => ({
      id: voice.id,
      name: voice.name,
      engine: 'capcut',
      engineName: 'CapCut TTS',
      gender: voice.gender,
      language: voice.language
    }));
    allVoices.push(...capCutVoices);
  }

  // OpenAI voices
  const languageMap = {
    'vi': 'Vietnamese',
    'en': 'English',
    'zh': 'Chinese'
  };

  Object.entries(languageMap).forEach(([langCode, displayLang]) => {
    const openaiVoices = ttsStore.getOpenAIVoicesByLanguage(langCode).map(voice => ({
      id: `openai_${voice.id}_${langCode}`,
      name: `${voice.name} (OpenAI)`,
      engine: 'openai',
      engineName: 'OpenAI TTS',
      originalId: voice.id,
      language: displayLang
    }));
    allVoices.push(...openaiVoices);
  });

  // Mimimax voices
  const mimimaxVoices = ttsStore.getMimimaxVoices().map(voice => ({
    id: `mimimax_${voice.id}`,
    name: `${voice.name} (Mimimax)`,
    engine: 'mimimax',
    engineName: 'Mimimax TTS',
    originalId: voice.id,
    gender: voice.gender,
    level: voice.isDefault ? 'DEFAULT' : 'CUSTOM'
  }));
  allVoices.push(...mimimaxVoices);

  // VBee voices
  const vbeeVoices = ttsStore.getVbeeVoices()
    .filter(voice => voice.active)
    .map(voice => ({
      id: `vbee_${voice.id}`,
      name: `${voice.name} (VBee)`,
      engine: 'vbee',
      engineName: 'VBee TTS',
      originalId: voice.id,
      gender: voice.gender,
      level: voice.level,
      language: 'Vietnamese'
    }));
  allVoices.push(...vbeeVoices);

  // Edge TTS voices
  const edgeTTSVoices = edgeVoices.map(voice => ({
    id: voice.ShortName,
    name: voice.FriendlyName,
    engine: 'edge',
    engineName: 'Edge TTS',
    originalId: voice.ShortName,
    gender: voice.Gender?.toLowerCase(),
    locale: voice.Locale,
    language: getLanguageFromLocale(voice.Locale)
  }));
  allVoices.push(...edgeTTSVoices);

  return allVoices;
});

const speakers = computed(() => {
  return allVoicesWithEngine.value;
});
const isLoading = computed(() => ttsStore.isLoading);
const error = computed(() => ttsStore.error);
const audioUrl = computed(() => ttsStore.audioUrl);

// Helper function to detect engine from voice ID
function detectEngineFromVoiceId(voiceId) {
  if (voiceId.startsWith('openai_')) return 'openai';
  if (voiceId.startsWith('mimimax_')) return 'mimimax';
  if (voiceId.startsWith('vbee_')) return 'vbee';
  if (voiceId.startsWith('edge_')) return 'edge';

  // Check if it's a direct VBee voice ID (without prefix)
  const isVbeeVoice = ttsStore.getVbeeVoices()?.some(v => v.id === voiceId);
  if (isVbeeVoice) return 'vbee';

  // Check if it's an Edge TTS voice (by ShortName pattern)
  const isEdgeVoice = edgeVoices.some(v => v.ShortName === voiceId);
  if (isEdgeVoice) return 'edge';

  return 'capcut'; // default for CapCut/TikTok voices
}

// Helper function to extract original voice ID
function extractOriginalVoiceId(voiceId) {
  if (voiceId.startsWith('openai_')) {
    const parts = voiceId.split('_');
    return parts[1];
  }
  if (voiceId.startsWith('mimimax_')) {
    return voiceId.replace('mimimax_', '');
  }
  if (voiceId.startsWith('vbee_')) {
    return voiceId.replace('vbee_', '');
  }
  if (voiceId.startsWith('edge_')) {
    return voiceId.replace('edge_', '');
  }

  // For direct VBee voice IDs, Edge TTS voices, or CapCut/TikTok voices, return as-is
  return voiceId;
}

async function generateTTS() {
  if (!text.value) return;

  // Detect engine from selected voice
  let detectedEngine = detectEngineFromVoiceId(ttsStore.selectedSpeaker);
  const originalVoiceId = extractOriginalVoiceId(ttsStore.selectedSpeaker);
  if(!isCapcutVoice.value && detectedEngine === 'capcut') {
    detectedEngine = 'tiktok';
  }
  try {
    ttsStore.isLoading = true;
    ttsStore.error = null;

    // Handle Edge TTS directly
    if (detectedEngine === 'edge') {
      console.log('Generating Edge TTS audio...');

      // Generate unique output path
      const timestamp = Date.now();
      const outputPath = await window.electronAPI.invoke('path:join', [
        await window.electronAPI.getCurrentDir(),
        `edge_tts_${timestamp}.mp3`
      ]);

      // Call Edge TTS generation directly
      await edgeTts.generateAudioByEdgeTTS({
        text: text.value,
        voice: originalVoiceId,
        outputPath: outputPath,
        pitch: 0, // Default pitch
        rate: 0,  // Default rate
      });

      // Get audio duration
      const duration = await window.electronAPI.invoke('audio:getDuration', outputPath);

      // Update store with results
      ttsStore.audioUrl = `file://${outputPath}`;
      ttsStore.audioDuration = duration || 0;

      console.log('Edge TTS generation successful:', { outputPath, duration });
      message.success('Edge TTS audio generated successfully!');

    } else {
      // Handle other engines through backend
      const originalEngine = ttsStore.typeEngine;
      const originalSpeaker = ttsStore.selectedSpeaker;

      ttsStore.typeEngine = detectedEngine;
      ttsStore.selectedSpeaker = originalVoiceId;
      ttsStore.setText(text.value);

      try {
        await ttsStore.generateTTS();
      } finally {
        // Restore original settings
        ttsStore.typeEngine = originalEngine;
        ttsStore.selectedSpeaker = originalSpeaker;
      }
    }
  } catch (error) {
    console.error('TTS generation error:', error);
    ttsStore.error = error.message;
    message.error('TTS generation failed: ' + error.message);
  } finally {
    ttsStore.isLoading = false;
  }
}

function formatDuration(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function downloadAudio(){
  const a = document.createElement('a');
  a.href = audioUrl.value;
  a.download = `tts-audio-${Date.now()}.mp3`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

function filterVoiceOption(input, option) {
  const voiceName = option.children[0]?.children?.[0]?.children || option.label || '';
  return voiceName.toLowerCase().includes(input.toLowerCase());
}

// Watch for voice selection changes to auto-select first voice if none selected
watch(() => speakers.value, (newSpeakers) => {
  if (newSpeakers.length > 0 && !ttsStore.selectedSpeaker) {
    ttsStore.selectedSpeaker = newSpeakers[0].id;
  }
}, { immediate: true });

// Helper functions
function getTagColor(level) {
  const colors = {
    'PREMIUM': 'gold',
    'STANDARD': 'blue',
    'DEFAULT': 'green',
    'CUSTOM': 'purple'
  };
  return colors[level] || 'blue';
}

function getEngineColor(engine) {
  const colors = {
    'capcut': 'blue',
    'tiktok': 'purple',
    'openai': 'green',
    'mimimax': 'orange',
    'vbee': 'red',
    'edge': 'cyan'
  };
  return colors[engine] || 'default';
}

// Voice statistics
const voiceStats = computed(() => {
  const stats = {
    total: speakers.value.length,
    male: 0,
    female: 0,
    premium: 0,
    standard: 0,
    engines: {
      capcut: 0,
      openai: 0,
      mimimax: 0,
      vbee: 0,
      edge: 0
    }
  };

  speakers.value.forEach(speaker => {
    if (speaker.gender === 'male') stats.male++;
    if (speaker.gender === 'female') stats.female++;
    if (speaker.level === 'PREMIUM') stats.premium++;
    if (speaker.level === 'STANDARD') stats.standard++;
    if (stats.engines[speaker.engine] !== undefined) {
      stats.engines[speaker.engine]++;
    }
  });

  return stats;
});

// Voice preview
const previewLoading = ref(false);

async function previewVoice() {
  if (!ttsStore.selectedSpeaker) return;

  previewLoading.value = true;

  // Detect engine from selected voice
  const detectedEngine = detectEngineFromVoiceId(ttsStore.selectedSpeaker);
  const originalVoiceId = extractOriginalVoiceId(ttsStore.selectedSpeaker);

  // Use appropriate preview text based on voice language
  let previewText = "Hello, this is a voice preview.";
  if (originalVoiceId.includes('vi-VN')) {
    previewText = "Xin chào, đây là bản xem trước giọng nói.";
  } else if (originalVoiceId.includes('zh-')) {
    previewText = "你好，这是语音预览。";
  }

  try {
    const originalText = ttsStore.text;

    // Handle Edge TTS directly
    if (detectedEngine === 'edge') {
      console.log('Previewing Edge TTS voice...');

      // Generate unique output path
      const timestamp = Date.now();
      const outputPath = await window.electronAPI.invoke('path:join', [
        await window.electronAPI.getCurrentDir(),
        `edge_tts_preview_${timestamp}.mp3`
      ]);

      // Call Edge TTS generation directly
      await edgeTts.generateAudioByEdgeTTS({
        text: previewText,
        voice: originalVoiceId,
        outputPath: outputPath,
        pitch: 0,
        rate: 0,
      });

      // Play the generated audio
      const audio = new Audio(`file://${outputPath}`);
      audio.play();

    } else {
      // Handle other engines through backend
      ttsStore.setText(previewText);
      await ttsStore.generateTTS();

      if (ttsStore.audioUrl) {
        const audio = new Audio(ttsStore.audioUrl);
        audio.play();
      }
    }

    ttsStore.setText(originalText);
  } catch (error) {
    console.error('Voice preview error:', error);
    message.error('Voice preview failed: ' + error.message);
  } finally {
    previewLoading.value = false;
  }
}


function toggleCapcutVoiceEnabled() {
  if(isCapcutVoice.value){
    isCapcutVoice.value = true;
  } else {
    isCapcutVoice.value = false;
  }
}






// Load speakers on component mount
onMounted(async () => {
  if (!ttsStore.speakers || ttsStore.speakers.length === 0) {
    console.log('Loading CapCut/TikTok speakers...');
    await ttsStore.fetchCapCutSpeakers();
  }
});
</script>

<template>
  <!-- Container chiếm full height, có scroll riêng -->
  <div class="flex-1 flex flex-col bg-gray-900 text-white">
    <!-- Header - cố định không scroll -->
    <div class="flex-shrink-0 p-4 border-b border-gray-700">
      <h2 class="text-lg font-medium text-white">Text to Speech</h2>
    </div>

    <!-- Content area - có thể scroll -->
    <div class="flex-1 min-h-0 overflow-auto p-4">
      <div class="space-y-4 max-w-2xl">
        <!-- Multi-Engine Voice Selection Info -->
        <div class="mb-4 p-3 bg-blue-900/20 rounded-lg border border-blue-700">
          <h3 class="text-sm font-medium text-blue-300 mb-2">🎯 Multi-Engine Voice Selection</h3>
          <p class="text-xs text-blue-200">
            Select any voice from any TTS engine. The system will automatically use the correct engine for each voice.
          </p>
        </div>

        <!-- Voice Selection -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <label for="speaker" class="block text-sm font-medium text-gray-300">
              Voice ({{ voiceStats.total }} available)
            </label>
            <div class="flex gap-2 text-xs">
              <span v-if="voiceStats.male > 0" class="text-cyan-400">♂ {{ voiceStats.male }}</span>
              <span v-if="voiceStats.female > 0" class="text-pink-400">♀ {{ voiceStats.female }}</span>
              <span v-if="voiceStats.premium > 0" class="text-yellow-400">★ {{ voiceStats.premium }}</span>
            </div>
          </div>

          <!-- Engine Statistics -->
          <div class="mb-2 p-2 bg-gray-800 rounded text-xs">
            <div class="flex flex-wrap gap-2">
              <span v-if="voiceStats.engines.capcut > 0" class="text-blue-400">
                CapCut: {{ voiceStats.engines.capcut }}
              </span>
              <span v-if="voiceStats.engines.openai > 0" class="text-green-400">
                OpenAI: {{ voiceStats.engines.openai }}
              </span>
              <span v-if="voiceStats.engines.mimimax > 0" class="text-orange-400">
                Mimimax: {{ voiceStats.engines.mimimax }}
              </span>
              <span v-if="voiceStats.engines.vbee > 0" class="text-red-400">
                VBee: {{ voiceStats.engines.vbee }}
              </span>
            </div>
          </div>
          <a-select
            id="speaker"
            v-model:value="ttsStore.selectedSpeaker"
            class="w-full"
            placeholder="Select a voice"
            show-search
            :filter-option="filterVoiceOption"
          >
            <a-select-option v-for="(speaker, index) in speakers" :key="index" :value="speaker.id">
              <div class="flex items-center justify-between">
                <div class="flex flex-col">
                  <span class="font-medium">{{ speaker.name }}</span>
                  <span v-if="speaker.language" class="text-xs text-gray-500">{{ speaker.language }}</span>
                </div>
                <div class="flex items-center gap-1">
                  <a-tag :color="getEngineColor(speaker.engine)" size="small">
                    {{ speaker.engineName }}
                  </a-tag>
                  <a-tag v-if="speaker.level" :color="getTagColor(speaker.level)" size="small">
                    {{ speaker.level }}
                  </a-tag>
                  <a-tag v-if="speaker.gender" :color="speaker.gender === 'female' ? 'pink' : 'cyan'" size="small">
                    {{ speaker.gender }}
                  </a-tag>
                </div>
              </div>
            </a-select-option>
          </a-select>

          <!-- Voice Preview Button -->
          <div class="mt-2 flex justify-end">
            <a-button
              size="small"
              @click="previewVoice"
              :loading="previewLoading"
              :disabled="!ttsStore.selectedSpeaker"
            >
              🔊 Preview Voice
            </a-button>
          </div>
        </div>

        <div>
          
          <label for="text" class="block text-sm font-medium text-gray-300 mb-2">
            Text
          </label>
          <a-textarea
            id="text"
            v-model:value="text"
            placeholder="Enter text to convert to speech"
            :rows="6"
            class="w-full"
          />
        </div>

        <div class="flex justify-end gap-2 items-center">
              <a-checkbox @change="toggleCapcutVoiceEnabled" v-model:checked="isCapcutVoice">
                Capcut
              </a-checkbox>
          <a-button type="primary" @click="generateTTS" :loading="isLoading">
            Generate Speech
          </a-button>
        </div>

        <div v-if="error" class="p-3 bg-red-900/50 text-red-300 rounded border border-red-700">
          {{ error }}
        </div>

        <div v-if="audioUrl" class="bg-gray-800 p-4 rounded-lg border border-gray-700">
          <h3 class="text-md font-medium text-white mb-3">Generated Audio</h3>
          <audio controls class="w-full mb-3" :src="audioUrl"></audio>
          <div class="text-sm text-gray-400 mb-3">
            Duration: {{ formatDuration(ttsStore.audioDuration) }}
          </div>
          <div class="flex space-x-2">
            <a-button type="default" size="small" :href="audioUrl" target="_blank">
              Open in New Tab
            </a-button>
            <a-button type="default" size="small" @click="downloadAudio">
              Download
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
