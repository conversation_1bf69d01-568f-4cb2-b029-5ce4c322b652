<template>
    <div class="subtitleInfo-wrapper card">
        <table class="subtitleInfo-header">
            <thead>
                <tr class="subtitleInfo-row">
                    <th class="subtitleInfo-time">Start</th>
                    <th class="subtitleInfo-time">End</th>
                    <th class="subtitleInfo-text">Subtitle Text</th>
                </tr>
            </thead>
        </table>
        <simplebar force-visible>
            <table class="subtitleInfo-content">
                <transition-group name="line" tag="tbody">
                    <tr :class="{ 'subtitleInfo-row': true, 'subtitleInfo-row-merge-highlight': selectStart === index || selectStart <= index && index <= selectEnd, 'active': subtitleInfo.startFrame <= currentFrame && currentFrame < subtitleInfo.endFrame }"
                        v-for="subtitleInfo, index in subtitleInfos" :key="subtitleInfo.id" @pointerenter="setSelectEnd(index)"
                        @click="setCurrentFrame((subtitleInfo.endFrame - subtitleInfo.startFrame) / 2 + subtitleInfo.startFrame)">
                        <td class="subtitleInfo-time">
                            <input v-model="subtitleInfo.startTimeValidated" @change="updateInput" />
                        </td>
                        <td class="subtitleInfo-time">
                            <input v-model="subtitleInfo.endTimeValidated" @change="updateInput" />
                        </td>
                        <td class="subtitleInfo-text">
                            <input :title="subtitleInfo.text" v-model="subtitleInfo.text" placeholder="Enter subtitle here" />
                        </td>
                        <td class="subtitleInfo-buttons">
                            <button @click="addSubtitle(index)" title="Add">
                                <img src="@/assets/subtitle-add.svg" alt="" />
                            </button>
                            <button @click="removeSubtitle(index)" title="Remove">
                                <img src="@/assets/subtitle-delete.svg" alt="" />
                            </button>
                            <button @click="setCurrentFrame((subtitleInfo.endFrame - subtitleInfo.startFrame) / 2 + subtitleInfo.startFrame)" title="Jump">
                                <img src="@/assets/subtitle-locate.svg" alt="" />
                            </button>
                            <button @click="selectStart = index; selectEnd = index;" v-if="selectStart === -1" title="Merge">
                                <img src="@/assets/subtitle-merge-start.svg" alt="" />
                            </button>
                            <button @click="selectStart = -1; selectEnd = -1" v-else-if="selectStart === index" title="Cancel Merge">
                                <img src="@/assets/subtitle-merge-start.svg" alt="" />
                            </button>
                            <button @click="mergeSubtitles" v-else-if="index > selectStart" title="Merge">
                                <img src="@/assets/subtitle-merge-end.svg" alt="" />
                            </button>
                        </td>
                    </tr>
                </transition-group>
            </table>
        </simplebar>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, toRefs } from 'vue'
import simplebar from 'simplebar-vue'
import '@/assets/simplebar.css'

import { SubtitleInfo } from '@/lib/SubtitleInfo'

// Props
defineProps<{
  subtitleInfos: SubtitleInfo[]
  currentFrame: number
}>()

// Emits
const emit = defineEmits<{
  (e: 'update:subtitleInfos', value: SubtitleInfo[]): void
  (e: 'update:currentFrame', value: number): void
}>()

// Local states
const selectStart = ref(-1)
const selectEnd = ref(-1)

const subtitleInfosUndo = ref<SubtitleInfo[][]>([])
const subtitleInfosRedo = ref<SubtitleInfo[][]>([])

function updateInput() {
  emit('update:subtitleInfos', [...subtitleInfos].sort((a, b) => a.startFrame - b.startFrame))
}

function setCurrentFrame(frame: number) {
  emit('update:currentFrame', frame)
}

function setSelectEnd(index: number) {
  if (selectStart.value !== -1) {
    selectEnd.value = index
  }
}

function addSubtitle(index: number) {
  subtitleInfosRedo.value = []
  subtitleInfosUndo.value.push([...subtitleInfos])
  const newSubtitle = new SubtitleInfo(subtitleInfos[index])
  if (!subtitleInfos[index].fps) {
    throw new Error('cannot get fps from other subtitles')
  }
  newSubtitle.startFrame = newSubtitle.endFrame
  newSubtitle.generateTime(subtitleInfos[index].fps!)
  newSubtitle.text = ''
  subtitleInfos.splice(index, 0, newSubtitle)
  updateInput()
}

function removeSubtitle(index: number) {
  subtitleInfosRedo.value = []
  subtitleInfosUndo.value.push([...subtitleInfos])
  subtitleInfos.splice(index, 1)
  updateInput()
}

function mergeSubtitles() {
  if (selectStart.value === -1 || selectEnd.value === -1 || selectStart.value >= selectEnd.value) return
  subtitleInfosRedo.value = []
  subtitleInfosUndo.value.push([...subtitleInfos])
  subtitleInfos[selectStart.value].endFrame = subtitleInfos[selectEnd.value].endFrame
  subtitleInfos.splice(selectStart.value + 1, selectEnd.value - selectStart.value)
  selectStart.value = -1
  selectEnd.value = -1
  updateInput()
}

function undo(event: KeyboardEvent) {
  if (event.ctrlKey && event.key === 'z') {
    if (subtitleInfosUndo.value.length) {
      subtitleInfosRedo.value.push([...subtitleInfos])
      const last = subtitleInfosUndo.value.pop()
      if (last) emit('update:subtitleInfos', last)
    }
  }
}

function redo(event: KeyboardEvent) {
  if (event.ctrlKey && event.key === 'y') {
    if (subtitleInfosRedo.value.length) {
      subtitleInfosUndo.value.push([...subtitleInfos])
      const next = subtitleInfosRedo.value.pop()
      if (next) emit('update:subtitleInfos', next)
    }
  }
}

// onMounted(() => {
//   window.addEventListener('keyup', undo)
//   window.addEventListener('keyup', redo)
// })

// onBeforeUnmount(() => {
//   window.removeEventListener('keyup', undo)
//   window.removeEventListener('keyup', redo)
// })
</script>
<style scoped>
.subtitleInfo-wrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 16px;
    position: relative;
}

.subtitleInfo-row {
    display: flex;
    flex-direction: row;
    padding: 12px 24px;
    cursor: default;
    min-height: 48px;
    position: relative;
    transition: all 0.2s;
}

.subtitleInfo-row:nth-child(2n+1) {
    background: rgba(18, 44, 63, 0.2);
}

.subtitleInfo-content .subtitleInfo-row:hover {
    background-color: #1c425f;
}

.subtitleInfo-content .subtitleInfo-row.active {
    background-color: rgba(24, 162, 180, 0.8);
}

.subtitleInfo-row-merge-highlight {
    background-color: #133047 !important;
}

.subtitleInfo-header {
    width: 100%;
    background: #091620;
    color: rgba(255, 255, 255, 0.3);
}

.subtitleInfo-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    margin: 0 0 50px;
}

.subtitleInfo-content .subtitleInfo-text {
    flex-grow: 2;
    display: flex;
}

.subtitleInfo-header .subtitleInfo-row .subtitleInfo-text {
    margin: auto 0;
}

.subtitleInfo-time {
    margin: auto 16px auto 0;
    width: 90px;
}

.subtitleInfo-content input {
    background: transparent;
    border: none;
    width: 100%;
}

.subtitleInfo-content .subtitleInfo-time input {
    color: rgba(255, 255, 255, 0.4);
}

.subtitleInfo-content .subtitleInfo-text input {
    color: rgba(255, 255, 255, 0.8);
    text-overflow: ellipsis;
    flex-grow: 1;
}

.subtitleInfo-content .subtitleInfo-text input::placeholder {
    opacity: 0.5;
}

.subtitleInfo-buttons {
    display: none;
    position: absolute;
    bottom: -14px;
    right: 16px;
    background: #1c425f;
    border: 1px solid rgba(0, 0, 0, 0.37);
    border-radius: 4px;
    padding: 2px 4px;
    z-index: 10;
}

.subtitleInfo-buttons button {
    border: transparent;
    border-radius: 2px;
    display: flex;
    background-color: transparent;
    padding: 3px 4px;
    transition: 0.2s all;
    cursor: pointer;
}

.subtitleInfo-buttons button img {
    width: 16px;
    height: 16px;
    margin: auto;
}

.subtitleInfo-buttons button:hover {
    background: #fff 20;
    border: #fff 20;
}

.subtitleInfo-content .subtitleInfo-row:hover .subtitleInfo-buttons {
    display: flex;
}

.subtitleInfo-content .subtitleInfo-text input:focus + .subtitleInfo-buttons {
    display: none;
}

.line-enter-active,
.line-leave-active {
    transition: all 0.2s;
}

.line-enter {
    opacity: 0;
    transform: translate(0, -8px);
}

.line-leave-to {
    opacity: 0;
    transform: translate(0, -8px);
}

.line-leave-active {
    position: absolute;
}

</style>

<style>
[data-simplebar] {
    position: absolute;
    top: 48px;
    bottom: 0;
    left: 0;
    right: 0;
}

.simplebar-scrollbar::before {
    opacity: 0.5;
}

</style>
