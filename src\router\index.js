import { createRouter, createWebHashHistory } from 'vue-router';

import HomePage from '@/pages/HomePage.vue';
import Dashboard from '@/pages/Dashboard.vue';
import Summary from '@/pages/Summary.vue';
import SummaryIntro from '@/pages/SummaryIntro.vue';
import TrainGPT from '@/pages/TrainGPT.vue';
import Configs from '@/pages/Configs.vue';
// import MainWindow from '@/pages/MainWindow.vue';


const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
  },
  {
    path: '/summary',
    name: 'Summary',
    component: Summary,
  },  {
    path: '/summary-intro',
    name: 'SummaryIntro',
    component: SummaryIntro,
  },
  {
    path: '/train-gpt',
    name: 'TrainGPT',
    component: TrainGPT
  },
  {
    path: '/download',
    name: 'DownloadTool',
    component: () => import('@/pages/DownloadTool.vue')
  },
  {
    path: '/configs',
    name: 'Configs',
    component: Configs
  },
  {
    path: '/export-srt-capcut',
    name: 'ExportSrtCapcut',
    component: () => import('@/pages/ExportSrtCapcut.vue')
  },
    // {
    //     path: '/MainWindow',
    //     name: 'MainWindow',
    //     component: MainWindow
    // }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach(async function (to, from, next) {
  next();
});


export default router;
