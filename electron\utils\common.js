function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}
function check_long_text(text) {
    info = new Object();
    info.status = true;
    info.errors = [];
    
    text = text.replaceAll('、',',');
    text = text.replaceAll('，',',');
    text = text.replaceAll('。','.');
    text = text.replaceAll('！','!');
    text = text.replaceAll('？','?');
    text = text.replaceAll('：',':');
    text = text.replaceAll('；',';');
    text = text.replaceAll('：',':');
    text = text.replaceAll('；',';');
    const sentences = text.split(/[.,!;]/).map(sentence => sentence.trim()).filter(sentence => sentence.length > 0);
    
    // <PERSON>ểm tra từng câu
    sentences.forEach((sentence, index) => {
        if (sentence.length > 1000) {
            info.status = false;
            info.errors.push(`Câu ${index + 1} dài ${sentence.length} ký tự, vượt quá giới hạn 1000 ký tự: "${sentence.substring(0, 50)}..."`)
        }
    });
    
    // Nếu không có câu nào vượt quá 1000 ký tự
    if (!sentences.some(sentence => sentence.length > 1000)) {
        info.status = true
        return info
    }

    return info
}

function size_format(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function slug(text) {
    // Bảng chuyển đổi ký tự có dấu thành không dấu
    const map = {
        'a': 'àáạảãâầấậẩẫăằắặẳẵ',
        'e': 'èéẹẻẽêềếệểễ',
        'i': 'ìíịỉĩ',
        'o': 'òóọỏõôồốộổỗơờớợởỡ',
        'u': 'ùúụủũưừứựửữ',
        'y': 'ỳýỵỷỹ',
        'd': 'đ',
        'A': 'ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ',
        'E': 'ÈÉẸẺẼÊỀẾỆỂỄ',
        'I': 'ÌÍỊỈĨ',
        'O': 'ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ',
        'U': 'ÙÚỤỦŨƯỪỨỰỬỮ',
        'Y': 'ỲÝỴỶỸ',
        'D': 'Đ'
    };

    // Chuyển đổi chuỗi
    let slug = text;
    
    // Thay thế từng ký tự có dấu
    for (let key in map) {
        const regex = new RegExp(`[${map[key]}]`, 'g');
        slug = slug.replace(regex, key);
    }

    // Chuyển thành chữ thường, thay khoảng trắng bằng dấu gạch ngang
    slug = slug
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-') // Thay các ký tự không phải chữ và số thành '-'
        .replace(/-+/g, '-') // Xóa các dấu '-' liên tiếp
        .replace(/^-|-$/g, ''); // Xóa dấu '-' ở đầu và cuối

    return slug;
}

module.exports = {
    uuidv4,
    check_long_text,
    size_format,
    slug
}