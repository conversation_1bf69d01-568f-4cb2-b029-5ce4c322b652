const Tesseract = require('tesseract.js');
const { Jimp } = require('jimp');

// Simple but effective JavaScript OCR for Chinese
class SimpleJSChineseOCR {
  constructor() {
    this.configs = [
      {
        name: 'Chinese Simplified',
        lang: 'chi_sim',
        options: {
          tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
          tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
        }
      },
      {
        name: 'Chinese Traditional',
        lang: 'chi_tra',
        options: {
          tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
          tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
        }
      },
      {
        name: 'Chinese Mixed',
        lang: 'chi_sim+chi_tra',
        options: {
          tessedit_pageseg_mode: Tesseract.PSM.AUTO,
          tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
        }
      },
      {
        name: 'Chinese + English',
        lang: 'chi_sim+eng',
        options: {
          tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
          tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
        }
      }
    ];
  }

  async preprocessImage(imagePath) {
    const image = await Jimp.read(imagePath);
    const { width, height } = image.bitmap;
    
    console.log(`📐 Original: ${width}x${height}`);
    
    // Create multiple preprocessing variants
    const variants = [];
    
    // Variant 1: Minimal processing (often works best)
    let variant1 = image.clone();
    if (width < 500 || height < 150) {
      const scale = Math.max(500 / width, 150 / height);
      variant1 = variant1.scale(scale);
      console.log(`🔍 Variant 1: Scaled by ${scale.toFixed(2)}x`);
    }
    variant1.greyscale();
    await variant1.write('variant1.jpg');
    variants.push({ name: 'Minimal', file: 'variant1.jpg' });
    
    // Variant 2: Enhanced contrast
    let variant2 = image.clone();
    if (width < 500 || height < 150) {
      variant2 = variant2.scale(Math.max(500 / width, 150 / height));
    }
    variant2.greyscale().contrast(0.5).normalize();
    await variant2.write('variant2.jpg');
    variants.push({ name: 'Enhanced', file: 'variant2.jpg' });
    
    // Variant 3: High scale for small images
    if (width < 300 || height < 100) {
      let variant3 = image.clone().scale(3);
      variant3.greyscale().normalize();
      await variant3.write('variant3.jpg');
      variants.push({ name: 'High Scale', file: 'variant3.jpg' });
      console.log('🔍 Variant 3: High scale (3x) for small image');
    }
    
    return variants;
  }

  async recognizeWithConfig(imagePath, config) {
    try {
      console.log(`🔍 Testing ${config.name}...`);
      
      const result = await Tesseract.recognize(imagePath, config.lang, {
        logger: m => {
          if (m.status === 'recognizing text' && m.progress === 1) {
            process.stdout.write('✅ ');
          }
        },
        ...config.options
      });
      
      const rawText = result.data.text.trim();
      const cleanText = rawText.replace(/\s+/g, '');
      const confidence = result.data.confidence;
      
      console.log(`📝 ${config.name}: "${cleanText}" (${confidence.toFixed(1)}%)`);
      
      return {
        text: cleanText,
        rawText: rawText,
        confidence: confidence,
        config: config.name
      };
      
    } catch (error) {
      console.error(`❌ ${config.name} failed:`, error.message);
      return null;
    }
  }

  async recognizeText(imagePath) {
    console.log('🎯 Starting Simple JS Chinese OCR...');
    
    // First, try with original image
    console.log('\n--- Testing with original image ---');
    const originalResults = [];
    
    for (const config of this.configs) {
      const result = await this.recognizeWithConfig(imagePath, config);
      if (result && result.confidence > 20) {
        originalResults.push(result);
      }
    }
    
    // Then try with preprocessed variants
    console.log('\n--- Testing with preprocessed variants ---');
    const variants = await this.preprocessImage(imagePath);
    const variantResults = [];
    
    for (const variant of variants) {
      console.log(`\n🔧 Testing ${variant.name} variant:`);
      
      for (const config of this.configs) {
        const result = await this.recognizeWithConfig(variant.file, config);
        if (result && result.confidence > 20) {
          result.variant = variant.name;
          variantResults.push(result);
        }
      }
    }
    
    // Combine all results
    const allResults = [...originalResults, ...variantResults];
    
    if (allResults.length === 0) {
      console.log('❌ No good results found');
      return null;
    }
    
    // Find best result
    const bestResult = allResults.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );
    
    console.log('\n📊 All Results:');
    allResults.forEach(result => {
      const variant = result.variant ? ` (${result.variant})` : ' (Original)';
      console.log(`   ${result.config}${variant}: "${result.text}" (${result.confidence.toFixed(1)}%)`);
    });
    
    console.log(`\n🏆 Best: ${bestResult.config}${bestResult.variant ? ` (${bestResult.variant})` : ' (Original)'}`);
    console.log(`📝 Text: "${bestResult.text}"`);
    console.log(`🎯 Confidence: ${bestResult.confidence.toFixed(1)}%`);
    
    return bestResult;
  }

  // Quick recognition with just the best-performing config
  async quickRecognize(imagePath) {
    console.log('⚡ Quick Chinese OCR...');
    
    // Use the most reliable config first
    const quickConfig = {
      name: 'Quick Chinese',
      lang: 'chi_sim',
      options: {
        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
        tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
      }
    };
    
    // Try original first
    let result = await this.recognizeWithConfig(imagePath, quickConfig);
    
    if (result && result.confidence > 70) {
      console.log('✅ Quick success with original image');
      return result;
    }
    
    // Try with minimal preprocessing
    console.log('🔧 Trying with preprocessing...');
    const image = await Jimp.read(imagePath);
    const { width, height } = image.bitmap;
    
    let processed = image.clone();
    if (width < 400 || height < 120) {
      const scale = Math.max(400 / width, 120 / height);
      processed = processed.scale(scale);
    }
    processed.greyscale().normalize();
    await processed.write('quick_processed.jpg');
    
    result = await this.recognizeWithConfig('quick_processed.jpg', quickConfig);
    
    if (result) {
      result.variant = 'Quick Processed';
      console.log('✅ Success with preprocessing');
    }
    
    return result;
  }
}

module.exports = { SimpleJSChineseOCR };

// Example usage
if (require.main === module) {
  const ocr = new SimpleJSChineseOCR();
  
  // Try quick recognition first
  ocr.quickRecognize('./logs/149k.png').then(result => {
    if (result && result.confidence > 60) {
      console.log('\n🎯 Quick Result:');
      console.log(`📝 Text: "${result.text}"`);
      console.log(`🎯 Confidence: ${result.confidence.toFixed(1)}%`);
    } else {
      console.log('\n🔄 Quick recognition insufficient, trying full recognition...');
      return ocr.recognizeText('./logs/149k.png');
    }
  }).then(fullResult => {
    if (fullResult) {
      console.log('\n🎯 Full Recognition Result:');
      console.log(`📝 Text: "${fullResult.text}"`);
      console.log(`🎯 Confidence: ${fullResult.confidence.toFixed(1)}%`);
    }
  });
}
