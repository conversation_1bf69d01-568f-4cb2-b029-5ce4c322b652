/**
 * Convert emoji to text alternatives for ASS subtitles
 * @param {string} text - Text with potential emoji
 * @returns {string} - Text with emoji converted to alternatives
 */
function convertEmojiToTextASS(text) {
  if (!text) return '';

  const emojiMap = {
    '😱': '[SHOCKED]',
    '🔥': '[FIRE]',
    '💯': '[100]',
    '🚀': '[ROCKET]',
    '🎵': '[MUSIC]',
    '✨': '[SPARKLES]',
    '❤️': '[HEART]',
    '👍': '[THUMBS_UP]',
    '👎': '[THUMBS_DOWN]',
    '😂': '[LAUGHING]',
    '😭': '[CRYING]',
    '🤔': '[THINKING]',
    '💪': '[STRONG]',
    '🎉': '[PARTY]',
    '⚡': '[LIGHTNING]',
    '🌟': '[STAR]',
    '💰': '[MONEY]',
    '🎯': '[TARGET]',
    '🔴': '[RED_CIRCLE]',
    '🟢': '[GREEN_CIRCLE]',
    '⭐': '[STAR]',
    '📈': '[CHART_UP]',
    '📉': '[CHART_DOWN]',
    '🎬': '[MOVIE]',
    '📱': '[PHONE]',
    '💻': '[LAPTOP]',
    '🎮': '[GAME]',
    '🍕': '[PIZZA]',
    '☕': '[COFFEE]',
    '🌈': '[RAINBOW]',
    '🎊': '[CONFETTI]'
  };

  let result = text;
  for (const [emoji, replacement] of Object.entries(emojiMap)) {
    result = result.replace(new RegExp(emoji, 'g'), replacement);
  }

  return result;
}

/**
 * Escape text for ASS subtitle format to handle emoji and special characters
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text safe for ASS format
 */
function escapeTextForASS(text) {
  if (!text) return '';

  // First convert emoji to text alternatives
  // let processedText = convertEmojiToTextASS(text);

  return text
    // ASS specific escapes
    .replace(/\\/g, '\\\\')  // Escape backslashes first
    .replace(/\{/g, '\\{')   // Escape opening braces
    .replace(/\}/g, '\\}')   // Escape closing braces
    .replace(/\n/g, '\\N')   // Convert newlines to ASS format
    .replace(/\r/g, '');     // Remove carriage returns
}

// Hàm tạo header ASS với tùy chọn màu sắc
const createASSHeader1 = (options = {}) => {
  // Cấu hình mặc định
  const config = {
    fontSize: options.fontSize || 48,
    fontName: options.fontName || 'Arial',

    // Màu chữ (Primary Color) - format: &H00BBGGRR (BGR hex)
    textColor: options.textColor || '&H00FFFFFF', // Trắng

    // Màu nền (Back Color) - format: &HAARRGGBB
    // AA = alpha (transparency), 00 = opaque, FF = transparent
    backgroundColor: options.backgroundColor || '&H80000000', // Đen semi-transparent

    // Màu viền (Outline Color)
    outlineColor: options.outlineColor || '&H00000000', // Đen

    // Kích thước viền và shadow
    outlineSize: options.outlineSize || 2,
    shadowSize: options.shadowSize || 2,

    // Alignment: 1=left, 2=center, 3=right, 5=top-left, 6=top-center, etc.
    alignment: options.alignment || 2,

    // Margins
    marginLeft: options.marginLeft || 20,
    marginRight: options.marginRight || 20,
    marginVertical: options.marginVertical || 30,

    // Bold, Italic
    bold: options.bold ? -1 : 0,
    italic: options.italic ? -1 : 0,

    // Background box style
    // BorderStyle: 1 = outline and shadow, 3 = background box, 4 = background box with outline
    borderStyle: options.borderStyle || 3, // 3 = background box

    // Padding cho background box (chỉ hoạt động với BorderStyle 3 hoặc 4)
    // Sử dụng MarginV để tạo padding vertical
    // Horizontal padding được tạo bằng cách thêm spaces trong text
  };
  const resolution = options.resolution || {};
  // Use dynamic ASS resolution from video processing
  // This supports both original resolution and upscaled resolution
  // const assResX = options.assResolution?.width || 1920;
  // const assResY = options.assResolution?.height || 1080;
  const assResX = resolution.width || 1920;
  const assResY = resolution.height || 1080;

  const header = `[Script Info]
Title: Untitled
ScriptType: v4.00+
PlayResX: ${assResX}
PlayResY: ${assResY}
WrapStyle: 0
Collisions: Reverse

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${config.fontName},${config.fontSize},${config.textColor},&H000000FF,${config.outlineColor},${config.backgroundColor},${config.bold},${config.italic},0,0,100,100,0,0,${config.borderStyle},${config.outlineSize},${config.shadowSize},${config.alignment},${config.marginLeft},${config.marginRight},${config.marginVertical},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`.trim();

  return header;
};
// Hàm sinh file ASS từ danh sách phụ đề
// const generateASSSubtitle1 = (srtArray, options = {}) => {
//   const header = createASSHeader(options);

//   const formatTime = (seconds) => {
//     const hours = Math.floor(seconds / 3600);
//     const minutes = Math.floor((seconds % 3600) / 60);
//     const secs = (seconds % 60).toFixed(2).padStart(5, '0');
//     return `${hours}:${minutes.toString().padStart(2, '0')}:${secs}`;
//   };
  
  
//   const assOptions = options.assOptions || {};
//   const resolution = options.resolution || {};
//   const upscaled = upscaleAssOptions(assOptions, resolution);
//   console.log('upscaled', upscaled);
//   let events = '';
//   srtArray.forEach((srt, index) => {
//     const startTime = formatTime(srt.adjustedStartTime || srt.startTime);
//     const endTime = formatTime(srt.adjustedEndTime || srt.endTime);
//     const text = srt.translatedText || srt.text || '';
//     const paddedText = options.addPadding ? ` ${text} ` : text;
//     const srtAssOptions = srt.textSubtitleSettings?.assOptions || null
//     const srtUpscaled = srtAssOptions ? upscaleAssOptions(srtAssOptions, resolution) : upscaled;
//     const tag = buildASSPositionTag(srtUpscaled, resolution);

//     const line = `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,{${tag}}${paddedText}\n`;
//     events += line;
//   });

//   return header + '\n\n' + events;
// };
const createASSHeader = (resolution, stylesSection) => {
  const assResX = resolution.width || 1920;
  const assResY = resolution.height || 1080;

  return `[Script Info]
Title: Untitled
ScriptType: v4.00+
PlayResX: ${assResX}
PlayResY: ${assResY}
WrapStyle: 0
Collisions: Reverse

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
${stylesSection.trim()}

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`.trim();
};
const generateASSStyles = (uniqueStylesMap, resolution) => {
  let styles = '';

  for (const { name, options } of uniqueStylesMap.values()) {
    const cfg = {
      ...{
        fontSize: 48,
        fontName: 'Arial',
        textColor: '&H00FFFFFF',
        backgroundColor: '&H80000000',
        outlineColor: '&H00000000',
        outlineSize: 2,
        shadowSize: 2,
        alignment: 2,
        marginLeft: 20,
        marginRight: 20,
        marginVertical: 30,
        bold: 0,
        italic: 0,
        borderStyle: 3,
      },
      ...options,
    };

    styles += `Style: ${name},${cfg.fontName},${cfg.fontSize},${cfg.textColor},&H000000FF,${cfg.outlineColor},${cfg.backgroundColor},${cfg.bold ? -1 : 0},${cfg.italic ? -1 : 0},0,0,100,100,0,0,${cfg.borderStyle},${cfg.outlineSize},${cfg.shadowSize},${cfg.alignment},${cfg.marginLeft},${cfg.marginRight},${cfg.marginVertical},1\n`;
  }

  return styles;
};



const generateASSSubtitle = (srtArray, options = {}) => {
  const resolution = options.resolution || {};
  const defaultStyle = generateASSStyles(new Map([
    [ 'default', { name: 'Default', options } ]
  ]), resolution);
  // console.log('generateASSSubtitle', options);
  const uniqueStylesMap = new Map();
  let styleCounter = 1;

  srtArray.forEach((srt) => {
    const srtAssOptions = srt?.textSubtitleSettings || {};
    if(srtAssOptions){
      const subtitleOptions = {
        fontSize: srtAssOptions.fontSize || 48,
        fontName: srtAssOptions.fontFamily || 'Arial',
        textColor: srtAssOptions.assColors?.text || '&H000000',
        backgroundColor: srtAssOptions.assColors?.background || '&H000000',
        outlineColor: srtAssOptions.assColors?.border || '&H000000',
        borderStyle: 4,
        bold: srtAssOptions.bold || true,
        addPadding: true,
        alignment: 2,
        marginVertical: 50,
        resolution: resolution,
        assOptions: srtAssOptions?.assOptions
      };
      const key = JSON.stringify(srtAssOptions?.assOptions || {});
      if (!uniqueStylesMap.has(key)) {
        uniqueStylesMap.set(key, { name: `Style${styleCounter++}`, options: subtitleOptions });
      }
    }
  });

  const fullStyleSection = defaultStyle + '\n' + generateASSStyles(uniqueStylesMap, resolution);
  const header = createASSHeader(resolution, fullStyleSection);


  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = (seconds % 60).toFixed(2).padStart(5, '0');
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs}`;
  };
  const assOptions = options.assOptions || {};
  const upscaled = upscaleAssOptions(assOptions, resolution);
  let events = '';
  srtArray.forEach((srt) => {
    const srtAssOptions = srt?.textSubtitleSettings?.assOptions || null
    const key = JSON.stringify(srtAssOptions || {});
    const styleName = srtAssOptions
      ? uniqueStylesMap.get(key).name
      : 'Default';

    // ====== FIX: Ưu tiên sử dụng adjustedStartTime/adjustedEndTime nếu có ======
    const startTime = formatTime(srt.adjustedStartTime !== undefined ? srt.adjustedStartTime : srt.startTime);
    const endTime = formatTime(srt.adjustedEndTime !== undefined ? srt.adjustedEndTime : srt.endTime);

    // Debug log for timing issues
    // if (srt.index === 21) { // First segment of batch 1
      // console.log(`🐛 DEBUG Segment ${srt.index}: adjustedStartTime=${srt.adjustedStartTime}, startTime=${srt.startTime}, formatted: ${startTime} -> ${endTime}`);
    // }
    const text = srt.translatedText || srt.text || '';
    const escapedText = escapeTextForASS(text);
    const paddedText = options.addPadding ? ` ${escapedText} ` : escapedText;

    const srtUpscaled = upscaled //srtAssOptions ? upscaleAssOptions(srtAssOptions, resolution) : upscaled;
    const tag = buildASSPositionTag(srtUpscaled, resolution);

    events += `Dialogue: 0,${startTime},${endTime},${styleName},,0,0,0,,{${tag}}${paddedText}\n`;
  });

  return header + '\n\n' + events;
};







function buildASSPositionTag(assOptions, resolution) {
  let tags = '';

  // Check if we need custom positioning (not at default center 0%, 0%)
  const needsCustomPosition =
    (assOptions.assPos && assOptions.assPos.x !== undefined && assOptions.assPos.y !== undefined) ||
    (assOptions.pos && assOptions.pos.x !== undefined && assOptions.pos.y !== undefined);

  // Only add alignment if we're using custom positioning or non-default alignment
  const align = assOptions.align !== undefined ? assOptions.align : 5; // Default to middle center (5)
  if (needsCustomPosition || align !== 5) {
    tags += `\\an${align}`;
  }

  // Font size - use ASS font size if available
  if (assOptions.assFontSize) {
    tags += `\\fs${assOptions.assFontSize}`;
  } else if (assOptions.fontSize) {
    tags += `\\fs${assOptions.fontSize}`;
  }

  // Rotation
  if (assOptions.rotation !== undefined && assOptions.rotation !== 0) {
    tags += `\\frz${assOptions.rotation}`;
  }

  // Position - only use if not at default center position
  if (needsCustomPosition) {
    if (assOptions.assPos && assOptions.assPos.x !== undefined && assOptions.assPos.y !== undefined) {
      // Use direct ASS coordinates (already in ASS coordinate system)
      tags += `\\pos(${Math.round(assOptions.assPos.x)},${Math.round(assOptions.assPos.y)})`;
    } else if (assOptions.pos && assOptions.pos.x !== undefined && assOptions.pos.y !== undefined) {
      // Use scaled coordinates (fallback)
      tags += `\\pos(${Math.round(assOptions.pos.x)},${Math.round(assOptions.pos.y)})`;
    }
  }

  return tags;
}
const cssToASSColor = (cssHex, alpha = '00') => {
  const hex = cssHex.replace('#', '');
  const r = hex.substring(0, 2);
  const g = hex.substring(2, 4);
  const b = hex.substring(4, 6);
  return `&H${alpha}${b}${g}${r}`.toUpperCase();
};
function convertPercentToPixel(percent, total) {
  return Math.round((percent / 100) * total);
}
function upscaleAssOptions(options, targetResolution) {
  const { width: srcW, height: srcH } = options.assResolution;
  const { width: dstW, height: dstH } = targetResolution;
  if (srcW == dstW && srcH == dstH) {
    return options;
  }
  const scaleX = dstW / srcW;
  const scaleY = dstH / srcH;

  return {
    ...options,
    assResolution: targetResolution,
    assPos: {
      x: Math.round(options.pos.x * scaleX),
      y: Math.round(options.pos.y * scaleY),
    },
    pos: {
      x: Math.round(options.pos.x * scaleX),
      y: Math.round(options.pos.y * scaleY),
    },
    assFontSize: Math.round(options.fontSize * scaleY),
  };
}

function clampAtTempo(value) {
  return Math.max(0.5, Math.min(100, parseFloat(value)));
}

module.exports = {
  createASSHeader,
  generateASSSubtitle,
  cssToASSColor,
  clampAtTempo,
  escapeTextForASS,
};
