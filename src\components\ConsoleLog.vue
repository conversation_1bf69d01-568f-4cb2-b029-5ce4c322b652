<template>
    <div class='console-log rounded w-36' @click="visible = true" v-if="logs.length > 0 || state.contentStream" :style="{ height: props.size === 'small' ? '30px' : '50px' }">
        <p v-for="(log, index) in logs" :key="index" class="log-item">
            <span :class="log?.code === 0 ? 'success' : 'error'" v-html="formatLogText(log?.data)"></span>
        </p>
    </div>
    <a-modal
      v-model:open="visible"
      :width="700"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="srt-list-container">
        <div class="mb-4 flex justify-between items-center">
          <h3 class="text-lg font-medium">Console Log</h3>
        </div>
        <div class="console-log" style="height: 500px; width: 100%; overflow-y: auto;">
            <p v-for="(log, index) in logs.slice().reverse()" :key="index" class="log-item">
                <span :class="log?.code === 0 ? 'success' : 'error'" v-html="formatLogText(log?.data)"></span>
            </p>
            <p class="log-item" v-if="state.contentStream">
                <span v-html="formatLogText(state.contentStream)"></span>
            </p>
        </div>
      </div>
    </a-modal>

</template>
<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { state } from '@/lib/state';


const props = defineProps({
  size: {
    type: String,
    default: 'default'
  }
});

const logs = ref([]);
const visible = ref(false);

const maxLogs = 500;

const setLogs = (newLogs) => {
    logs.value.unshift(newLogs);
    logs.value = logs.value.slice(0, maxLogs);
};

const allListeners = {
    'get-srt-task-res': setLogs,
    'adjust-speed-res': setLogs,
    'merge-audio-files-res': setLogs,
    'synthesize-service-res': setLogs,
    'process-audio-whisper-res': setLogs,
    'video-task': setLogs
};

let removeListener = []

onMounted(() => {
    Object.entries(allListeners).forEach(([event, listener]) => {
      const res=  window.electronAPI.on(event, listener);
      removeListener.push(res);
    });
});
onUnmounted(() => {
    removeListener.forEach((off) => off());
});

const handleCancel = () => {
  visible.value = false;
};

// Format log text with syntax highlighting
const formatLogText = (text) => {
  if (!text) return '';

  let formatted = String(text);

  // Highlight status indicators first (before numbers)
  formatted = formatted.replace(/(\[INFO\]|INFO)/gi, '<span style="color: #58a6ff; font-weight: 600;">$1</span>');
  formatted = formatted.replace(/(\[WARN\]|WARN|WARNING)/gi, '<span style="color: #f0883e; font-weight: 600;">$1</span>');
  formatted = formatted.replace(/(\[ERROR\]|ERROR|FAILED)/gi, '<span style="color: #ff7b72; font-weight: 600;">$1</span>');
  formatted = formatted.replace(/(\[DEBUG\]|DEBUG)/gi, '<span style="color: #8b949e; font-weight: 600;">$1</span>');
  formatted = formatted.replace(/(\[SUCCESS\]|SUCCESS|COMPLETED|DONE)/gi, '<span style="color: #7ee787; font-weight: 600;">$1</span>');

  // Highlight timestamps first
  formatted = formatted.replace(/(\d{1,2}:\d{2}:\d{2}(?:\.\d{3})?)/g, '<span style="color: #ffa657; font-weight: 500;">$1</span>');

  // Highlight file paths and extensions
  formatted = formatted.replace(/([\w\-\.]+\.(mp4|mp3|wav|avi|mkv|mov|srt|txt|json|xml))/gi, '<span style="color: #a5a5a5; font-weight: 500;">$1</span>');

  // Highlight URLs
  formatted = formatted.replace(/(https?:\/\/[^\s]+)/g, '<span style="color: #58a6ff; text-decoration: underline;">$1</span>');

  // Highlight ETA and speed indicators (before standalone numbers)
  formatted = formatted.replace(/(ETA\s+\d+:\d+:\d+)/gi, '<span style="color: #a5a5a5; font-style: italic;">$1</span>');
  formatted = formatted.replace(/(\d+(?:\.\d+)?(?:MB|KB|GB|TB)\/s)/gi, '<span style="color: #79c0ff; font-weight: 500;">$1</span>');

  // Highlight progress indicators
  formatted = formatted.replace(/(\[[\d\s\/]+\])/g, '<span style="color: #d2a8ff; font-weight: 500;">$1</span>');

  // Highlight standalone numbers with units (percentages, file sizes, etc.)
  formatted = formatted.replace(/\b(\d+(?:\.\d+)?(?:%|MB|KB|GB|TB|ms|s))\b/g, '<span style="color: #79c0ff; font-weight: 600;">$1</span>');

  // Highlight standalone pure numbers (not part of words/IDs)
  formatted = formatted.replace(/\b(\d+(?:\.\d+)?)\b(?![a-zA-Z])/g, '<span style="color: #79c0ff; font-weight: 600;">$1</span>');

  // Highlight special symbols and operators
  formatted = formatted.replace(/([→←↑↓✓✗⚠️📁📄🎵🎬])/g, '<span style="color: #f0883e;">$1</span>');

  return formatted;
};


</script>
<style scoped>
.console-log {
  height: 50px;
  background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
  color: #c9d1d9;
  border: 1px solid #30363d;
  border-radius: 6px;
  overflow-x: hidden;
  word-wrap: break-word;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.3;
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.console-log:hover {
  border-color: #58a6ff;
  box-shadow: 0 0 0 1px rgba(88, 166, 255, 0.3), inset 0 1px 0 rgba(255,255,255,0.05);
}

.console-log::before {
  content: '●';
  position: absolute;
  top: 4px;
  right: 6px;
  color: #7c3aed;
  font-size: 8px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.log-item {
  margin: 0;
  padding: 1px 6px;
  font-size: 10px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.log-item:first-child {
  padding-top: 3px;
}

.log-item:last-child {
  padding-bottom: 3px;
}

/* Enhanced success/error styling with terminal colors */
.success {
  color: #7ee787;
  font-weight: 500;
  text-shadow: 0 0 2px rgba(126, 231, 135, 0.3);
}

.error {
  color: #ff7b72;
  font-weight: 500;
  text-shadow: 0 0 2px rgba(255, 123, 114, 0.3);
}

/* Highlight numbers, percentages, and special characters */
.log-item span {
  background: linear-gradient(90deg, transparent 0%, rgba(88, 166, 255, 0.1) 50%, transparent 100%);
  padding: 0 2px;
  border-radius: 2px;
}

/* Modal console styling */
.console-log[style*="height: 500px"] {
  background: #0d1117;
  border: 1px solid #21262d;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
  padding: 12px;
}

.console-log[style*="height: 500px"] .log-item {
  padding: 2px 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-word;
  border-left: 2px solid transparent;
  padding-left: 8px;
  margin-bottom: 1px;
}

.console-log[style*="height: 500px"] .log-item:hover {
  background: rgba(88, 166, 255, 0.1);
  border-left-color: #58a6ff;
}

.console-log[style*="height: 500px"] .success {
  color: #7ee787;
  background: rgba(126, 231, 135, 0.05);
  border-left-color: #7ee787;
}

.console-log[style*="height: 500px"] .error {
  color: #ff7b72;
  background: rgba(255, 123, 114, 0.05);
  border-left-color: #ff7b72;
}

/* Additional terminal-like effects */
.console-log::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(88, 166, 255, 0.03) 50%,
    transparent 100%
  );
  pointer-events: none;
  animation: scan 3s linear infinite;
}

@keyframes scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Terminal cursor effect */
.console-log[style*="height: 500px"]::before {
  content: '▋';
  position: absolute;
  bottom: 12px;
  right: 12px;
  color: #7ee787;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>