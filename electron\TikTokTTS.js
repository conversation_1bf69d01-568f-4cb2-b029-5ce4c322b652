const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { execSync } = require('child_process');
const { randomInt } = require('crypto');
const { ffmpegManager } = require('./ffmpeg-config');
const { getCurrentDir } = require('./utils');
const pLimit = require('p-limit')

const ENDPOINT_DATA = [
  { url: 'https://tiktok-tts.weilnet.workers.dev/api/generation', response: 'data', failed: false },
  { url: 'https://tiktok-tts.printmechanicalbeltpumpkingutter.workers.dev/api/generation', response: 'audio', failed: false },
  //   { url: 'https://countik.com/api/text/speech', response: 'v_data' },
  { url: 'https://translate.volcengine.com/crx/tts/v1/', response: 'audio.data', type: 'volcengine', failed: false },
//   { url: 'https://gesserit.co/api/tiktok-tts', response: 'base64' },
  { url: 'https://api16-normal-v6.tiktokv.com/media/api/text/speech/invoke', response: 'data.v_str', type: 'tiktok', failed: false },
];

// https://api22-normal-c-alisg.tiktokv.com/media/api/text/speech/invoke/
// https://api.tiktokv.com/media/api/text/speech/invoke/

// Supported volcengine voices
const SUPPORTED_VOLCENGINE_VOICES = [
  'BV562_streaming',
  'BV075_streaming',
  'BV074_streaming'
];

class TikTokTTS {
  constructor(config) {
    this.config = config;
    this.maxChars = 300; // giới hạn ký tự mỗi chunk
    this.uriBase = 'https://api16-normal-c-useast1a.tiktokv.com/media/api/text/speech/invoke/';
    this.headers = {
      'User-Agent':
        'com.zhiliaoapp.musically/2022600030 (Linux; U; Android 7.1.2; es_ES; SM-G988N; Build/NRD90M;tt-ok/3.12.13.1)',
      Cookie: `sessionid=${config.tiktok_sessionid}`,
    };
    this.session = axios.create({ headers: this.headers });
    this.volcengineHeaders = {
      'Content-Type': 'application/json',
      Authority: 'translate.volcengine.com',
      Origin: 'chrome-extension://klgfhbdadaspgppeadghjjemk',
      Cookie: 'hasUserBehavior=1',
      'user-agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36',
    };
    this.volcengineSession = axios.create({ headers: this.volcengineHeaders });
  }
  detectLang(text) {
    // Regex nhận diện tiếng Trung (ký tự CJK Unified Ideographs)
    const chineseRegex = /[\u4E00-\u9FFF]/;

    // Regex nhận diện dấu tiếng Việt
    const vietnameseRegex = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;

    if (chineseRegex.test(text)) {
      return 'zh';
    } else if (vietnameseRegex.test(text)) {
      return 'vi';
    } else {
      return 'en';
    }
  }

  isVolcengineVoiceSupported(voice) {
    // Remove tts.other. prefix if present for comparison
    const cleanVoice = voice.replace('tts.other.', '');
    return SUPPORTED_VOLCENGINE_VOICES.includes(cleanVoice);
  }
  randomEndpoint() {
    const availableEndpoints = ENDPOINT_DATA.filter(endpoint => !endpoint.failed);
    if (availableEndpoints.length === 0) {
      // If all endpoints are marked as failed, reset them and try again
      this.resetFailedEndpoints();
      return ENDPOINT_DATA[randomInt(ENDPOINT_DATA.length)];
    }
    return availableEndpoints[randomInt(availableEndpoints.length)];
  }

  markEndpointAsFailed(url) {
    const endpoint = ENDPOINT_DATA.find(ep => ep.url === url);
    if (endpoint) {
      endpoint.failed = true;
      console.log(`Endpoint ${url} marked as failed and will be skipped in future attempts.`);
    }
  }

  resetFailedEndpoints() {
    ENDPOINT_DATA.forEach(endpoint => {
      endpoint.failed = false;
    });
    console.log('All failed endpoints have been reset.');
  }

  async run(text, filepath, voice, randomEndpoint = false) {
    if (!voice) throw new Error('No voice provided');

    const chunks = this._splitText(text);
    const maxRetries = 5;
    let currentRetry = 0;

    // Filter endpoints based on voice support and failed status
    let availableEndpoints = ENDPOINT_DATA.filter(endpoint => {
      // Skip failed endpoints
      if (endpoint.failed) {
        return false;
      }

      if (endpoint.type === 'volcengine') {
        return this.isVolcengineVoiceSupported(voice);
      }
      return true; // Allow all other endpoints
    });

    // Check if we have any available endpoints
    if (availableEndpoints.length === 0) {
      throw new Error(`No compatible endpoints found for voice: ${voice}. Volcengine only supports: ${SUPPORTED_VOLCENGINE_VOICES.join(', ')}`);
    }

    while (currentRetry < maxRetries) {
      // Re-filter endpoints to exclude newly failed ones
      const currentAvailableEndpoints = availableEndpoints.filter(endpoint => !endpoint.failed);

      if (currentAvailableEndpoints.length === 0) {
        console.log('All endpoints have been marked as failed. Resetting failed status for retry...');
        this.resetFailedEndpoints();
        // Re-filter after reset
        availableEndpoints = ENDPOINT_DATA.filter(endpoint => {
          if (endpoint.type === 'volcengine') {
            return this.isVolcengineVoiceSupported(voice);
          }
          return true;
        });
      }

      const endpointsToTry = randomEndpoint ?
        [currentAvailableEndpoints.length > 0 ? currentAvailableEndpoints[randomInt(currentAvailableEndpoints.length)] : availableEndpoints[randomInt(availableEndpoints.length)]] :
        (currentAvailableEndpoints.length > 0 ? currentAvailableEndpoints : availableEndpoints);

      for (const entry of endpointsToTry) {
        console.log(`Attempt ${currentRetry + 1}/${maxRetries} - Using endpoint:`, entry.url);

        const result = await this._tryEndpoint(entry, chunks, filepath, voice);

        if (result.success) {
          console.log(`File '${filepath}' has been generated successfully.`);
          return filepath;
        } else {
          console.log(`Endpoint ${entry.url} failed:`, result.error);
          // Mark this endpoint as failed so it won't be retried
          this.markEndpointAsFailed(entry.url);
        }
      }

      currentRetry++;
      if (currentRetry < maxRetries) {
        console.log(`All available endpoints failed on attempt ${currentRetry}. Retrying...`);
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error(`All endpoints failed after ${maxRetries} attempts. Unable to generate audio file.`);
  }

  async _tryEndpoint(entry, chunks, filepath, voice) {
    let orderedFiles = [];
    const limit = pLimit(5);
    try {
      const jobs = chunks.map((chunk, index) => {
        return limit(async () => {
          if (!chunk || /^[.,!?;:]+$/.test(chunk.trim())) {
            return null;
          }

          const safeName = this.slugify(chunk).slice(0, 24);
          const _filePath =path.join(getCurrentDir(), `${safeName}_${Date.now()}_temp.wav`);
          
          const audioBuffer = await this._processChunk(entry, chunk, voice);

          if (!audioBuffer) {
            throw new Error(`Failed to process chunk ${index + 1}`);
          }
          this.saveFile(_filePath, audioBuffer);
          return { index, filePath: _filePath };
        });
      });

      // Đợi tất cả kết quả
      const results = await Promise.all(jobs);

      // Lọc bỏ null và sắp xếp lại đúng thứ tự ban đầu
      orderedFiles = results
        .filter(Boolean)
        .sort((a, b) => a.index - b.index)
        .map((r) => r.filePath);
      // If we got here, all chunks were processed successfully
      if (orderedFiles.length === 0) {
        throw new Error('No audio chunks were generated');
      }

      // Concatenate files
      await this._concatenateFiles(orderedFiles, filepath);

      return { success: true };

    } catch (error) {
      // Clean up temporary files on error
      this._cleanupTempFiles(orderedFiles);
      return { success: false, error: error.message };
    }
  }

  async _processChunk(entry, chunk, voice) {
    try {
      const volcBody = {
        text: chunk,
        speaker: voice,
        language: this.detectLang(chunk),
      };
      console.log('volcBody',volcBody);
      
      let response;
      if (entry.type === 'tiktok' && this.config?.tiktok_sessionid) {
        response = await this.session.post(
          `${entry.url}/?text_speaker=${voice.replace('tts.other.', '')}&req_text=${chunk}&speaker_map_type=0&aid=1233`,
        );
      } else if (entry.type === 'volcengine') {
        // check if not tts.other. and Name BV...
        if (!voice.includes('tts.other.') && voice.includes('BV')) {
          volcBody.speaker = 'tts.other.' + voice;
        }
        response = await this.volcengineSession.post(entry.url, volcBody);
      } else {
        response = await axios.post(entry.url, {
          text: chunk,
          voice: voice.replace('tts.other.', ''),
        });
      }

      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle different response formats
      if (entry.type === 'tiktok' && this.config?.tiktok_sessionid) {
        const status_code = response.data.status_code;
        if (status_code !== 0) {
          this.handleStatusError(status_code);
          return null;
        }
        const audioBase64 = response.data.data.v_str;
        return Buffer.from(audioBase64, 'base64');
      } else if (entry.type === 'volcengine') {
        const base64Audio = response.data?.audio?.data;
        if (!base64Audio) {
          throw new Error('No audio data received from volcengine server');
        }
        return Buffer.from(base64Audio, 'base64');
      } else {
        const audioBase64 = response.data[entry.response];
        if (!audioBase64) {
          throw new Error(`No audio data found in response field: ${entry.response}`);
        }
        return Buffer.from(audioBase64, 'base64');
      }

    } catch (error) {
      throw new Error(`Chunk processing failed: ${error.message}`);
    }
  }

  async _concatenateFiles(tempFiles, filepath) {
    if (tempFiles.length === 1) {
      // If only one chunk, rename directly
      fs.renameSync(tempFiles[0], filepath);
    } else {
      // Create file list for ffmpeg concatenation
      const listFile = filepath + '_list.txt';
      const fileContent = tempFiles.map((f) => `file '${path.resolve(f)}'`).join('\n');
      fs.writeFileSync(listFile, fileContent);

      try {
        execSync(`${ffmpegManager.ffmpegPath} -y -f concat -safe 0 -i "${listFile}" -af "loudnorm=I=-16:LRA=11:TP=-1.5,volume=1.5" -ar 48000 -ac 2 -c:a pcm_s16le "${filepath}"`);
      } catch (e) {
        // Clean up and re-throw error
        this._cleanupTempFiles(tempFiles);
        fs.unlinkSync(listFile);
        throw new Error(`FFmpeg concatenation failed: ${e.message}`);
      }

      // Clean up temporary files and list file
      this._cleanupTempFiles(tempFiles);
      fs.unlinkSync(listFile);
    }
  }

  _cleanupTempFiles(tempFiles) {
    tempFiles.forEach((f) => {
      if (fs.existsSync(f)) {
        try {
          fs.unlinkSync(f);
        } catch (error) {
          console.warn(`Failed to delete temp file ${f}:`, error.message);
        }
      }
    });
  }
  handleStatusError(status_code) {
    switch (status_code) {
      case 1:
        throw new Error(
          `Your TikTok session id might be invalid or expired. Try getting a new one. status_code: ${status_code}`,
        );
      case 2:
        throw new Error(`The provided text is too long. status_code: ${status_code}`);
      case 4:
        throw new Error(`Invalid speaker, please check the list of valid speaker values. status_code: ${status_code}`);
      case 5:
        throw new Error(`No session id found. status_code: ${status_code}`);
    }
  }
  _splitText(text) {
    // Tách đoạn dựa trên dấu câu và khoảng trắng, giữ giới hạn 300 ký tự
    // remove "..." from text.
    text = text.replace(/["]+/g, '');
    text = text.replace(/\\n+/g, '');
    // text = text.replace(/[']+/g, '');
    const separatedChunks = [...text.matchAll(/.*?[.,!?:;\n]|.+/g)].map((m) => m[0]);
    const finalChunks = [];

    for (let chunk of separatedChunks) {
      if (chunk.length > this.maxChars) {
        // tách nhỏ theo khoảng trắng nếu quá dài
        const words = chunk.split(' ');
        let temp = '';
        for (const word of words) {
          if ((temp + word).length > this.maxChars) {
            if (temp) finalChunks.push(temp.trim());
            temp = word + ' ';
          } else {
            temp += word + ' ';
          }
        }
        if (temp) finalChunks.push(temp.trim());
      } else {
        finalChunks.push(chunk.trim());
      }
    }

    return finalChunks;
  }
  slugify(text) {
    return text
      .normalize('NFD') // Tách dấu khỏi ký tự
      .replace(/[\u0300-\u036f]/g, '') // Xóa các dấu
      .replace(/đ/g, 'd') // Chuyển đ -> d
      .replace(/Đ/g, 'd')
      .toLowerCase()
      .replace(/\s+/g, '-') // Thay khoảng trắng bằng -
      .replace(/[^\w\-]+/g, '') // Xóa ký tự không phải chữ/số/gạch ngang
      .replace(/\-\-+/g, '-') // Gộp nhiều dấu - liên tiếp
      .replace(/^-+|-+$/g, ''); // Xóa - ở đầu và cuối
  }
  saveFile(filepath, buffer) {
    fs.writeFileSync(filepath, buffer);
  }
}

module.exports = TikTokTTS;

// const tts = new TikTokTTS({ tiktok_sessionid: 'f99b95b348771c84efde83a0d457cc4d' });
// const filePath = path.join(__dirname,'..','_test-tts', 'cache', 'tts', 'output2.mp3');
// Example 1: Using the default behavior (try all endpoints in sequence)
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, false, true);

// Example 2: Using a random endpoint (set the 5th parameter to true)
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, 'BV074_streaming', true, true);
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, 'tts.other.BV074_streaming', true, true);
