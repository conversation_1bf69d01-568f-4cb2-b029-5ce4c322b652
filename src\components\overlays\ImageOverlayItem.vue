<template>
  <div class="overlay-item border border-gray-700 rounded-lg p-3 mb-3">
    <!-- Header with enable/disable and remove -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <a-checkbox v-model:checked="image.enabled">
          Enable Image {{ index + 1 }}
        </a-checkbox>
        <span v-if="image.file" class="text-xs text-gray-500">
          {{ getFileName(image.file) }}
        </span>
      </div>
      <div class="flex items-center gap-2">
        <a-button size="small" type="text" @click="duplicateImage" title="Duplicate">
          <template #icon>📋</template>
        </a-button>
        <a-button size="small" type="text" danger @click="removeImage" title="Remove">
          <template #icon>🗑️</template>
        </a-button>
      </div>
    </div>

    <div v-if="image.enabled">
      <!-- Image File Upload -->
      <a-form-item label="Image File" size="small">
        <a-upload 
          :file-list="[]"
          :before-upload="beforeImageUpload" 
          accept="image/*"
          :max-count="1"
          :show-upload-list="false"
        >
          <a-button size="small">
            <upload-outlined />
            Select Image
          </a-button>
        </a-upload>
      </a-form-item>

      <!-- Position Controls -->
      <a-row :gutter="8">
        <a-col :span="6">
          <a-form-item label="Left/Right" size="small">
            <a-slider 
              v-model:value="image.options.posX" 
              :min="0" 
              :max="100"
              :step="1" 
              size="small"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="Top/Bottom" size="small">
            <a-slider 
              v-model:value="image.options.posY" 
              :min="0" 
              :max="100"
              :step="1" 
              size="small"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="Scale" size="small">
            <a-slider 
              v-model:value="image.options.scale" 
              :min="10" 
              :max="1000" 
              size="small"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="Opacity" size="small">
            <a-slider 
              v-model:value="image.options.opacity" 
              :min="0" 
              :max="100" 
              size="small"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- Transform Controls -->
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input-group compact>
            <a-input-number v-model:value="image.options.timeStart" :min="0" :max="videoDuration" :step="0.1"
              @change="(e) => image.options.timeStart = e" />
            <a-button @click="() => image.options.timeStart = currentTime">
              Start
            </a-button>
          </a-input-group>
        </a-col>
        <a-col :span="6">
          <a-input-group compact>
            <a-input-number v-model:value="image.options.timeEnd" :min="0" :max="videoDuration" :step="0.1"
              @change="(e) => image.options.timeEnd = e" />
            <a-button @click="() => image.options.timeEnd = currentTime">
              End
            </a-button>
          </a-input-group>
        </a-col>
        <a-col :span="2">
          <a-button danger @click="() => (image.options.timeStart = 0, image.options.timeEnd = videoDuration)">
            Reset
          </a-button>
        </a-col>


      </a-row>
      <a-row :gutter="16">
        <a-col :span="5">
          <a-form-item label="Blend mode" size="small">
            <a-select v-model:value="image.options.blendMode">
              <a-select-option v-for="(item, index) in blendModes" :key="index" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Rotation" size="small">
            <a-input-number 
              v-model:value="image.options.rotation" 
              :min="-360" 
              :max="360" 
              size="small"
              addon-after="°" 
            />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Auto Fit Video" size="small">
            <a-checkbox 
              v-model:checked="image.options.autoFit" 
            />
          </a-form-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  image: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  currentTime:{
    type: Number,
    required: false
  },
  videoDuration: {
    type: Number,
    default: 0
  },
});

const emit = defineEmits(['remove', 'duplicate']);

const blendModes = [
  'normal',
  'overlay',
  'addition',
  'multiply',
  'difference',
  'screen'
]

// File upload handler
function beforeImageUpload(file) {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('Please select a valid image file!');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('Image file must be smaller than 10MB!');
    return false;
  }

  props.image.file = file.path;
  return false; // Prevent auto upload
}

// Helper functions
function getFilePreviewUrl(filePath) {
  if (!filePath) return '';
  return `file://${filePath}`;
}

function getFileName(filePath) {
  if (!filePath) return '';
  return filePath.split(/[\\/]/).pop();
}

function getPreviewStyle() {
  const options = props.image.options;
  return {
    opacity: (options.opacity || 100) / 100,
    position: 'absolute',
    left: `${options.posX || 50}%`,
    top: `${options.posY || 50}%`,
    transform: `translate(-50%, -50%) rotate(${options.rotation || 0}deg) scale(${(options.scale || 100) / 100})`
  };
}

function removeImage() {
  emit('remove', props.index);
}

function duplicateImage() {
  emit('duplicate', props.index);
}
</script>

<style scoped>

.overlay-preview {
  border-radius: 4px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
  border: 1px solid #d9d9d9;
}
</style>
