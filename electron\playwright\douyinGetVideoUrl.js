const { chromium } = require('playwright');

/**
 * Simple function to get Douyin video URL using the direct API approach
 * @param {string} input - The video ID or URL (including short URLs)
 * @returns {Promise<Object>} Video URL and info
 */
async function getDouyinVideoUrl(input) {
  const browser = await chromium.launch({
    headless: true,
  });

  try {
    const context = await browser.newContext({
      userAgent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    });

    const page = await context.newPage();

    // Navigate to the input URL (could be short URL or full URL)
    console.log(`Navigating to: ${input}`);

    // Load page and stop as soon as DOM is ready
    await page.goto(input, { waitUntil: 'domcontentloaded' });

    // Stop any further loading immediately using modern approach
    await page.evaluate(`(() => {
      // Stop window loading
      if (window.stop) {
        window.stop();
      }
      // Also try to stop via navigation
      try {
        window.history.pushState(null, '', window.location.href);
      } catch (e) {
        // Ignore errors
      }
    })()`);

    console.log('DOM loaded, stopped loading, extracting video ID...');

    // Execute the extraction and API call in browser context
    const result = await page.evaluate(`
      (async () => {
        // Extract video ID from current URL after any redirects
        var match = location.href.match(/\\/video\\/(\\d+)/);
        var aweme_id = match ? match[1] : null;

        if (!aweme_id) {
          return {
            success: false,
            error: 'Could not extract video ID from URL: ' + location.href,
            currentUrl: location.href
          };
        }

        console.log('Extracted video ID:', aweme_id);

        try {
          var detail = await fetch('https://www.douyin.com/aweme/v1/web/aweme/detail?aid=6383&version_code=190500&aweme_id=' + aweme_id, {
            method: 'GET',
            headers: {
              'Referer': 'https://www.douyin.com',
              'User-Agent': navigator.userAgent
            },
          }).then((data) => data.json());

          var videoUrl = detail?.aweme_detail?.video?.play_addr?.url_list[0];
          var fileName = detail?.aweme_detail?.desc || 'douyin_video.mp4';
          var author = detail?.aweme_detail?.author?.nickname;
          var duration = detail?.aweme_detail?.duration;
          var cover = detail?.aweme_detail?.video?.cover?.url_list?.[0];

          return {
            success: true,
            videoUrl: videoUrl,
            fileName: fileName,
            author: author,
            duration: duration,
            cover: cover,
            aweme_id: aweme_id,
            currentUrl: location.href,
            rawDetail: detail?.aweme_detail
          };

        } catch (e) {
          return {
            success: false,
            error: e.message,
            aweme_id: aweme_id,
            currentUrl: location.href
          };
        }
      })();
    `);

    return result;
  } catch (error) {
    console.error('Error:', error);
    return {
      success: false,
      error: error.message,
      currentUrl: input,
    };
  } finally {
    await browser.close();
  }
}

/**
 * Extract video ID from Douyin URL
 * @param {string} url - Douyin video URL
 * @returns {string|null} Video ID
 */
function extractVideoId(url) {
  const match = url.match(/\/video\/(\d+)/);
  return match ? match[1] : null;
}

// Export functions
module.exports = {
  getDouyinVideoUrl,
  extractVideoId,
};

// If run directly
if (require.main === module) {
  (async () => {
    const input = process.argv[2];

    if (!input) {
      console.log('Usage:');
      console.log('  node douyinGetVideoUrl.js <video_id>');
      console.log('  node douyinGetVideoUrl.js <video_url>');
      console.log('');
      console.log('Examples:');
      console.log('  node douyinGetVideoUrl.js 7509343869454306618');
      console.log('  node douyinGetVideoUrl.js https://www.douyin.com/video/7509343869454306618');
      return;
    }

    console.log(`Processing input: ${input}`);

    const result = await getDouyinVideoUrl(input);

    if (result.success) {
      console.log('\n✅ SUCCESS!');
      console.log('📹 Video URL:', result.videoUrl);
      console.log('📝 Title:', result.fileName);
      console.log('👤 Author:', result.author);
      console.log('⏱️  Duration:', result.duration ? `${result.duration}s` : 'Unknown');
      console.log('🖼️  Cover:', result.cover);

      // Save to file for easy access
      const fs = require('fs');
      const path = require('path');
      const outputPath = path.join(__dirname, 'video_url_result.json');

      fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf8');
      console.log('💾 Result saved to:', outputPath);
    } else {
      console.log('\n❌ FAILED!');
      console.log('Error:', result.error);
    }
  })();
}
