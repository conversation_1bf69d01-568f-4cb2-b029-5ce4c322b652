<template>
  <div class="timeline-control">
    <div class="timeline-control-background">
      <div class="timeline-control-bar" @pointerdown="barPointerDownEvent"
        :style="{ left: `${leftPercent * 100}%`, width: `${(rightPercent - leftPercent) * 100}%` }"></div>
      <div class="timeline-control-handle timeline-control-handle-left" @pointerdown="leftHandlePointerDownEvent"
        :style="{ left: `${leftPercent * 100}%` }"></div>
      <div class="timeline-control-handle timeline-control-handle-right" @pointerdown="rightHandlePointerDownEvent"
        :style="{ left: `${rightPercent * 100}%` }"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeUnmount } from 'vue'

const props = defineProps<{
  leftPercent: number
  rightPercent: number
  totalFrame: number
}>()

const emit = defineEmits<{
  (e: 'update:leftPercent', val: number): void
  (e: 'update:rightPercent', val: number): void
  (e: 'move', val: number): void
}>()

let pointerId: number | undefined
let pointerDragging: 'left' | 'right' | 'bar' = 'bar'
let lastX = 0

const barPointerDownEvent = (event: PointerEvent) => {
  if (pointerId === undefined) {
    event.preventDefault()
    pointerId = event.pointerId
    pointerDragging = 'bar'
    lastX = event.pageX
  }
}

const leftHandlePointerDownEvent = (event: PointerEvent) => {
  if (pointerId === undefined) {
    event.preventDefault()
    pointerId = event.pointerId
    pointerDragging = 'left'
  }
}

const rightHandlePointerDownEvent = (event: PointerEvent) => {
  if (pointerId === undefined) {
    event.preventDefault()
    pointerId = event.pointerId
    pointerDragging = 'right'
  }
}

const pointerMoveEvent = (event: PointerEvent) => {
  if (pointerId === event.pointerId) {
    event.preventDefault()
    const timeline = document.querySelector('.timeline-control')
    if (!timeline) return

    const timelinePos = timeline.getBoundingClientRect()
    const percent = (event.pageX - timelinePos.left) / timelinePos.width

    if (pointerDragging === 'bar') {
      emit('move', (event.pageX - lastX) / timelinePos.width * props.totalFrame)
      lastX = event.pageX
    } else if (pointerDragging === 'left') {
      emit('update:leftPercent', percent)
    } else if (pointerDragging === 'right') {
      emit('update:rightPercent', percent)
    }
  }
}

const pointerUpEvent = (event: PointerEvent) => {
  if (pointerId === event.pointerId) {
    event.preventDefault()
    pointerId = undefined
    pointerDragging = 'bar'
  }
}

onMounted(() => {
  document.addEventListener('pointermove', pointerMoveEvent)
  document.addEventListener('pointerup', pointerUpEvent)
})

onBeforeUnmount(() => {
  document.removeEventListener('pointermove', pointerMoveEvent)
  document.removeEventListener('pointerup', pointerUpEvent)
})
</script>

<style scoped>
.timeline-bar {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: rgba(0, 153, 255, 0.5);
  border: 1px solid #0099ff;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
}

.left-handle,
.right-handle {
  width: 6px;
  background-color: #007acc;
  cursor: ew-resize;
}

.left-handle {
  border-right: 1px solid white;
}

.right-handle {
  border-left: 1px solid white;
}
</style>

<style scoped>
.timeline-control {
  height: 28px;
  display: flex;
  position: relative;
}

.timeline-control-background {
  position: absolute;
  left: 8px;
  right: 8px;
  top: 8px;
  height: 8px;
  background-color: rgba(9, 22, 32, 0.6);
  border-radius: 10px;
}

.timeline-control-handle {
  position: absolute;
  top: -2px;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  border: 2px solid rgba(28, 66, 95, 1);
  background-color: #122c3f;
  cursor: pointer;
  transform: translateX(-50%);
}

.timeline-control-bar {
  background-color: #122c3f;
  position: absolute;
  height: 8px;
  width: 80px;
  left: 20px;
}
</style>
