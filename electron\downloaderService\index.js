const { fetchDouyinVideoInfo } = require('./douyinService');
const { fetchTikTokData, extractTikTokId } = require('./tiktokService');
const { fetchYouTubeData } = require('./youtubeService');
const facebookInstaService = require('./facebookInstaService.js');
// const { fetchLinkedInData } = require('./linkedinService');
// const { fetchPinterestData } = require('./pinterestService');
// const { fetchRedditData } = require('./redditService');
// const { fetchThreadsData } = require('./threadsService');
// const { fetchTwitterData } = require('./twitterService');

/**
 * Detect platform from URL
 * @param {string} url
 * @returns {string} platform name
 */
function detectPlatform(url) {
  const urlLower = url.toLowerCase();

  if (urlLower.includes('douyin.com') || urlLower.includes('iesdouyin.com')) {
    return 'douyin';
  }
  if (urlLower.includes('tiktok.com')) {
    return 'tiktok';
  }
  if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
    return 'youtube';
  }
  if (urlLower.includes('facebook.com') || urlLower.includes('instagram.com')) {
    return 'facebook_insta';
  }
  if (urlLower.includes('linkedin.com')) {
    return 'linkedin';
  }
  if (urlLower.includes('pinterest.com')) {
    return 'pinterest';
  }
  if (urlLower.includes('reddit.com')) {
    return 'reddit';
  }
  if (urlLower.includes('threads.net')) {
    return 'threads';
  }
  if (urlLower.includes('twitter.com') || urlLower.includes('x.com')) {
    return 'twitter';
  }

  return 'unknown';
}

/**
 * Get video info using appropriate service
 * @param {string} url
 * @returns {Promise<Object>}
 */
async function getVideoInfoWithService(url) {
  const platform = detectPlatform(url);

  try {
    let rawData;

    switch (platform) {
      case 'douyin':
        rawData = await fetchDouyinVideoInfo(url);
        return normalizeDouyinData(rawData);

      case 'tiktok':
        const tikTokId = extractTikTokId(url);
        if (!tikTokId) throw new Error('Invalid TikTok URL');
        rawData = await fetchTikTokData(tikTokId);
        return normalizeTikTokData(rawData);

      case 'youtube':
        rawData = await fetchYouTubeData(url);
        return normalizeYouTubeData(rawData);

      case 'facebook_insta':
        rawData = await facebookInstaService(url);
        return normalizeFacebookInstaData(rawData);

      case 'linkedin':
        return await fetchLinkedInData(url);

      case 'pinterest':
        return await fetchPinterestData(url);

      case 'reddit':
        return await fetchRedditData(url);

      case 'threads':
        return await fetchThreadsData(url);

      case 'twitter':
        return await fetchTwitterData(url);

      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  } catch (error) {
    throw new Error(`${platform} service failed: ${error.message}`);
  }
}

// Normalize data from different services to common format
function normalizeDouyinData(data) {
  return {
    title: data.title || 'Douyin Video',
    thumbnail: data.thumbnail,
    duration: null,
    uploader: 'Douyin User',
    formats:
      data.videoLinks?.map((link) => ({
        quality: link.label || 'Default',
        extension: 'mp4',
        url: link.url,
        type: 'video',
      })) || [],
  };
}

function normalizeTikTokData(data) {
  return {
    title: data.title || 'TikTok Video',
    thumbnail: data.thumbnail,
    duration: data.duration,
    uploader: data.author || 'TikTok User',
    formats:
      data.formats?.map((format) => ({
        quality: format.quality || 'Default',
        extension: format.extension || 'mp4',
        url: format.url,
        type: format.type || 'video',
      })) || [],
  };
}

function normalizeYouTubeData(data) {
  return {
    title: data.title,
    thumbnail: data.thumbnail,
    duration: data.duration,
    uploader: data.uploader || 'YouTube User',
    formats:
      data.formats?.map((format) => ({
        quality: format.quality,
        extension: format.extension,
        url: format.url,
        type: format.type,
      })) || [],
  };
}

function normalizeFacebookInstaData(data) {
  if (!data.status) {
    throw new Error(data.msg || 'Facebook/Instagram service failed');
  }

  return {
    title: data.title || 'Facebook/Instagram Video',
    thumbnail: data.thumbnail,
    duration: null,
    uploader: 'Facebook/Instagram User',
    formats:
      data.medias?.map((media) => ({
        quality: media.quality || 'Default',
        extension: media.extension || 'mp4',
        url: media.url,
        type: 'video',
      })) || [],
  };
}

/**
 * Download video using appropriate service
 * @param {string} url
 * @param {string} outputPath
 * @param {string} format
 * @returns {Promise<Object>}
 */
async function downloadVideoWithService(url, outputPath, format) {
  const platform = detectPlatform(url);

  // Get video info first
  const videoInfo = await getVideoInfoWithService(url);

  if (!videoInfo || !videoInfo.formats || videoInfo.formats.length === 0) {
    throw new Error('No download links found');
  }

  // Select format
  let selectedFormat = videoInfo.formats[0]; // Default to first format

  if (format && videoInfo.formats.find((f) => f.quality === format || f.type === format)) {
    selectedFormat = videoInfo.formats.find((f) => f.quality === format || f.type === format);
  }

  if (!selectedFormat.url) {
    throw new Error('No download URL found for selected format');
  }

  // Download the file
  const axios = require('axios');
  const fs = require('fs');
  const path = require('path');

  const fileName = `${videoInfo.title || 'video'}.${selectedFormat.extension || 'mp4'}`;
  const sanitizedFileName = fileName.replace(/[<>:"/\\|?*]/g, '_');
  const fullPath = path.join(outputPath, sanitizedFileName);

  const response = await axios({
    method: 'GET',
    url: selectedFormat.url,
    responseType: 'stream',
  });

  const writer = fs.createWriteStream(fullPath);
  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on('finish', () => {
      resolve({
        success: true,
        filePath: fullPath,
        platform: platform,
      });
    });
    writer.on('error', reject);
  });
}

module.exports = {
  detectPlatform,
  getVideoInfoWithService,
  downloadVideoWithService,
};
