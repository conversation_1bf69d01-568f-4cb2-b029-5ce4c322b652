# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
dist_electron
jsc
electron-build
cache
_xxl_data
venv
__pycache__
types
pnpm-workspace.yaml
chrome-data
.env
subtitles.ass
/static/witts.exe
ffmpeg
story_summary.txt
translated.txt
input.txt
output
.cache
witts.exe
pytool.exe
PaddleOCR-json/
