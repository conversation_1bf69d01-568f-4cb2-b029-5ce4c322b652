import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'

// https://vite.dev/config/
export default defineConfig(({mode}) => {
  const isDev = false //mode === 'development'
  console.log('Mode:', mode,isDev); // true
  return {
  base: './', // Sử dụng đường dẫn tương đối
  plugins: [
    vue(),
    tailwindcss(),
    AutoImport({
      imports: [
        'vue',
        { '@/globals/index': [['F', 'F'], ['S', 'S']] },
      ],
      dts: 'src/types/auto-imports.d.ts',
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({importStyle: false}),
      ],
      // components
      dirs: ['src/components'],
      extensions: ['vue'],
      dts: 'src/types/components.d.ts',
      include: [/\.vue$/, /\.vue\?vue/, /\.vue\.[tj]sx?\?vue/],
    }),

  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    assetsDir: 'assets', // Thư mục assets
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      output: {
        manualChunks:{
          main: ['src/main.js']
        },
        assetFileNames: 'assets/[name]-[hash][extname]',
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        // manualChunks: (id) => {
        //   // Node modules chunking
        //   if (id.includes('node_modules')) {
        //     return 'vendor';
        //   }
        //   // Default chunk
        //   return 'main';
        // },
      }
    }
  },
  server: {
    port: 5173,
    strictPort: true,
    watch: {
      // Cho phép theo dõi thư mục cụ thể
      ignored: ['**/*', '!**/src/**']
      // ignored: ['**/*', '!**/electron/**','!**/src/**']
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      }
    }
  }
}
})
