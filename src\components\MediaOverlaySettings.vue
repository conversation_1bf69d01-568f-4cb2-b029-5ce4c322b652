<template>
  <a-form-item label="Media Overlay Settings" class="border border-gray-600 p-2 rounded-lg">
    <a-tabs v-model:activeKey="activeOverlayTab" size="small">
      <!-- Logo Tab -->
      <a-tab-pane key="logo" :tab="`Logos (${overlays.logos.length})`">
        <div class="mb-3">
          <a-button type="primary" size="small" @click="addLogo" :icon="h(PlusOutlined)">
            Add Logo
          </a-button>
          <span v-if="overlays.logos.length === 0" class="ml-2 text-gray-500 text-sm">
            No logos added yet
          </span>
        </div>

        <div v-if="overlays.logos.length > 0" class="space-y-2 max-h-[30rem] overflow-auto">
          <LogoOverlayItem
            v-for="(logo, index) in overlays.logos"
            :key="logo.id"
            :logo="logo"
            :index="index"
            @remove="removeLogo"
            @duplicate="duplicateLogo"
            :current-time="currentTime"
            :video-duration="videoDuration" 
          />
        </div>
          
      </a-tab-pane>

      <!-- Image Tab -->
      <a-tab-pane key="image" :tab="`Images (${overlays.images.length})`">
        <div class="mb-3">
          <a-button type="primary" size="small" @click="addImage" :icon="h(PlusOutlined)">
            Add Image
          </a-button>
          <span v-if="overlays.images.length === 0" class="ml-2 text-gray-500 text-sm">
            No images added yet
          </span>
        </div>

        <div v-if="overlays.images.length > 0" class="space-y-2 max-h-[30rem] overflow-auto">
          <ImageOverlayItem
            v-for="(image, index) in overlays.images"
            :key="image.id"
            :image="image"
            :index="index"
            @remove="removeImage"
            @duplicate="duplicateImage"
            :current-time="currentTime"
            :video-duration="videoDuration" 
          />
        </div>
      </a-tab-pane>

      <!-- Fixed Text Tab -->
      <a-tab-pane key="fixedText" :tab="`Texts (${overlays.texts.length})`">
        <div class="mb-3">
          <a-button type="primary" size="small" @click="addText" :icon="h(PlusOutlined)">
            Add Text
          </a-button>
          <span v-if="overlays.texts.length === 0" class="ml-2 text-gray-500 text-sm">
            No texts added yet
          </span>
        </div>

        <div v-if="overlays.texts.length > 0" class="space-y-2 max-h-[30rem] overflow-auto">
          <TextOverlayItem
            v-for="(text, index) in overlays.texts"
            :key="text.id"
            :text="text"
            :index="index"
            @remove="removeText"
            @duplicate="duplicateText"
            :current-time="currentTime"
            :video-duration="videoDuration" 
          />
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-form-item>
</template>

<script setup>
import { ref, computed, onMounted, h } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import fontService from '@/services/fontService';
import LogoOverlayItem from './overlays/LogoOverlayItem.vue';
import ImageOverlayItem from './overlays/ImageOverlayItem.vue';
import TextOverlayItem from './overlays/TextOverlayItem.vue';

const props = defineProps({
  videoDuration: {
    type: Number,
    default: 0
  },
  currentTime: {
    type: Number,
    default: 0
  }
})

const subtitleStore = useSubtitleStore();
const renderOptions = subtitleStore.renderOptions;


// Active tab
const activeOverlayTab = ref('logo');

// Initialize overlays structure and migrate old data
subtitleStore.initializeOverlays();

// Reactive overlays reference
const overlays = computed(() => renderOptions.overlays);

// Generate unique ID for overlays
function generateId() {
  return 'overlay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Logo management functions
function addLogo() {
  const newLogo = {
    id: generateId(),
    enabled: true,
    file: '',
    options: {
      timeStart: 0,
      timeEnd: props.videoDuration,
      posX: 90,
      posY: 10,
      scale: 100,
      rotation: 0,
      opacity: 100
    }
  };
  overlays.value.logos.push(newLogo);
  message.success('Logo added successfully!');
}

function removeLogo(index) {
  overlays.value.logos.splice(index, 1);
  message.success('Logo removed successfully!');
}

function duplicateLogo(index) {
  const originalLogo = overlays.value.logos[index];
  const duplicatedLogo = {
    ...originalLogo,
    id: generateId(),
    options: { ...originalLogo.options }
  };
  overlays.value.logos.splice(index + 1, 0, duplicatedLogo);
  message.success('Logo duplicated successfully!');
}

// Image management functions
function addImage() {
  const newImage = {
    id: generateId(),
    enabled: true,
    file: '',
    options: {
      timeStart: 0,
      timeEnd: props.videoDuration,
      posX: 50,
      posY: 50,
      scale: 100,
      rotation: 0,
      opacity: 100,
      autoFit: false,
      blendMode: 'normal'
    }
  };
  overlays.value.images.push(newImage);
  message.success('Image added successfully!');
}

function removeImage(index) {
  overlays.value.images.splice(index, 1);
  message.success('Image removed successfully!');
}

function duplicateImage(index) {
  const originalImage = overlays.value.images[index];
  const duplicatedImage = {
    ...originalImage,
    id: generateId(),
    options: { ...originalImage.options }
  };
  overlays.value.images.splice(index + 1, 0, duplicatedImage);
  message.success('Image duplicated successfully!');
}

// Text management functions
function addText() {
  const newText = {
    id: generateId(),
    enabled: true,
    options: {
      text: 'Sample Text',
      timeStart: 0,
      timeEnd: props.videoDuration,
      fontSize: 24,
      color: '#ffffff',
      backgroundColor: 'transparent',
      borderColor: '#000000',
      fontFamily: 'Arial',
      bold: false,
      posX: 50,
      posY: 10,
      rotation: 0,
      opacity: 100,
      align: 'center',
      paddingX: 0.6, // Default padding width (0.6x font size)
      paddingY: 0.6  // Default padding height (0.6x font size)
    }
  };
  overlays.value.texts.push(newText);
  message.success('Text added successfully!');
}

function removeText(index) {
  overlays.value.texts.splice(index, 1);
  message.success('Text removed successfully!');
}

function duplicateText(index) {
  const originalText = overlays.value.texts[index];
  const duplicatedText = {
    ...originalText,
    id: generateId(),
    options: { ...originalText.options }
  };
  overlays.value.texts.splice(index + 1, 0, duplicatedText);
  message.success('Text duplicated successfully!');
}



function getFileName(filePath) {
  if (!filePath) return '';
  return filePath.split(/[\\/]/).pop();
}
// Load fonts on component mount
onMounted(async () => {
  await fontService.loadFonts();

});
</script>

<style scoped>
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.overlay-preview {
  border-radius: 4px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
  border: 1px solid #d9d9d9;
}

.fixed-text-preview-content {
  margin: 0;
  max-width: 100%;
  word-wrap: break-word;
}
</style>
