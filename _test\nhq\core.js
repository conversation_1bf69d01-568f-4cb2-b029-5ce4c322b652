function set_cookie(name, value, days) {
    let expires = "";
    if (days) {
        let date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = "; expires=" + date.toUTCString();
    }
    
    const secure = '; secure';
    const sameSite = '; samesite=none';
    
    document.cookie = name + "=" + encodeURIComponent(value) + 
                     expires + 
                     "; path=/" + 
                     secure +
                     sameSite;
}
function check_long_text(text) {
    info = new Object();
    info.status = true;
    info.errors = [];
    
    text = text.replaceAll('、',',');
    text = text.replaceAll('，',',');
    text = text.replaceAll('。','.');
    text = text.replaceAll('！','!');
    text = text.replaceAll('？','?');
    text = text.replaceAll('：',':');
    text = text.replaceAll('；',';');
    text = text.replaceAll('：',':');
    text = text.replaceAll('；',';');
    const sentences = text.split(/[.,!;]/).map(sentence => sentence.trim()).filter(sentence => sentence.length > 0);
    
    // Kiểm tra từng câu
    sentences.forEach((sentence, index) => {
        if (sentence.length > 1000) {
            info.status = false;
            info.errors.push(`Câu ${index + 1} dài ${sentence.length} ký tự, vượt quá giới hạn 1000 ký tự: "${sentence.substring(0, 50)}..."`)
        }
    });
    
    // Nếu không có câu nào vượt quá 1000 ký tự
    if (!sentences.some(sentence => sentence.length > 1000)) {
        info.status = true
        return info
    }

    return info
}
function srt_to_text(srtContent) {
  // Split content into lines and trim
  const lines = srtContent.trim().split('\n');
  let result = '';
  let isTextLine = false;

  for (const line of lines) {
    // Skip empty lines
    if (line.trim() === '') {
      isTextLine = false;
      continue;
    }
    // Skip number lines (subtitle index)
    if (/^\d+$/.test(line.trim())) {
      continue;
    }
    // Skip timestamp lines
    if (line.includes('-->')) {
      isTextLine = true;
      continue;
    }
    // Process text lines
    if (isTextLine) {
      result += line.trim() + '\n\n';
    }
  }

  // Remove extra spaces and return
  return result.trim();
}
function uuidv4() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

function convertAnsiToHtml(ansiText, theme='dark') {
    // Định nghĩa các mã màu ANSI và mã HTML tương ứng
    if (theme == 'dark') {
        yellow = '<span style="color: yellow">';
    } else {
        yellow = '<span style="color: orange">';
    }
    const ansiColors = {
        '\u001b[30m': '<span style="color: black">',
        '\u001b[31m': '<span style="color: red">',
        '\u001b[32m': '<span style="color: green">',
        '\u001b[33m': yellow,
        '\u001b[34m': '<span style="color: blue">',
        '\u001b[35m': '<span style="color: magenta">',
        '\u001b[36m': '<span style="color: cyan">',
        '\u001b[37m': '<span style="color: white">',
        '\u001b[0m': '</span>',  // reset
        '\u001b[1m': '<span style="font-weight: bold">', // bold
    };

    let html = ansiText;
    
    // Thay thế tất cả các mã ANSI bằng HTML
    for (const [ansi, htmlCode] of Object.entries(ansiColors)) {
        // Chuyển đổi \033 thành \u001b
        const ansiVariant = ansi.replace('\u001b', '\x1B');
        const ansiVariant2 = ansi.replace('\u001b', String.fromCharCode(27));
        
        // Thay thế cả 3 dạng có thể có của mã ANSI
        html = html.replaceAll(ansi, htmlCode);
        html = html.replaceAll(ansiVariant, htmlCode);
        html = html.replaceAll(ansiVariant2, htmlCode);
    }

    return html;
}

function get_cookie(name) {
    let nameEQ = name + "=";
    let ca = document.cookie.split(';');
    for(let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    return null;
}


function delete_cookie(name) {
    document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/';
}
function size_format(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function time_format(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${Math.round(remainingSeconds).toString().padStart(2, '0')}`;
}

async function sha256(file) {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
}

async function download_file(session) {
    const formData = new FormData();
    formData.append('action', 'download_file');
    formData.append('session', session);
    
    try {
        const resp = await fetch('/ajax', {
            method: 'POST',
            body: formData,
        });
        
        const data = await resp.json();
        if (data.status && data.url) {
            window.location.href = data.url;
        } else {
            alert(data.error || 'Can not download file');
        }
    } catch (error) {
        console.error('Error when download file:', error);
        alert('Can not download file');
    }
}

function md5(input) {
    function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32((a << s) | (a >>> (32 - s)), b);
    }

    function ff(a, b, c, d, x, s, t) {
        return cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }

    function gg(a, b, c, d, x, s, t) {
        return cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }

    function hh(a, b, c, d, x, s, t) {
        return cmn(b ^ c ^ d, a, b, x, s, t);
    }

    function ii(a, b, c, d, x, s, t) {
        return cmn(c ^ (b | (~d)), a, b, x, s, t);
    }

    function md5cycle(x, k) {
        let a = x[0], b = x[1], c = x[2], d = x[3];

        a = ff(a, b, c, d, k[0], 7, -680876936);
        d = ff(d, a, b, c, k[1], 12, -389564586);
        c = ff(c, d, a, b, k[2], 17, 606105819);
        b = ff(b, c, d, a, k[3], 22, -1044525330);
        a = ff(a, b, c, d, k[4], 7, -176418897);
        d = ff(d, a, b, c, k[5], 12, 1200080426);
        c = ff(c, d, a, b, k[6], 17, -1473231341);
        b = ff(b, c, d, a, k[7], 22, -45705983);
        a = ff(a, b, c, d, k[8], 7, 1770035416);
        d = ff(d, a, b, c, k[9], 12, -1958414417);
        c = ff(c, d, a, b, k[10], 17, -42063);
        b = ff(b, c, d, a, k[11], 22, -1990404162);
        a = ff(a, b, c, d, k[12], 7, 1804603682);
        d = ff(d, a, b, c, k[13], 12, -40341101);
        c = ff(c, d, a, b, k[14], 17, -1502002290);
        b = ff(b, c, d, a, k[15], 22, 1236535329);

        a = gg(a, b, c, d, k[1], 5, -165796510);
        d = gg(d, a, b, c, k[6], 9, -1069501632);
        c = gg(c, d, a, b, k[11], 14, 643717713);
        b = gg(b, c, d, a, k[0], 20, -373897302);
        a = gg(a, b, c, d, k[5], 5, -701558691);
        d = gg(d, a, b, c, k[10], 9, 38016083);
        c = gg(c, d, a, b, k[15], 14, -660478335);
        b = gg(b, c, d, a, k[4], 20, -405537848);
        a = gg(a, b, c, d, k[9], 5, 568446438);
        d = gg(d, a, b, c, k[14], 9, -1019803690);
        c = gg(c, d, a, b, k[3], 14, -187363961);
        b = gg(b, c, d, a, k[8], 20, 1163531501);
        a = gg(a, b, c, d, k[13], 5, -1444681467);
        d = gg(d, a, b, c, k[2], 9, -51403784);
        c = gg(c, d, a, b, k[7], 14, 1735328473);
        b = gg(b, c, d, a, k[12], 20, -1926607734);

        a = hh(a, b, c, d, k[5], 4, -378558);
        d = hh(d, a, b, c, k[8], 11, -2022574463);
        c = hh(c, d, a, b, k[11], 16, 1839030562);
        b = hh(b, c, d, a, k[14], 23, -35309556);
        a = hh(a, b, c, d, k[1], 4, -1530992060);
        d = hh(d, a, b, c, k[4], 11, 1272893353);
        c = hh(c, d, a, b, k[7], 16, -155497632);
        b = hh(b, c, d, a, k[10], 23, -1094730640);
        a = hh(a, b, c, d, k[13], 4, 681279174);
        d = hh(d, a, b, c, k[0], 11, -358537222);
        c = hh(c, d, a, b, k[3], 16, -722521979);
        b = hh(b, c, d, a, k[6], 23, 76029189);
        a = hh(a, b, c, d, k[9], 4, -640364487);
        d = hh(d, a, b, c, k[12], 11, -421815835);
        c = hh(c, d, a, b, k[15], 16, 530742520);
        b = hh(b, c, d, a, k[2], 23, -995338651);

        a = ii(a, b, c, d, k[0], 6, -198630844);
        d = ii(d, a, b, c, k[7], 10, 1126891415);
        c = ii(c, d, a, b, k[14], 15, -1416354905);
        b = ii(b, c, d, a, k[5], 21, -57434055);
        a = ii(a, b, c, d, k[12], 6, 1700485571);
        d = ii(d, a, b, c, k[3], 10, -1894986606);
        c = ii(c, d, a, b, k[10], 15, -1051523);
        b = ii(b, c, d, a, k[1], 21, -2054922799);
        a = ii(a, b, c, d, k[8], 6, 1873313359);
        d = ii(d, a, b, c, k[15], 10, -30611744);
        c = ii(c, d, a, b, k[6], 15, -1560198380);
        b = ii(b, c, d, a, k[13], 21, 1309151649);
        a = ii(a, b, c, d, k[4], 6, -145523070);
        d = ii(d, a, b, c, k[11], 10, -1120210379);
        c = ii(c, d, a, b, k[2], 15, 718787259);
        b = ii(b, c, d, a, k[9], 21, -343485551);

        x[0] = add32(a, x[0]);
        x[1] = add32(b, x[1]);
        x[2] = add32(c, x[2]);
        x[3] = add32(d, x[3]);
    }

    function add32(a, b) {
        return (a + b) & 0xFFFFFFFF;
    }

    function str2blks_MD5(str) {
        const nblk = ((str.length + 8) >> 6) + 1;
        const blks = new Array(nblk * 16);
        
        for (let i = 0; i < nblk * 16; i++) {
            blks[i] = 0;
        }
        
        for (let i = 0; i < str.length; i++) {
            blks[i >> 2] |= str.charCodeAt(i) << ((i % 4) * 8);
        }
        
        blks[str.length >> 2] |= 0x80 << ((str.length % 4) * 8);
        blks[nblk * 16 - 2] = str.length * 8;
        
        return blks;
    }

    const x = [1732584193, -271733879, -1732584194, 271733878];
    const blks = str2blks_MD5(input);
    
    for (let i = 0; i < blks.length; i += 16) {
        const block = blks.slice(i, i + 16);
        md5cycle(x, block);
    }

    return x.map(num => {
        const str = (num >>> 0).toString(16);
        return '00000000'.slice(str.length) + str;
    }).join('');
}

function slug(text) {
    // Bảng chuyển đổi ký tự có dấu thành không dấu
    const map = {
        'a': 'àáạảãâầấậẩẫăằắặẳẵ',
        'e': 'èéẹẻẽêềếệểễ',
        'i': 'ìíịỉĩ',
        'o': 'òóọỏõôồốộổỗơờớợởỡ',
        'u': 'ùúụủũưừứựửữ',
        'y': 'ỳýỵỷỹ',
        'd': 'đ',
        'A': 'ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ',
        'E': 'ÈÉẸẺẼÊỀẾỆỂỄ',
        'I': 'ÌÍỊỈĨ',
        'O': 'ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ',
        'U': 'ÙÚỤỦŨƯỪỨỰỬỮ',
        'Y': 'ỲÝỴỶỸ',
        'D': 'Đ'
    };

    // Chuyển đổi chuỗi
    let slug = text;
    
    // Thay thế từng ký tự có dấu
    for (let key in map) {
        const regex = new RegExp(`[${map[key]}]`, 'g');
        slug = slug.replace(regex, key);
    }

    // Chuyển thành chữ thường, thay khoảng trắng bằng dấu gạch ngang
    slug = slug
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-') // Thay các ký tự không phải chữ và số thành '-'
        .replace(/-+/g, '-') // Xóa các dấu '-' liên tiếp
        .replace(/^-|-$/g, ''); // Xóa dấu '-' ở đầu và cuối

    return slug;
}

function convertTimestamp(timestamp) {
    const [hours, minutes, seconds] = timestamp.replace(',', '.').split(':');
    return parseFloat(hours) * 3600 + parseFloat(minutes) * 60 + parseFloat(seconds);
}
function check_srt_errors(srtContent) {
    /**
     * Kiểm tra định dạng thời gian trong nội dung SRT.
     * @param {string} srtContent - Nội dung chuỗi của file SRT.
     * @returns {string[]} - Mảng chứa các lỗi, nếu không có lỗi thì trả về mảng rỗng.
     */
    const errors = [];
    
    // Biểu thức chính quy cho định dạng thời gian: HH:MM:SS,mmm
    const timePattern = /^\d{2}:\d{2}:\d{2},\d{3}$/;
    
    try {
        // Tách nội dung thành các dòng
        const lines = srtContent.split('\n').map(line => line.trim());
        
        lines.forEach((line, index) => {
            const lineNumber = index + 1;
            
            // Kiểm tra dòng có định dạng thời gian (chứa -->)
            if (line.includes('-->')) {
                const times = line.split('-->');
                if (times.length !== 2) {
                    errors.push(`Dòng ${lineNumber}: Định dạng thời gian không hợp lệ, cần có ' --> ' giữa hai thời gian`);
                    return;
                }
                
                const startTime = times[0].trim();
                const endTime = times[1].trim();
                
                // Kiểm tra định dạng thời gian
                if (!timePattern.test(startTime)) {
                    errors.push(`Dòng ${lineNumber}: Thời gian bắt đầu '${startTime}' không đúng định dạng HH:MM:SS,mmm`);
                }
                if (!timePattern.test(endTime)) {
                    errors.push(`Dòng ${lineNumber}: Thời gian kết thúc '${endTime}' không đúng định dạng HH:MM:SS,mmm`);
                }
                
                // Kiểm tra giá trị thời gian hợp lệ
                if (timePattern.test(startTime)) {
                    const [h, m, sMs] = startTime.split(':');
                    const [s, ms] = sMs.split(',');
                    
                    if (parseInt(h) > 99 || parseInt(m) > 59 || parseInt(s) > 59 || parseInt(ms) > 999) {
                        errors.push(`Dòng ${lineNumber}: Giá trị thời gian bắt đầu '${startTime}' không hợp lệ`);
                    }
                }
                
                if (timePattern.test(endTime)) {
                    const [h, m, sMs] = endTime.split(':');
                    const [s, ms] = sMs.split(',');
                    
                    if (parseInt(h) > 99 || parseInt(m) > 59 || parseInt(s) > 59 || parseInt(ms) > 999) {
                        errors.push(`Dòng ${lineNumber}: Giá trị thời gian kết thúc '${endTime}' không hợp lệ`);
                    }
                }
            }
        });
        
    } catch (error) {
        errors.push(`Lỗi khi xử lý nội dung: ${error.message}`);
    }
    
    return errors;
}

function srt_parser(fileContent) {
    // Hàm chuyển đổi timestamp từ HH:MM:SS,mmm sang giây
    function convertTimestamp(timestamp) {
        const [hours, minutes, seconds] = timestamp.split(':');
        const [secs, millis] = seconds.split(',');
        return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(secs) + parseInt(millis) / 1000;
    }

    const subtitles = [];
    let currentSubtitle = null;
    
    // Tách nội dung file thành từng dòng và loại bỏ dòng trống ở đầu/cuối
    const lines = fileContent.trim().split('\n');
    
    let i = 0;
    while (i < lines.length) {
        const line = lines[i].trim();
        
        // Kiểm tra nếu là số thứ tự phụ đề
        if (/^\d+$/.test(line) && i + 1 < lines.length && lines[i + 1].match(/\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}/)) {
            if (currentSubtitle) {
                subtitles.push(currentSubtitle);
            }
            currentSubtitle = {
                index: parseInt(line),
                voice_index: null,
                text: '',
                speaker: null
            };
            i++;
            continue;
        }
        
        // Nếu là timestamp
        const timestampMatch = line.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
        if (timestampMatch && currentSubtitle) {
            const [, startTime, endTime] = timestampMatch;
            currentSubtitle.start_time = startTime;
            currentSubtitle.end_time = endTime;
            currentSubtitle.start_seconds = convertTimestamp(startTime);
            currentSubtitle.end_seconds = convertTimestamp(endTime);
            i++;
            continue;
        }
        
        // Nếu là nội dung phụ đề
        if (currentSubtitle && line) {
            const ex = line.split('|');
            const text = ex.length > 1 ? line.replace(ex[0] + '|', '') : line;
            const speaker = ex.length > 1 ? ex[0] : null;

            if (currentSubtitle.text) {
                currentSubtitle.text += '\n' + text;
            } else {
                currentSubtitle.text = text;
            }
            currentSubtitle.speaker = speaker;
        }
        i++;
    }
    
    // Thêm phụ đề cuối cùng nếu có
    if (currentSubtitle) {
        subtitles.push(currentSubtitle);
    }
    
    return subtitles;
}

function srt_parse(fileContent) {
    // Hàm chuyển đổi timestamp từ HH:MM:SS,mmm sang giây
    

    const subtitles = [];
    let currentSubtitle = null;
    
    // Tách nội dung file thành từng dòng và loại bỏ dòng trống ở đầu/cuối
    const lines = fileContent.trim().split('\n');
    
    let i = 0;
    while (i < lines.length) {
        const line = lines[i].trim();
        
        // Nếu là số thứ tự phụ đề
        if (/^\d+$/.test(line)) {
            if (currentSubtitle) {
                subtitles.push(currentSubtitle);
            }
            currentSubtitle = {
                index: parseInt(line),
                voice_index: null,
                text: ''
            };
            i++;
            continue;
        }
        
        // Nếu là timestamp
        const timestampMatch = line.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
        if (timestampMatch && currentSubtitle) {
            const [, startTime, endTime] = timestampMatch;
            currentSubtitle.start_time = startTime;
            currentSubtitle.end_time = endTime;
            currentSubtitle.start_seconds = convertTimestamp(startTime);
            currentSubtitle.end_seconds = convertTimestamp(endTime);
            i++;
            continue;
        }
        
        // Nếu là nội dung phụ đề
        if (currentSubtitle && line) {
            if (currentSubtitle.text) {
                ex = line.split('|');
                if (ex.length > 1) {
                    currentSubtitle.text += '\n' + line.replace(ex[0] + '|', '');
                } else {
                    currentSubtitle.text += '\n' + line;
                }
                currentSubtitle.speaker = ex[0];
            } else {
                ex = line.split('|');
                if (ex.length > 1) {
                    currentSubtitle.text = line.replace(ex[0] + '|', '');
                } else {
                    currentSubtitle.text = line;
                }
                currentSubtitle.speaker = ex[0];
            }
            
        }
        i++;
    }
    
    // Thêm phụ đề cuối cùng nếu có
    if (currentSubtitle) {
        subtitles.push(currentSubtitle);
    }
    
    return subtitles;
}
function hexToRgb(hex, opacity = 100) {
    // Xóa dấu # nếu có
    hex = hex.replace('#', '');
    
    // Parse các giá trị RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    
    // Chuyển đổi opacity từ phần trăm sang decimal
    const alpha = opacity / 100;
    
    // Trả về chuỗi rgba
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}