<template>
  <div class="overlay-item border border-gray-700 rounded-lg p-3 mb-3">
    <!-- Header with enable/disable and remove -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <a-checkbox v-model:checked="text.enabled">
          Enable Text {{ index + 1 }}
        </a-checkbox>
        <span v-if="text.options.text" class="text-xs text-gray-500">
          "{{ text.options.text.substring(0, 20) }}{{ text.options.text.length > 20 ? '...' : '' }}"
        </span>
      </div>
      <div class="flex items-center gap-2">
        <a-button size="small" type="text" @click="duplicateText" title="Duplicate">
          <template #icon>📋</template>
        </a-button>
        <a-button size="small" type="text" danger @click="removeText" title="Remove">
          <template #icon>🗑️</template>
        </a-button>
      </div>
    </div>

    <div v-if="text.enabled">
      <!-- Text Content -->
      <a-form-item label="Text Content" size="small">
        <a-input-group compact>
        <a-input v-model:value="text.options.text" placeholder="Enter your text here..." :rows="2" :max-length="200"
          show-count style="width: 80%" />
            <a-button @click="() => text.options.text = text.options.text.toUpperCase()">
              TT
            </a-button>
            <a-button @click="() => text.options.text = capitalizeWords(text.options.text)">
              Tt
            </a-button>
            <a-button @click="() => text.options.text = text.options.text.toLowerCase()">
              tt
            </a-button>
          </a-input-group>
      </a-form-item>


      <!-- Typography Controls -->
      <a-row :gutter="8">
        <a-col :span="4">
          <a-form-item label="Font Size" size="small">
            <a-input-number v-model:value="text.options.fontSize" :min="8" :max="100" size="small" addon-after="px" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="Font Family" size="small">
            <a-select v-model:value="text.options.fontFamily" size="small">
              <a-select-option v-for="font in availableFonts" :key="font.name" :value="font.name">
                <span
                  :style="{ fontFamily: font.isSystem ? font.name : (loadedFonts.has(font.path) ? font.name : 'inherit') }">
                  {{ font.name }}
                  <span v-if="!font.isSystem" class="text-gray-400 text-xs ml-2">(Custom)</span>
                </span>
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Color" size="small">
            <a-input v-model:value="text.options.color" type="color" size="small" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Background" size="small">
            <a-input v-model:value="text.options.backgroundColor" type="color" size="small" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Border Color" size="small">
            <a-input v-model:value="text.options.borderColor" type="color" size="small" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- Style Controls -->
      <a-row :gutter="8">
        <a-col :span="5">
          <a-input-group compact>
            <a-input-number v-model:value="text.options.timeStart" :min="0" :max="videoDuration" :step="0.1"
              @change="(e) => text.options.timeStart = e" />
            <a-button @click="() => text.options.timeStart = currentTime">
              Start
            </a-button>
          </a-input-group>
        </a-col>
        <a-col :span="5">
          <a-input-group compact>
            <a-input-number v-model:value="text.options.timeEnd" :min="0" :max="videoDuration" :step="0.1"
              @change="(e) => text.options.timeEnd = e" />
            <a-button @click="() => text.options.timeEnd = currentTime">
              End
            </a-button>
          </a-input-group>
        </a-col>
        <a-col :span="2">
          <a-button danger @click="() => (text.options.timeStart = 0, text.options.timeEnd = videoDuration)">
            Reset
          </a-button>
        </a-col>
      <!-- Transform Controls -->
        <a-col :span="6">
          <a-form-item label="Rotation" size="small">
            <a-input-number v-model:value="text.options.rotation" :min="-360" :max="360" size="small" addon-after="°" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="Opacity" size="small">
            <a-slider v-model:value="text.options.opacity" :min="0" :max="100" size="small" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- Position Controls -->
      <a-row :gutter="8">
        <a-col :span="6">
          <a-form-item label="Left/Right" size="small">
            <a-slider v-model:value="text.options.posX" :min="0" :max="100" :step="1" size="small" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="Top/Bottom" size="small">
            <a-slider v-model:value="text.options.posY" :min="0" :max="100" :step="1" size="small" />
          </a-form-item>
        </a-col>
      <!-- Padding Controls -->
        <a-col :span="6">
          <a-form-item label="Padding Width" size="small">
            <a-slider v-model:value="text.options.paddingX" :min="0.1" :max="9.0" :step="0.1" size="small" />
            <span class="text-xs text-gray-500">{{ (text.options.paddingX || 0.6).toFixed(1) }}x font size</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="Padding Height" size="small">
            <a-slider v-model:value="text.options.paddingY" :min="0.1" :max="9.0" :step="0.1" size="small" />
            <span class="text-xs text-gray-500">{{ (text.options.paddingY || 0.6).toFixed(1) }}x font size</span>
          </a-form-item>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import fontService from '@/services/fontService';

const props = defineProps({
  text: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }, 
  currentTime:{
    type: Number,
    required: false
  },
  videoDuration: {
    type: Number,
    default: 0
  },
});

const emit = defineEmits(['remove', 'duplicate']);
const loadedFonts = ref(new Set());
// Available fonts
const availableFonts = computed(() => {
  return fontService.getFonts();
});

// Helper functions
function getPreviewStyle() {
  const options = props.text.options;
  return {
    fontSize: `${(options.fontSize || 24) / 3}px`, // Scale down for preview
    fontFamily: options.fontFamily || 'Arial',
    color: options.color || '#ffffff',
    backgroundColor: options.backgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : options.backgroundColor,
    textAlign: options.align || 'center',
    fontWeight: options.bold ? 'bold' : 'normal',
    padding: '2px 4px',
    borderRadius: '2px',
    opacity: (options.opacity || 100) / 100,
    textShadow: options.borderColor === 'transparent' ? 'none' : `
      -1px -1px 0 ${options.borderColor},
      1px -1px 0 ${options.borderColor},
      -1px  1px 0 ${options.borderColor},
      1px  1px 0 ${options.borderColor}
    `,
    position: 'absolute',
    left: `${options.posX || 50}%`,
    top: `${options.posY || 50}%`,
    transform: `translate(-50%, -50%) rotate(${options.rotation || 0}deg)`,
    maxWidth: '90%',
    wordWrap: 'break-word'
  };
}

function removeText() {
  emit('remove', props.index);
}

function duplicateText() {
  emit('duplicate', props.index);
}
const loadFontForPreview = (font) => {
  if (!font || font.isSystem || loadedFonts.value.has(font.path)) return;

  const fontName = font.name;
  const fontUrl = `http://localhost:8082/fonts/${font.path}`;
  const fontFace = new FontFace(fontName, `url('${fontUrl}')`);

  fontFace.load().then(() => {
    document.fonts.add(fontFace);
    loadedFonts.value.add(font.path);
  }).catch(error => {
    console.error(`Error loading font ${font.path}:`, error);
  });
};
// Load fonts on mount
onMounted(async () => {
  await fontService.loadFonts();
  fontService.getFonts().forEach(font => {
    loadFontForPreview(font);
  });
});

function capitalizeWords(str) {
  const value = str.toLowerCase()
  return value
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}




</script>

<style scoped>

.overlay-preview {
  border-radius: 4px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
  border: 1px solid #d9d9d9;
}

.fixed-text-preview-content {
  margin: 0;
  max-width: 100%;
  word-wrap: break-word;
}
</style>
