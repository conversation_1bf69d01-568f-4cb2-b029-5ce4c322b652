/* @tailwind base; */
/* @tailwind components; */
@tailwind utilities;
@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
/* scrollbar style */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(65, 65, 65, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(48, 48, 48, 0.7);
}
.highlighted {
  background-color: #00C1CD !important;
}

.flex-column {
  display:flex;
  flex-direction:column
}