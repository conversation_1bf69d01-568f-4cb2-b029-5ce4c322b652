const { app } = require('electron');
const OCR = require('paddleocrjson');
const path = require('path');

function mergeOCRText(ocrJson) {
  if (!ocrJson || ocrJson.code !== 100 || !Array.isArray(ocrJson.data)) {
    throw new Error('Invalid OCR JSON format.');
  }

  return ocrJson.data.map((item) => item.text).join(' ');
}
async function paddleocr(image_path) {
  let processId = null;

  const staticDir = path.join(STATIC_DIR, 'PaddleOCR-json');
  const ocr = new OCR(
    'PaddleOCR-json.exe',
    [],
    {
      cwd: staticDir,
    },
    false,
  );
  try {
    ocr.once('init', (id) => {
      processId = id;
    }); // some id 17256 undefined undefined

    // Handle OCR process exit without killing main app
    ocr.once('exit', (code) => {
      console.log(`🔄 PaddleOCR process exited with code: ${code}`);
    });

    const result = await ocr.flush({ image_path });
    return mergeOCRText(result);
  } catch (error) {
    console.error('❌ Error calling paddleocr:', error.message);
    return null;
  } finally {
    if (processId) {
      process.kill(processId, 'SIGKILL'); // hoặc 'SIGTERM'
    }
  }
}

module.exports = {
  paddleocr,
};
