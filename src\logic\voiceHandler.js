// Audio generation functions
const generateAudioForSubtitle = async (subtitle, useTranslatedText = true) => {

  // if (generatingAudio.value) return;

  const text = useTranslatedText ? subtitle.translatedText : subtitle.text;
  if (!text) {
    message.error(useTranslatedText ? 'No translated text available' : 'No text available');
    return;
  }

  // Check if any voice is selected for this subtitle
  if (!subtitle.isVoice || subtitle.isVoice === 0) {
    message.error(t('voiceConfig.selectVoice'));
    return;
  }

  try {
    generatingAudio.value = true;
    state.currentPlayingSubtitleId = subtitle.id;

    // Generate audio for the selected voice
    const voiceNumber = subtitle.isVoice;
    const voiceConfig = selectedVoiceConfig.value[`voice${voiceNumber}`];
    await generateSingleVoiceAudio(subtitle, text, voiceConfig, voiceNumber);

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    generatingAudio.value = false;
    state.currentPlayingSubtitleId = null;
  }
};

// Helper function to detect engine from voice ID
const detectEngineFromVoiceId = (voiceId) => {
  if (voiceId.startsWith('openai_')) return 'openai';
  if (voiceId.startsWith('mimimax_')) return 'mimimax';
  if (voiceId.startsWith('vbee_')) return 'vbee';

  // Check if it's a direct VBee voice ID (without prefix)
  const isVbeeVoice = ttsStore.getVbeeVoices()?.some(v => v.id === voiceId);
  if (isVbeeVoice) return 'vbee';

  return 'capcut'; // default for CapCut/TikTok voices
};

// Helper function to extract original voice ID
const extractOriginalVoiceId = (voiceId) => {
  if (voiceId.startsWith('openai_')) {
    // Format: openai_voiceId_langCode
    const parts = voiceId.split('_');
    return parts[1]; // return the original voice ID
  }
  if (voiceId.startsWith('mimimax_')) {
    return voiceId.replace('mimimax_', '');
  }
  if (voiceId.startsWith('vbee_')) {
    return voiceId.replace('vbee_', '');
  }

  // For direct VBee voice IDs or CapCut/TikTok voices, return as-is
  return voiceId;
};

// Helper function to get voice name from voice ID
const getVoiceName = (voiceId) => {
  // Try to find in CapCut/TikTok speakers first
  const capCutVoice = ttsStore.speakers.find(s => s.id === voiceId);
  if (capCutVoice) return capCutVoice.name;

  // Check OpenAI voices
  if (voiceId.startsWith('openai_')) {
    const parts = voiceId.split('_');
    const originalId = parts[1];
    const langCode = parts[2];
    const openaiVoice = ttsStore.getOpenAIVoicesByLanguage(langCode).find(v => v.id === originalId);
    if (openaiVoice) return openaiVoice.name;
  }

  // Check Mimimax voices
  if (voiceId.startsWith('mimimax_')) {
    const originalId = voiceId.replace('mimimax_', '');
    const mimimaxVoice = ttsStore.getMimimaxVoices().find(v => v.id === originalId);
    if (mimimaxVoice) return mimimaxVoice.name;
  }

  // Check VBee voices
  if (voiceId.startsWith('vbee_')) {
    const originalId = voiceId.replace('vbee_', '');
    const vbeeVoice = ttsStore.getVbeeVoices().find(v => v.id === originalId);
    if (vbeeVoice) return vbeeVoice.name;
  }

  // Check if it's a direct VBee voice ID (without prefix)
  const directVbeeVoice = ttsStore.getVbeeVoices().find(v => v.id === voiceId);
  if (directVbeeVoice) return directVbeeVoice.name;

  return 'Unknown Voice';
};

const generateSingleVoiceAudio = async (subtitle, text, voiceConfig, voiceNumber) => {
  if (!voiceConfig.enabled) return;

  // Detect engine from voice ID
  const detectedEngine = detectEngineFromVoiceId(voiceConfig.speaker);
  const originalVoiceId = extractOriginalVoiceId(voiceConfig.speaker);

  console.log(`🎵 Voice ${voiceNumber}: Using ${detectedEngine} engine with voice ${originalVoiceId}`);

  const audio_config = {
    speech_rate: voiceConfig.speed,
    volume_gain_db: voiceConfig.volume,
    pitch: voiceConfig.pitch,
    rate: voiceConfig.rate,
    trim: voiceConfig.trim
  };

  try {
    let requestConfig = {
      text: text,
      speaker: originalVoiceId, // Use original voice ID
      workspaceId: ttsStore.workspaceId,
      cookie: ttsStore.cookie,
      typeEngine: detectedEngine, // Use detected engine instead of global setting
      language: selectedVoiceConfig.value.language,
      audio_config
    };

    // Add engine-specific config based on detected engine
    if (detectedEngine === 'openai') {
      requestConfig.openaiConfig = {
        apiKey: ttsStore.openaiTTS.apiKey,
        baseURL: ttsStore.openaiTTS.baseURL,
        speed: voiceConfig.speed || ttsStore.openaiTTS.speed,
        format: ttsStore.openaiTTS.format
      };
    }

    if (detectedEngine === 'mimimax') {
      requestConfig.mimimaxConfig = {
        apiKey: ttsStore.mimimaxTTS.apiKey,
        groupId: ttsStore.mimimaxTTS.groupId,
        selectedVoice: originalVoiceId,
        voiceSettings: {
          speed: voiceConfig.speed || ttsStore.mimimaxTTS.voiceSettings.speed,
          vol: ttsStore.mimimaxTTS.voiceSettings.vol,
          pitch: voiceConfig.pitch || ttsStore.mimimaxTTS.voiceSettings.pitch
        },
        audioSettings: ttsStore.mimimaxTTS.audioSettings
      };
    }

    if (detectedEngine === 'vbee') {
      requestConfig.vbeeConfig = {
        apiKey: ttsStore.vbee.apiKey,
        baseURL: ttsStore.vbee.baseURL,
        selectedVoice: originalVoiceId,
        voiceSettings: {
          speed: voiceConfig.speed || ttsStore.vbee.voiceSettings.speed,
          volume: ttsStore.vbee.voiceSettings.volume,
          pitch: voiceConfig.pitch || ttsStore.vbee.voiceSettings.pitch
        },
        audioSettings: ttsStore.vbee.audioSettings
      };
    }

    const response = await window.electronAPI.generateTTS(requestConfig);

    if (response.success) {
      console.log(`✅ Voice ${voiceNumber} generated successfully using ${detectedEngine} engine`);

      // Update the subtitle with the audio URL
      const updatedSubtitles = [...srtItems.value];
      const index = updatedSubtitles.findIndex(s => s.id === subtitle.id);

      if (index !== -1) {
        updatedSubtitles[index] = {
          ...updatedSubtitles[index],
          audioUrl: response.audioUrl,
          [`audioUrl${voiceNumber}`]: response.audioUrl,
          [`audioDuration${voiceNumber}`]: response.duration,
          [`isGenerated${voiceNumber}`]: true,
          [`engine${voiceNumber}`]: detectedEngine // Store which engine was used
        };


        srtItems.value = updatedSubtitles;

        // Add to generated audios in the store
        const newAudio = {
          id: Date.now() + voiceNumber,
          text: text,
          speaker: originalVoiceId, // Use original voice ID
          url: response.audioUrl,
          duration: response.duration,
          timestamp: new Date().toISOString(),
          voice: `${getVoiceName(voiceConfig.speaker)} (${detectedEngine.toUpperCase()})`,
          subtitleId: subtitle.id,
          voiceNumber: voiceNumber,
          engine: detectedEngine
        };

        ttsStore.generatedAudios.push(newAudio);
      }
    } else {
      throw new Error(`${detectedEngine.toUpperCase()} TTS failed: ${response.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`❌ Error generating audio for voice ${voiceNumber} using ${detectedEngine} engine:`, error);
    throw new Error(`Failed to generate audio using ${detectedEngine.toUpperCase()} engine: ${error.message}`);
  }
};