const path = require('path');
const fs = require('fs');
const { execWithLog, getAudioDuration } = require('./utils');
const { spawn } = require('child_process');
const { formatTimeHMS } = require('./videoCutter');

/**
 * Render video with SRT array
 * @param {Object} event - Electron event
 * @param {Object} options - Rendering options
 * @param {Array} options.srtArray - Array of SRT items with audio URLs
 * @param {string} options.videoPath - Path to the input video
 * @param {string} options.outputDir - Directory to save the output (optional)
 * @param {string} options.subtitleStyle - Subtitle style (default, white, yellow)
 * @param {boolean} options.addLogo - Whether to add logo
 * @param {string} options.logoPath - Path to logo image
 * @param {string} options.addText - Text to overlay
 * @param {string} options.textPosition - Position of text overlay (top, bottom)
 * @param {string} options.videoCodec - Video codec to use (default: h264_nvenc)
 * @param {string} options.audioBitrate - Audio bitrate in kbps (default: 192)
 * @returns {Promise<Object>} - Result object
 */
async function renderVideoWithSrt(event, {
  srtArray,
  videoPath,
  outputDir,
  subtitleStyle = 'default',
  addLogo = false,
  logoPath = '',
  addText = '',
  textPosition = 'bottom',
  audioBitrate = '192'
}) {
  if (!videoPath || !fs.existsSync(videoPath)) {
    return {
      success: false,
      error: "Input video file does not exist"
    };
  }

  if (!srtArray || !Array.isArray(srtArray) || srtArray.length === 0) {
    return {
      success: false,
      error: "SRT array is empty or invalid"
    };
  }

  try {
    // Create temp directory for processing
    const { dir, name, ext } = path.parse(videoPath);
    const tempDir = path.join(outputDir || dir, `${name}_temp_${Date.now()}`);

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Create output directory if it doesn't exist
    const finalOutputDir = outputDir || dir;
    if (!fs.existsSync(finalOutputDir)) {
      fs.mkdirSync(finalOutputDir, { recursive: true });
    }

    // Generate output filename
    const outputPath = path.join(finalOutputDir, `${name}_rendered${ext}`);

    // Step 1: Create ASS subtitle file
    const assPath = await createAssSubtitles(srtArray, tempDir, subtitleStyle);

    // Step 2: Process each segment
    const segments = [];
    const batchSize = 3; // Process only 3 segments at a time to avoid overloading

    for (let i = 0; i < srtArray.length; i += batchSize) {
      const batch = [];

      // Create a batch of segments to process
      for (let j = 0; j < batchSize && i + j < srtArray.length; j++) {
        const index = i + j;
        const subtitle = srtArray[index];
        batch.push({ subtitle, index });
      }

      // Process this batch
      event.sender.send('render-video-progress', {
        data: `Processing segments ${i+1}-${Math.min(i+batchSize, srtArray.length)} of ${srtArray.length}`,
        code: 0
      });

      // Process each segment in the batch sequentially
      for (const { subtitle, index } of batch) {
        event.sender.send('render-video-progress', {
          data: `Processing segment ${index+1}/${srtArray.length}`,
          code: 0
        });

        // Cut video segment
        const segmentPath = await cutVideoSegment(
          event,
          videoPath,
          tempDir,
          subtitle,
          index,
          audioBitrate
        );

        if (segmentPath) {
          segments.push({
            path: segmentPath,
            subtitle
          });
        }
      }
    }

    // Step 3: Merge segments
    event.sender.send('render-video-progress', {
      data: `Merging ${segments.length} segments...`,
      code: 0
    });

    const finalPath = await mergeSegments(
      event,
      segments,
      outputPath,
      tempDir,
      assPath,
      addLogo,
      logoPath,
      addText,
      textPosition,
      audioBitrate
    );

    // Export SRT file with adjusted timing
    await exportSrtFile(srtArray, finalPath, event);

    // Clean up temp directory
    // Uncomment to enable cleanup
    // fs.rmSync(tempDir, { recursive: true, force: true });

    return {
      success: true,
      outputPath: finalPath,
      message: "Video rendered successfully"
    };
  } catch (err) {
    console.error("Error rendering video:", err);
    return {
      success: false,
      error: err.message || "Unknown error occurred"
    };
  }
}

/**
 * Create ASS subtitle file from SRT array
 * @param {Array} srtArray - Array of SRT items
 * @param {string} outputDir - Directory to save the subtitle file
 * @param {string} style - Subtitle style (default, white, yellow)
 * @returns {Promise<string>} - Path to the created subtitle file
 */
async function createAssSubtitles(srtArray, outputDir, style = 'default') {
  // Define styles based on the selected option
  let primaryColor, outlineColor, backgroundColor;

  switch (style) {
    case 'white':
      primaryColor = '&HFFFFFF&'; // White
      outlineColor = '&H000000&'; // Black outline
      backgroundColor = '&H000000&'; // Black background
      break;
    case 'yellow':
      primaryColor = '&H00FFFF&'; // Yellow
      outlineColor = '&H000000&'; // Black outline
      backgroundColor = '&H000000&'; // Black background
      break;
    default:
      primaryColor = '&HFFFFFF&'; // White
      outlineColor = '&H000000&'; // Black outline
      backgroundColor = '&H80000000&'; // Semi-transparent black background
  }

  // ASS header
  let assContent = `[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,${primaryColor},&H000000&,${outlineColor},${backgroundColor},0,0,0,0,100,100,0,0,1,2,2,2,20,20,20,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;

  // Add each subtitle
  for (const subtitle of srtArray) {
    const startTime = convertSrtTimeToAss(subtitle.start);
    const endTime = convertSrtTimeToAss(subtitle.end);
    const text = subtitle.translatedText || subtitle.text;

    assContent += `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${text}\n`;
  }

  // Write to file
  const assPath = path.join(outputDir, 'subtitles.ass');
  fs.writeFileSync(assPath, assContent, 'utf8');

  return assPath;
}

/**
 * Convert SRT time format (00:00:00,000) to ASS time format (0:00:00.00)
 * @param {string} srtTime - Time in SRT format
 * @returns {string} - Time in ASS format
 */
function convertSrtTimeToAss(srtTime) {
  // SRT: 00:00:00,000 -> ASS: 0:00:00.00
  const parts = srtTime.split(',');
  const timeParts = parts[0].split(':');
  const milliseconds = parts[1].substring(0, 2); // Take only first 2 digits

  // Remove leading zero from hours
  const hours = parseInt(timeParts[0], 10);

  return `${hours}:${timeParts[1]}:${timeParts[2]}.${milliseconds}`;
}

/**
 * Cut video segment based on subtitle timing
 * @param {Object} event - Electron event
 * @param {string} videoPath - Path to the input video
 * @param {string} outputDir - Directory to save the output
 * @param {Object} subtitle - Subtitle object
 * @param {number} index - Segment index
 * @param {string} videoCodec - Video codec to use
 * @param {string} audioBitrate - Audio bitrate in kbps
 * @returns {Promise<string>} - Path to the cut segment
 */
async function cutVideoSegment(
  event,
  videoPath,
  outputDir,
  subtitle,
  index,
  audioBitrate = '192'
) {
  try {
    // Get audio duration if available
    let audioDuration = 0;
    if (subtitle.audioUrl && subtitle.audioUrl.startsWith('file://')) {
      const audioPath = subtitle.audioUrl.replace('file://', '');
      if (fs.existsSync(audioPath)) {
        try {
          audioDuration = await getAudioDuration(audioPath);
        } catch (audioErr) {
          console.warn(`Warning: Could not get audio duration for segment ${index}:`, audioErr);
          // Continue without audio duration
        }
      }
    }

    // Calculate segment timing
    const startTime = subtitle.startTime;
    const endTime = subtitle.endTime;
    let duration = endTime - startTime;

    // Ensure minimum duration to avoid FFmpeg errors
    if (duration < 0.1) {
      console.warn(`Warning: Segment ${index} duration is too short (${duration}s), setting to 0.1s`);
      duration = 0.1;
    }

    // If audio is longer than subtitle duration, adjust video speed
    let speedFactor = 1.0;
    if (audioDuration > 0 && audioDuration > duration) {
      // Slow down video to match audio duration
      speedFactor = duration / audioDuration;
      duration = audioDuration;
    }

    // Format times for ffmpeg
    const startTimeStr = formatTimeHMS(startTime);
    const durationStr = formatTimeHMS(duration);

    // Output path for this segment
    const segmentPath = path.join(outputDir, `segment_${index.toString().padStart(4, '0')}.mp4`);

    // Prepare ffmpeg arguments
    const ffmpegArgs = [
      '-y',  // Overwrite output file if it exists
      '-i', videoPath,
      '-ss', startTimeStr,
      '-t', durationStr
    ];

    // Add speed adjustment if needed
    if (speedFactor !== 1.0) {
      ffmpegArgs.push(
        '-filter:v', `setpts=${1/speedFactor}*PTS`,
        '-filter:a', `atempo=${speedFactor}`
      );
    }

    // Always use CPU encoding to avoid NVENC issues
    ffmpegArgs.push(
      '-c:v', 'libx264',
      '-preset', 'fast', // Use 'fast' instead of 'medium' for better performance
      '-crf', '23'       // Constant Rate Factor for quality (lower is better)
    );

    // Add audio settings
    ffmpegArgs.push(
      '-c:a', 'aac',
      '-b:a', `${audioBitrate}k`,
      segmentPath
    );

    // Execute ffmpeg command
    await execWithLog(event, 'ffmpeg', ffmpegArgs);

    // Add a small delay to avoid overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify the file was created and has content
    if (!fs.existsSync(segmentPath) || fs.statSync(segmentPath).size === 0) {
      throw new Error(`Failed to create segment file or file is empty: ${segmentPath}`);
    }

    return segmentPath;
  } catch (err) {
    console.error(`Error cutting segment ${index}:`, err);
    event.sender.send('render-video-progress', {
      data: `Error cutting segment ${index}: ${err.message}`,
      code: 1
    });

    // Try again with simpler settings if it failed
    try {
      console.log(`Retrying segment ${index} with simpler settings...`);

      // Output path for this segment
      const segmentPath = path.join(outputDir, `segment_${index.toString().padStart(4, '0')}.mp4`);

      // Simpler ffmpeg arguments
      const ffmpegArgs = [
        '-y',                // Overwrite output file if it exists
        '-i', videoPath,     // Input file
        '-ss', formatTimeHMS(subtitle.startTime),  // Start time
        '-t', formatTimeHMS(subtitle.endTime - subtitle.startTime),  // Duration
        '-c:v', 'libx264',   // Use CPU encoding
        '-preset', 'ultrafast', // Fastest preset
        '-crf', '28',        // Lower quality but faster
        '-c:a', 'aac',       // Audio codec
        '-b:a', '128k',      // Lower audio bitrate
        segmentPath          // Output path
      ];

      // Execute ffmpeg command
      await execWithLog(event, 'ffmpeg', ffmpegArgs);

      // Add a small delay
      await new Promise(resolve => setTimeout(resolve, 200));

      // Verify the file was created and has content
      if (!fs.existsSync(segmentPath) || fs.statSync(segmentPath).size === 0) {
        throw new Error(`Failed to create segment file or file is empty on retry: ${segmentPath}`);
      }

      return segmentPath;
    } catch (retryErr) {
      console.error(`Error on retry for segment ${index}:`, retryErr);
      event.sender.send('render-video-progress', {
        data: `Failed to process segment ${index} even after retry. Skipping.`,
        code: 1
      });
      return null;
    }
  }
}

/**
 * Merge video segments with audio
 * @param {Object} event - Electron event
 * @param {Array} segments - Array of segment objects
 * @param {string} outputPath - Path to the output file
 * @param {string} tempDir - Temporary directory
 * @param {string} assPath - Path to ASS subtitle file
 * @param {boolean} addLogo - Whether to add logo
 * @param {string} logoPath - Path to logo image
 * @param {string} addText - Text to overlay
 * @param {string} textPosition - Position of text overlay
 * @param {string} videoCodec - Video codec to use
 * @param {string} audioBitrate - Audio bitrate in kbps
 * @returns {Promise<string>} - Path to the merged video
 */
async function mergeSegments(
  event,
  segments,
  outputPath,
  tempDir,
  assPath,
  addLogo = false,
  logoPath = '',
  addText = '',
  textPosition = 'bottom',
  audioBitrate = '192'
) {
  // Create a concat file
  const concatFilePath = path.join(tempDir, 'concat.txt');
  let concatContent = '';

  segments.forEach(segment => {
    concatContent += `file '${segment.path.replace(/\\/g, '/')}'\n`;
  });

  fs.writeFileSync(concatFilePath, concatContent, 'utf8');

  // Prepare ffmpeg arguments
  const ffmpegArgs = [
    '-y',  // Overwrite output file if it exists
    '-f', 'concat',
    '-safe', '0',
    '-i', concatFilePath
  ];

  // Add subtitle file
  ffmpegArgs.push('-i', assPath);

  // Add logo if specified
  let filterComplex = '';
  let filterIndex = 0;

  if (addLogo && logoPath && fs.existsSync(logoPath)) {
    ffmpegArgs.push('-i', logoPath);
    filterComplex += `[0:v][2:v] overlay=main_w-overlay_w-10:10 [v${filterIndex}]; `;
    filterIndex++;
  }

  // Add text overlay if specified
  if (addText) {
    const yPosition = textPosition === 'top' ? '10' : 'main_h-text_h-10';
    const prevIndex = filterIndex > 0 ? `[v${filterIndex-1}]` : '[0:v]';
    filterComplex += `${prevIndex} drawtext=text='${addText}':fontcolor=white:fontsize=24:box=1:boxcolor=black@0.5:boxborderw=5:x=(w-text_w)/2:y=${yPosition} [v${filterIndex}]; `;
    filterIndex++;
  }

  // Finalize filter complex
  if (filterComplex) {
    // Remove trailing semicolon and space
    filterComplex = filterComplex.slice(0, -2);
    ffmpegArgs.push('-filter_complex', filterComplex);
    ffmpegArgs.push('-map', `[v${filterIndex-1}]`);
    ffmpegArgs.push('-map', '0:a');
  }

  // Add subtitle mapping
  ffmpegArgs.push('-map', '1:0');
  ffmpegArgs.push('-c:s', 'copy');

  // Always use CPU encoding to avoid NVENC issues
  ffmpegArgs.push(
    '-c:v', 'libx264',
    '-preset', 'medium',
    '-crf', '23'
  );

  // Add audio settings
  ffmpegArgs.push(
    '-c:a', 'aac',
    '-b:a', `${audioBitrate}k`,
    outputPath
  );

  // Execute ffmpeg command
  await execWithLog(event, 'ffmpeg', ffmpegArgs);

  return outputPath;
}

/**
 * Format time in seconds to SRT time format (HH:MM:SS,mmm)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
function formatSrtTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Export SRT file with original timing from the SRT array
 * @param {Array} srtArray - Array of subtitle objects
 * @param {string} finalOutput - Path to the final video output
 * @param {Object} event - Electron event object for progress updates
 */
async function exportSrtFile(srtArray, finalOutput, event) {
  try {
    // Generate SRT content with original timing
    let srtContent = '';

    // Process each subtitle with original timing
    srtArray.forEach((srt, index) => {
      // Use original timing from SRT
      const startTime = srt.startTime || 0;
      const endTime = srt.endTime || startTime + (srt.duration || 1);

      // Use translated text if available, otherwise use original text
      const text = srt.translatedText || srt.text || '';

      // Format SRT entry
      srtContent += `${index + 1}\n`;
      srtContent += `${formatSrtTime(startTime)} --> ${formatSrtTime(endTime)}\n`;
      srtContent += `${text}\n\n`;
    });

    // Generate SRT file path based on video output path
    const videoBaseName = path.basename(finalOutput, path.extname(finalOutput));
    const srtFilePath = path.join(path.dirname(finalOutput), `${videoBaseName}.srt`);

    // Write SRT file
    fs.writeFileSync(srtFilePath, srtContent, 'utf8');

    console.log(`📝 SRT file exported: ${srtFilePath}`);
    event?.sender?.send('render-video-progress', {
      data: `📝 SRT file exported: ${path.basename(srtFilePath)}`,
      code: 0,
    });

    return srtFilePath;
  } catch (error) {
    console.error('❌ Error exporting SRT file:', error);
    event?.sender?.send('render-video-progress', {
      data: `❌ Error exporting SRT file: ${error.message}`,
      code: 1,
    });
    throw error;
  }
}

module.exports = {
  renderVideoWithSrt
};
