import { reactive, h } from 'vue';

export const state = reactive({
  contentStream: null,
  videoPlayer: null,
  currentPlayingSubtitleId: null,
  syncTable: true,
  currentTime: 0,
  cropData: null,
  cropText: null,
  assOptionsForVideo: {},
  activeTab: 'editor',
  // Synchronized playback settings
  originalVideoSettings: null,
  isSynchronizedPlayback: false,
  // Global playback system
  isGlobalPlayback: false,
  globalAudioElements: new Map(), // Map of subtitle ID to audio element
  globalPlaybackStartTime: 0,
  audioEnabled: false, // Track if audio has been enabled by user

  // Speed balancing system
  speedBalancingEnabled: false, // Enable/disable speed balancing
  joinTime: 0.5, // Join time for speed calculations in seconds
  webAudioSupported: false, // Track if Web Audio API is supported
  tabs: [
    {
      key: '/',
      label: 'Editor',
      icon: () =>
        h(
          'svg',
          {
            width: 16,
            height: 16,
            viewBox: '0 0 24 24',
            fill: 'none',
            stroke: 'currentColor',
            strokeWidth: 2,
          },
          [
            // video icon path
            h('path', { d: 'M5 4l14 0' }),
            h('path', { d: 'M5 8l14 0' }),
            h('path', { d: 'M5 12l14 0' }),
            h('path', { d: 'M5 16l14 0' }),
          ],
        ),
    },
    {
      key: '/dashboard',
      label: 'Tools',
      // icon: () =>
      //   h(
      //     'svg',
      //     {
      //       width: 16,
      //       height: 16,
      //       viewBox: '0 0 24 24',
      //       fill: 'none',
      //       stroke: 'currentColor',
      //       strokeWidth: 2,
      //     },
      //     [
      //       // tools ai icon path
      //       h('path', { d: 'M5 4l14 0' }),
      //       h('path', { d: 'M5 8l14 0' }),
      //       h('path', { d: 'M5 12l14 0' }),
      //       h('path', { d: 'M5 16l14 0' }),
      //     ],
      //   ),
    },  
    {
      key: '/MainWindow',
      label: 'Subtitle Info',
    },
  //   {
  //   key: 'summary',
  //   label: 'Summary Novel',
  //   // icon: () => h('svg', {
  //   //   width: 16,
  //   //   height: 16,
  //   //   viewBox: '0 0 24 24',
  //   //   fill: 'none',
  //   //   stroke: 'currentColor',
  //   //   strokeWidth: 2
  //   // }, [
  //   //   // summary icon path
  //   //   h('path', { d: 'M5 4l14 0' }),
  //   //   h('path', { d: 'M5 8l14 0' }),
  //   //   h('path', { d: 'M5 12l14 0' }),
  //   //   h('path', { d: 'M5 16l14 0' }),
  //   // ])
  // },
    {
      key: '/summary-intro',
      label: 'Summary Video',
      // icon: () =>
      //   h(
      //     'svg',
      //     {
      //       width: 16,
      //       height: 16,
      //       viewBox: '0 0 24 24',
      //       fill: 'none',
      //       stroke: 'currentColor',
      //       strokeWidth: 2,
      //     },
      //     [
      //       // summary intro icon path
      //       h('path', { d: 'M5 4l14 0' }),
      //       h('path', { d: 'M5 8l14 0' }),
      //       h('path', { d: 'M5 12l14 0' }),
      //       h('path', { d: 'M5 16l14 0' }),
      //     ],
      //   ),
    },
    {
      key: '/train-gpt',
      label: 'Train Ai',
      // icon: () =>
      //   h(
      //     'svg',
      //     {
      //       width: 16,
      //       height: 16,
      //       viewBox: '0 0 24 24',
      //       fill: 'none',
      //       stroke: 'currentColor',
      //       strokeWidth: 2,
      //     },
      //     [
      //       // train gpt icon path
      //       h('path', { d: 'M5 4l14 0' }),
      //       h('path', { d: 'M5 8l14 0' }),
      //       h('path', { d: 'M5 12l14 0' }),
      //       h('path', { d: 'M5 16l14 0' }),
      //     ],
      //   ),
    },
    {
      key: '/download',
      label: 'Download Tool',
      // icon: () =>
      //   h(
      //     'svg',
      //     {
      //       width: 16,
      //       height: 16,
      //       viewBox: '0 0 24 24',
      //       fill: 'none',
      //       stroke: 'currentColor',
      //       strokeWidth: 2,
      //     },
      //     [
      //       // download icon path
      //       h('path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4' }),
      //       h('polyline', { points: '7,10 12,15 17,10' }),
      //       h('line', { x1: '12', y1: '15', x2: '12', y2: '3' }),
      //     ],
      //   ),
    },
    {
      key: '/configs',
      label: 'Configs',
    },
    {
      key: '/export-srt-capcut',
      label: 'CapCut SRT',
      // icon: () =>
      //   h(
      //     'svg',
      //     {
      //       width: 16,
      //       height: 16,
      //       viewBox: '0 0 24 24',
      //       fill: 'none',
      //       stroke: 'currentColor',
      //       strokeWidth: 2,
      //     },
      //     [
      //       // subtitle/text icon path
      //       h('path', { d: 'M3 7h18' }),
      //       h('path', { d: 'M3 12h18' }),
      //       h('path', { d: 'M3 17h12' }),
      //     ],
      //   ),
    },
    {
      key: '/reload',
      label: 'Reload',
      fn: () => {
        window.location.reload();
      },
    },
  ],
  videoElement: null,
  tabContentRef: 0,
  key: 0,
  isRender: false,
  useTerm: false,
  useNovel: true,
  BATCH_SIZE: 20,
});

window.STATE = state;
