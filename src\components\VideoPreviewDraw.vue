<template>
  <div class="p-2 w-full border border-slate-700 rounded " style="overflow: auto; height: 90vh;">
    <h3>Video Preview
      <!-- full/minize button -->
      <!-- <a-button @click="setFullMinize" size="small" class="my-2">
      {{ isMinimize ? 'Maximize' : 'Minimize' }}
    </a-button> -->
    </h3>

    <!-- Video Player -->
    <div :class="[isMinimize ? '' : 'mx-auto', 'video-container']" :style="{ width: isMinimize ? '100%' : '450px' }">
      <VideoPlayer ref="videoPlayer" :src="videoSrc" @timeupdate="onVideoTimeUpdate" @loadedmetadata="onVideoLoaded"
        size="full" class="preview-video" />

      <!-- show text animation preview -->
      <div v-if="renderOptions.showText" class="text-preview">
        <p class="text-preview-content" :style="{
          fontSize: `${renderOptions.fontSize / 2}px`,
          color: renderOptions.textColor,
          opacity: renderOptions.textOpacity / 100,
          textAlign: getTextAlignment(),
          fontFamily: renderOptions.textFontFamily
        }">
          {{ renderOptions.textValue }}
        </p>
      </div>

      <!-- show subtitle preview -->
      <div v-if="renderOptions.showSubtitle" class="subtitle-preview">
        <p class="subtitle-preview-content" :style="{
          position: 'absolute',
          fontSize: `${getEffectiveSubtitleFontSize()}px`,
          fontFamily: renderOptions.fontFamily,
          color: currentVoiceSettings.subtitleTextColor,
          backgroundColor: currentVoiceSettings.subtitleBackgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : currentVoiceSettings.subtitleBackgroundColor ?? '#000000',
          textAlign: getTextAlignment(),
          fontWeight: currentVoiceSettings.subtitleBold ? 'bold' : 'normal',
          padding: '2px',
          textShadow:
            currentVoiceSettings.subtitleBorderColor === 'transparent'
              ? 'none'
              : `
                  -1px -1px 0 ${currentVoiceSettings.subtitleBorderColor},
                  1px -1px 0 ${currentVoiceSettings.subtitleBorderColor},
                  -1px  1px 0 ${currentVoiceSettings.subtitleBorderColor},
                  1px  1px 0 ${currentVoiceSettings.subtitleBorderColor},
                  -2px  0px 0 ${currentVoiceSettings.subtitleBorderColor},
                  2px  0px 0 ${currentVoiceSettings.subtitleBorderColor},
                  0px -2px 0 ${currentVoiceSettings.subtitleBorderColor},
                  0px  2px 0 ${currentVoiceSettings.subtitleBorderColor}
                `,
          top: `${getAssPositionY()}px`,
          left: `${getAssPositionX()}px`,
          transform: `${getTransformString()}`,
          transformOrigin: 'center center',
          whiteSpace: 'wrap',
          width: 'max-content',
          maxWidth: '90%'
        }">
          {{ getPreviewText() }}
        </p>
      </div>

      <!-- Multiple Logo Overlays Preview -->
      <div
        v-for="(logo, logoIndex) in enabledLogos"
        :key="logo.id || `logo-${logoIndex}`"
        class="logo-overlay-preview"
      >
        <img
          :src="getFilePreviewUrl(logo.file)"
          :alt="`Logo ${logoIndex + 1} Preview`"
          :style="getLogoOverlayStyle(logo, logoIndex)"
        />
      </div>

      <!-- Multiple Image Overlays Preview -->
      <div
        v-for="(image, imageIndex) in enabledImages"
        :key="image.id || `image-${imageIndex}`"
        class="image-overlay-preview"
      >
        <img
          :src="getFilePreviewUrl(image.file)"
          :alt="`Image ${imageIndex + 1} Preview`"
          :style="getImageOverlayStyle(image, imageIndex)"
        />
      </div>

      <!-- Multiple Text Overlays Preview -->
      <div
        v-for="(text, textIndex) in enabledTexts"
        :key="text.id || `text-${textIndex}`"
        class="fixed-text-overlay-preview"
      >
        <div :style="getTextOverlayStyle(text, textIndex)">
          {{ text.options.text }}
        </div>
      </div>

      <!-- Drawing Canvas Overlay -->
      <canvas ref="drawingCanvas" class="drawing-canvas" @mousedown="handleMouseDown" @mousemove="handleMouseMove"
        @mouseup="handleMouseUp" @mouseleave="handleMouseLeave" @click="handleCanvasClick" />
    </div>

    <!-- Drawing Controls -->
    <div class="drawing-controls">
      <a-space class="flex-wrap">
        <a-button
          :type="drawingMode === 'blur' ? 'primary' : 'default'"
          @click="setDrawingMode('blur')"
          size="small"
        >
          <blur-outlined />
          Blur
        </a-button>

        <!-- <a-button :type="drawingMode === 'delogo' ? 'primary' : 'default'" @click="setDrawingMode('delogo')"
          size="small">
          <delete-outlined />
          Logo
        </a-button> -->

        <a-button :type="drawingMode === 'subtitle' ? 'primary' : 'default'" @click="setDrawingMode('subtitle')"
          size="small">
          <font-colors-outlined />
          Subtitle
        </a-button>

        <a-button :type="drawingMode === 'select' ? 'primary' : 'default'" @click="setDrawingMode('select')"
          size="small">
          <drag-outlined />
          Select/Move
        </a-button>

        <a-button :type="drawingMode === 'media' ? 'primary' : 'default'" @click="setDrawingMode('media')"
          size="small">
          <picture-outlined />
          Media Overlay
        </a-button>

        <a-button @click="clearDrawings" size="small">
          <clear-outlined />
          Clear All
        </a-button>

        <a-button @click="undoLastDrawing" size="small">
          <undo-outlined />
          Undo
        </a-button>

        <a-button @click="initializeDefaultBlurArea" size="small" type="dashed">
          <font-colors-outlined />
          Add Default Area
        </a-button>
      </a-space>

      <div v-if="selectedArea" class="selected-area-info">
        <a-divider />
        <p><strong>Selected:</strong> {{ selectedArea.type }} Area</p>
        <a-row :gutter="8">
          <a-col :span="6">
            <a-form-item label="X" size="small">
              <a-input-number v-model:value="selectedArea.x" size="small" @change="redrawCanvas" :min="0" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="Y" size="small">
              <a-input-number v-model:value="selectedArea.y" size="small" @change="redrawCanvas" :min="0" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="Width" size="small">
              <a-input-number v-model:value="selectedArea.width" size="small" @change="redrawCanvas" :min="10" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="Height" size="small">
              <a-input-number v-model:value="selectedArea.height" size="small" @change="redrawCanvas" :min="10" />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>



    <!-- Blur Areas List -->
    <div class="blur-areas-list" v-if="blurAreas.length > 0">
      <h4>Blur/Remove Areas:</h4>
      <a-list size="small" :data-source="blurAreas">
        <template #renderItem="{ item, index }">
          <a-list-item>
            <template #actions>
              <a-input-group compact>
                <a-input-number v-model:value="item.timeStart" :min="0" :max="videoDuration" :step="0.1"
                  @change="(e) => blurAreas[index].timeStart = e" />
                <a-button @click="() => startTimeUpdate(index)">
                  Start
                </a-button>
              </a-input-group>
              <a-input-group compact>
                <a-input-number v-model:value="item.timeEnd" :min="0" :max="videoDuration" :step="0.1"
                  @change="(e) => blurAreas[index].timeEnd = e" />
                <a-button @click="() => endTimeUpdate(index)">
                  End
                </a-button>
                <a-button danger @click="() => resetTimeUpdate(index)">
                  Reset
                </a-button>
              </a-input-group>
              <a-button size="small" type="text" danger @click="removeBlurArea(index)">
                <delete-outlined />
              </a-button>
            </template>
            <a-list-item-meta>
              <template #title>
                {{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }} Area {{ index + 1 }}
              </template>
              <template #description>
                Display: {{ Math.round(item.x) }}, {{ Math.round(item.y) }} - {{ Math.round(item.width) }} x {{
                  Math.round(item.height) }}<br>
                Video: {{ Math.round(getVideoCoords(item).x) }}, {{ Math.round(getVideoCoords(item).y) }} - {{
                  Math.round(getVideoCoords(item).width) }} x {{ Math.round(getVideoCoords(item).height) }}<br>
                <!-- Time: {{ item.timeStart }}s - {{ item.timeEnd }}s -->
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </div>


      <!-- Video Content Analysis -->
      <VideoContentAnalyzer />
    <!-- Video Info Debug -->
    <a-button @click="showDebugInfo = !showDebugInfo" size="small" class="my-2">
      Show Debug
    </a-button>
    <div class="video-info-debug" v-if="showDebugInfo && videoWidth && videoHeight">
      <h4>Video Info:</h4>
      <p>
        Video Dimensions: {{ videoWidth }} x {{ videoHeight }}<br>
        Display Dimensions: {{ videoPlayer?.$refs?.video?.offsetWidth || 0 }} x {{
          videoPlayer?.$refs?.video?.offsetHeight
        || 0 }}<br>
        Scale: {{ (videoWidth / (videoPlayer?.$refs?.video?.offsetWidth || 1)).toFixed(2) }} x {{ (videoHeight /
          (videoPlayer?.$refs?.video?.offsetHeight || 1)).toFixed(2) }}
      </p>

      <div v-if="renderOptions?.assOptions">
        <h5>ASS Coordinate System:</h5>
        <p>
          <strong>ASS Resolution:</strong> {{ getAssResolution().width }}x{{ getAssResolution().height }}<br>
          <strong>Video Resolution:</strong> {{ videoWidth }}x{{ videoHeight }}<br>
          <strong>Display Size:</strong> {{ videoPlayer?.$refs?.video?.offsetWidth || 0 }}x{{
            videoPlayer?.$refs?.video?.offsetHeight || 0 }}<br>
          <strong>Upscaled:</strong> {{ getAssResolution().isUpscaled ? 'Yes' : 'No' }}<br>
          <strong>Scale Factor:</strong> {{ getAssResolution().scaleFactor?.toFixed(2) || '1.00' }}x
        </p>
        <p>
          <strong>Slider Values:</strong> {{ renderOptions.assOptions.posX || 0 }}%, {{ renderOptions.assOptions.posY ||
          0
          }}%<br>
          <strong>ASS Coordinates:</strong> pos({{ getAssOptionsForVideo().assPos?.x || 'N/A' }},{{
            getAssOptionsForVideo().assPos?.y || 'N/A' }})<br>
          <strong>Video Coordinates:</strong> {{ getAssOptionsForVideo().pos?.x || 'N/A' }}, {{
            getAssOptionsForVideo().pos?.y || 'N/A' }}<br>
          <strong>Display Position:</strong> {{ getAssPositionX().toFixed(0) }}, {{ getAssPositionY().toFixed(0)
          }}px<br>
          <strong>Rotation:</strong> {{ renderOptions.assOptions.rotation || 0 }}°<br>
          <strong>Alignment:</strong> {{ renderOptions.assOptions.align || 5 }}<br>
          <strong>Font Size:</strong> ASS={{ getAssOptionsForVideo().assFontSize || 'Auto' }}, Video={{
            getAssOptionsForVideo().fontSize || 'Auto' }}
        </p>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  DeleteOutlined,
  FontColorsOutlined,
  ClearOutlined,
  UndoOutlined,
  DragOutlined,
  PictureOutlined,
} from '@ant-design/icons-vue';
import VideoPlayer from './VideoPlayer.vue';
import VideoContentAnalyzer from './VideoContentAnalyzer.vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import { useTTSStore } from '@/stores/ttsStore';

const subtitleStore = useSubtitleStore();

const ttsStore = useTTSStore();

// Props
const props = defineProps({
  videoSrc: {
    type: String,
    required: true
  },
  renderOptions: {
    type: Object,
    required: true
  },
  timeRange: {
    type: Object,
    required: true
  },
  videoDuration: {
    type: Number,
    default: 0
  },
  currentTime: {
    type: Number,
    default: 0
  }
});

// Emits
const emit = defineEmits([
  'blur-areas-updated',
  'time-range-updated',
  'video-loaded',
  'video-time-update'
]);

// Video preview refs
const videoPlayer = ref(null);
const drawingCanvas = ref(null);

// Video dimensions for coordinate conversion
const videoWidth = ref(0);
const videoHeight = ref(0);

// Drawing state
const isDrawing = ref(false);
const isResizing = ref(false);
const drawingMode = ref('subtitle'); // 'blur', 'delogo', 'subtitle', 'media'
const blurAreas = ref([]);
const currentDrawing = ref(null);
const drawingHistory = ref([]);
const selectedArea = ref(null);
const resizeHandle = ref(null); // 'nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'
const isDragging = ref(false);
const showDebugInfo = ref(false);
const isMinimize = ref(false);

// Media overlay state
const selectedMediaOverlay = ref(null); // 'logo', 'image', 'fixedText'
const isDraggingMedia = ref(false);
const dragStartMediaPos = ref({ x: 0, y: 0 });
const dragStartMediaOptions = ref(null);

// Initialize overlays if not exists
if (!props.renderOptions.overlays) {
  subtitleStore.initializeOverlays();
}

// Computed properties for multiple overlays
const enabledLogos = computed(() => {
  return props.renderOptions.overlays?.logos?.filter(logo => logo.enabled && logo.file) || [];
});

const enabledImages = computed(() => {
  return props.renderOptions.overlays?.images?.filter(image => image.enabled && image.file) || [];
});

const enabledTexts = computed(() => {
  return props.renderOptions.overlays?.texts?.filter(text => text.enabled && text.options?.text) || [];
});

// Local time range for two-way binding
const localTimeRange = reactive({
  start: 0,
  end: 0
});

// Voice settings access
const renderVoiceOptions = subtitleStore.renderVoiceOptions || {};

// Computed property for current voice settings
const currentVoiceSettings = computed(() => {
  return ttsStore.activeTabChild === "subtitle-voice" ? renderVoiceOptions[subtitleStore.selectedVoice] : props.renderOptions
});


// Watch for prop changes
watch(() => props.timeRange, (newRange) => {
  localTimeRange.start = newRange.start;
  localTimeRange.end = newRange.end;
}, { immediate: true });

// Initialize blur areas from subtitle store on mount
onMounted(() => {
  const subtitleStore = useSubtitleStore();
  blurAreas.value = [...(subtitleStore.getBlurAreas() || [])];
  redrawCanvas();
});

// Watch for ASS options changes and emit updates
watch(() => currentVoiceSettings.value?.assOptions, () => {
  // Emit updated ASS options when they change
  if (videoWidth.value && videoHeight.value) {
    emitBlurAreasUpdate();
  }
}, { deep: true });

// Watch for media overlay options changes and redraw canvas
watch(() => [
  props.renderOptions.showLogo,
  props.renderOptions.logoOptions,
  props.renderOptions.showImage,
  props.renderOptions.imageOptions,
  props.renderOptions.showFixedText,
  props.renderOptions.fixedTextOptions
], () => {
  redrawCanvas();
}, { deep: true });

// Initialize canvas on mount
onMounted(() => {
  redrawCanvas();
});

// Coordinate conversion function - converts display coordinates to actual video coordinates
const getVideoCoords = (displayBox) => {
  if (!videoPlayer.value?.$refs?.video) return { x: 0, y: 0, width: 0, height: 0 };

  const videoElement = videoPlayer.value.$refs.video;
  const scaleX = videoWidth.value / videoElement.offsetWidth;
  const scaleY = videoHeight.value / videoElement.offsetHeight;

  return {
    x: displayBox.x * scaleX,
    y: displayBox.y * scaleY,
    width: displayBox.width * scaleX,
    height: displayBox.height * scaleY
  };
};

// Video event handlers
function onVideoLoaded() {
  if (videoPlayer.value?.$refs?.video) {
    const video = videoPlayer.value.$refs.video;

    // Store actual video dimensions
    videoWidth.value = video.videoWidth;
    videoHeight.value = video.videoHeight;

    const duration = video.duration;
    localTimeRange.end = duration;

    emit('video-loaded', video);

    setupCanvas();
    // setTimeout(() => {
    //   initializeDefaultBlurArea();
    // }, 800);
  }
}

function onVideoTimeUpdate() {
  if (videoPlayer.value?.$refs?.video) {
    const currentTime = videoPlayer.value.$refs.video.currentTime;
    emit('video-time-update', currentTime);
  }
}

function setupCanvas() {
  if (drawingCanvas.value && videoPlayer.value?.$refs?.video) {
    const canvas = drawingCanvas.value;
    const video = videoPlayer.value.$refs.video;

    canvas.width = video.clientWidth;
    canvas.height = video.clientHeight;
  }
}



// Initialize default blur area with automatic timeEnd
function initializeDefaultBlurArea() {
  if (props.videoDuration <= 0) {
    message.warning('Please wait for video to load completely');
    return;
  }

  const canvas = drawingCanvas.value;
  if (!canvas) {
    message.warning('Canvas not ready');
    return;
  }

  // If no blur areas exist, create a default subtitle area
  if (blurAreas.value.length === 0) {
    const defaultArea = {
      type: "subtitle",
      x: canvas.width * 0.01, // 1% from left
      y: canvas.height * 0.85, // 85% from top (bottom area)
      width: canvas.width * 0.97, // 97% of video width
      height: canvas.height * 0.04, // 10% of video height
      timeStart: 0,
      timeEnd: props.videoDuration // Automatically use video duration
    };

    blurAreas.value.push(defaultArea);
    console.log('Initialized default blur area:', defaultArea);
    console.log('Video coordinates:', getVideoCoords(defaultArea));
    message.success('Default subtitle area added');
  } else {
    // If areas exist, add a new area based on current drawing mode
    const areaCount = blurAreas.value.length;
    const offset = (areaCount * 20) % 100; // Offset each new area slightly

    const newArea = {
      type: drawingMode.value,
      x: (canvas.width * 0.1) + offset,
      y: (canvas.height * 0.7) + offset,
      width: canvas.width * 0.3,
      height: canvas.height * 0.15,
      timeStart: localTimeRange.start,
      timeEnd: localTimeRange.end || props.videoDuration
    };

    blurAreas.value.push(newArea);
    // console.log('Added new area:', newArea);
    // console.log('Video coordinates:', getVideoCoords(newArea));
    message.success(`New ${drawingMode.value} area added`);
  }

  // Update existing areas' timeEnd if they don't have proper values
  blurAreas.value.forEach(area => {
    if (!area.timeEnd || area.timeEnd === 0 || area.timeEnd > props.videoDuration) {
      area.timeEnd = props.videoDuration;
    }
  });

  // Emit updated blur areas with video coordinates
  emitBlurAreasUpdate();

  // Redraw canvas to show the new area
  nextTick(() => {
    redrawCanvas();
  });
}

// Emit blur areas update with video coordinates and ASS options
function emitBlurAreasUpdate() {
  const areasWithVideoCoords = blurAreas.value.map(area => ({
    ...area,
    videoCoords: getVideoCoords(area)
  }));

  // Include ASS options for video coordinates
  const assOptionsForVideo = getAssOptionsForVideo();

  // Add media overlay coordinates for accurate positioning
  const mediaOverlayCoords = getMediaOverlayCoords();

  const data = {
    areas: areasWithVideoCoords,
    assOptions: assOptionsForVideo,
    mediaOverlayCoords: mediaOverlayCoords,
    isVoice: {
      isVoice1: getAssOptionsForVideo('isVoice1'),
      isVoice2: getAssOptionsForVideo('isVoice2'),
      isVoice3: getAssOptionsForVideo('isVoice3'),
      isVoice4: getAssOptionsForVideo('isVoice4'),
      isVoice5: getAssOptionsForVideo('isVoice5'),
    }
  }
  emit('blur-areas-updated', data);
}

// Method to reload blur areas from store (for profile loading)
function reloadBlurAreas() {
  const subtitleStore = useSubtitleStore();
  blurAreas.value = [...(subtitleStore.getBlurAreas() || [])];
  redrawCanvas();
}

// Expose method to parent component
defineExpose({
  reloadBlurAreas
});

// Dynamic ASS coordinate system based on video resolution
function getAssResolution() {
  if (!videoWidth.value || !videoHeight.value) {
    // Fallback to common 16:9 resolution
    return { width: 1920, height: 1080 };
  }

  // Option 1: Use video's actual resolution (1:1 mapping)
  // This is most accurate but may result in low quality for small videos
  const actualRes = {
    width: videoWidth.value,
    height: videoHeight.value
  };

  // Option 2: Upscale small videos to minimum quality
  // Ensure minimum resolution for better subtitle quality
  const minWidth = 1280;
  const minHeight = 720;

  if (actualRes.width < minWidth || actualRes.height < minHeight) {
    // Calculate upscale factor to reach minimum resolution
    const scaleX = minWidth / actualRes.width;
    const scaleY = minHeight / actualRes.height;
    const scale = Math.max(scaleX, scaleY);

    return {
      width: Math.round(actualRes.width * scale),
      height: Math.round(actualRes.height * scale),
      isUpscaled: true,
      originalWidth: actualRes.width,
      originalHeight: actualRes.height,
      scaleFactor: scale
    };
  }

  return {
    ...actualRes,
    isUpscaled: false,
    scaleFactor: 1
  };
}

// ASS position conversion functions
function getAssPositionX() {
  if (!videoPlayer.value?.$refs?.video || !currentVoiceSettings.value?.assOptions) return 0;

  const videoElement = videoPlayer.value.$refs.video;
  const posXPercent = currentVoiceSettings.value?.assOptions.posX || 0;
  // Map percentage to display position
  // -100% = left (0), 0% = center, 100% = right
  const displayWidth = videoElement.offsetWidth;
  let anchorX = displayWidth * (50 + posXPercent) / 100;

  // Return the anchor point - CSS transform will handle alignment
  return anchorX;
}

function getAssPositionY() {
  if (!videoPlayer.value?.$refs?.video || !currentVoiceSettings.value?.assOptions) return 0;

  const videoElement = videoPlayer.value.$refs.video;
  const posYPercent = currentVoiceSettings.value.assOptions.posY || 0;
  const align = currentVoiceSettings.value.assOptions.align || 5;

  // Map percentage to display position
  // -100% = top (0), 0% = center, 100% = bottom
  const displayHeight = videoElement.offsetHeight;
  let anchorY = displayHeight * (50 + posYPercent) / 100;

  // For CSS positioning, we don't need to adjust for alignment here
  // because we're using CSS transform to handle the alignment
  // The anchorY is the exact position where the text should be anchored

  // console.log(`[Preview Y] posY: ${posYPercent}%, align: ${align} (raw: ${props.renderOptions.assOptions.align}), anchorY: ${anchorY}, displayHeight: ${displayHeight}`);

  return anchorY;
}



// Get ASS options in video coordinates for FFmpeg processing
function getAssOptionsForVideo(isVoice) {
  if (!currentVoiceSettings.value?.assOptions) return {};

  const assOptions = isVoice ? renderVoiceOptions[isVoice]?.assOptions : currentVoiceSettings.value.assOptions;
  const videoCoords = {};

  // Convert position from percentage to ASS coordinate system (like pos(690,957))
  // Only add position if it's not at default center
  const posX = assOptions.posX || 0;
  const posY = assOptions.posY || 0;

  // Check if position is at default center
  // For any alignment, default is center horizontally and vertically
  // We consider 0%, 0% as default position (center X, center Y)
  const isDefaultPosition = (posX === 0 && posY === 0);

  if (!isDefaultPosition && (assOptions.posX !== undefined || assOptions.posY !== undefined)) {
    // Calculate ASS coordinates based on percentage using dynamic resolution
    const assRes = getAssResolution();

    // For dynamic ASS coordinate system:
    // X: -100% = 0, 0% = center, 100% = width
    // Y: -100% = 0, 0% = center, 100% = height
    let assX = assRes.width * (50 + posX) / 100;
    let assY = assRes.height * (50 + posY) / 100;

    // console.log(`[ASS Coords] posX: ${posX}%, posY: ${posY}%, assRes: ${assRes.width}x${assRes.height}, assX: ${assX}, assY: ${assY}, align: ${assOptions.align || 2}`);

    // Since we're using video resolution as ASS resolution, no scaling needed
    // ASS coordinates = Video coordinates (1:1 mapping)
    videoCoords.pos = {
      x: Math.round(assX),
      y: Math.round(assY)
    };

    // Also provide raw ASS coordinates for direct ASS file generation
    videoCoords.assPos = {
      x: Math.round(assX),
      y: Math.round(assY)
    };
  }

  // Font size scaling based on video resolution vs ASS resolution
  if (assOptions.fontSize !== undefined && assOptions.fontSize > 0) {
    // With dynamic resolution (1:1 mapping), no scaling needed
    videoCoords.fontSize = assOptions.fontSize;
    videoCoords.assFontSize = assOptions.fontSize;
  }

  if (assOptions.rotation !== undefined) {
    videoCoords.rotation = assOptions.rotation;
  }

  if (assOptions.align !== undefined) {
    videoCoords.align = assOptions.align;
  }

  // Include ASS resolution info for proper scaling
  const assRes = getAssResolution();
  videoCoords.assResolution = {
    width: assRes.width,
    height: assRes.height
  };

  return videoCoords;
}

// Get media overlay coordinates for FFmpeg processing
function getMediaOverlayCoords() {
  if (!videoPlayer.value?.$refs?.video) {
    return {
      displayDimensions: null,
      logo: null,
      image: null,
      fixedText: null
    };
  }

  const videoElement = videoPlayer.value.$refs.video;
  const displayDimensions = {
    width: videoElement.offsetWidth,
    height: videoElement.offsetHeight
  };

  const result = {
    displayDimensions,
    logo: null,
    image: null,
    fixedText: null
  };

  // Calculate logo coordinates if enabled
  if (props.renderOptions.showLogo && props.renderOptions.logoOptions) {
    const { posX, posY } = props.renderOptions.logoOptions;
    const displayCoords = {
      x: (posX / 100) * displayDimensions.width,
      y: (posY / 100) * displayDimensions.height
    };
    result.logo = {
      displayCoords,
      videoCoords: getVideoCoords(displayCoords),
      options: props.renderOptions.logoOptions
    };
  }

  // Calculate image coordinates if enabled
  if (props.renderOptions.showImage && props.renderOptions.imageOptions) {
    const { posX, posY } = props.renderOptions.imageOptions;
    const displayCoords = {
      x: (posX / 100) * displayDimensions.width,
      y: (posY / 100) * displayDimensions.height
    };
    result.image = {
      displayCoords,
      videoCoords: getVideoCoords(displayCoords),
      options: props.renderOptions.imageOptions
    };
  }

  // Calculate fixed text coordinates if enabled
  if (props.renderOptions.showFixedText && props.renderOptions.fixedTextOptions) {
    const { posX, posY } = props.renderOptions.fixedTextOptions;
    const displayCoords = {
      x: (posX / 100) * displayDimensions.width,
      y: (posY / 100) * displayDimensions.height
    };
    result.fixedText = {
      displayCoords,
      videoCoords: getVideoCoords(displayCoords),
      options: props.renderOptions.fixedTextOptions
    };
  }

  return result;
}

// Helper functions for preview styling
function getEffectiveFontSize() {
  // Use ASS font size override if specified, otherwise use default
  if (currentVoiceSettings.value?.assOptions?.fontSize && currentVoiceSettings.value.assOptions.fontSize > 0) {
    return currentVoiceSettings.value.assOptions.fontSize / 2; // Scale down for preview
  }
  return currentVoiceSettings.value?.fontSize || 24;
}

function getEffectiveSubtitleFontSize() {
  // Use ASS font size override if specified, otherwise use subtitle font size
  if (currentVoiceSettings.value?.assOptions?.fontSize && currentVoiceSettings.value.assOptions.fontSize > 0) {
    // Scale ASS font size to display size (like in ASS system)
    if (videoPlayer.value?.$refs?.video) {
      const assRes = getAssResolution();
      const displayScale = videoPlayer.value.$refs.video.offsetHeight / assRes.height;
      return currentVoiceSettings.value.assOptions.fontSize * displayScale;
    }
    return currentVoiceSettings.value.assOptions.fontSize / 2; // Fallback
  }

  // Use default subtitle font size scaled to display
  if (videoPlayer.value?.$refs?.video) {
    const assRes = getAssResolution();
    const displayScale = videoPlayer.value.$refs.video.offsetHeight / assRes.height;
    return (currentVoiceSettings.value?.subtitleFontSize || 48) * displayScale;
  }

  return (currentVoiceSettings.value?.subtitleFontSize || 48) / 2; // Fallback
}

function getTextAlignment() {
  if (!currentVoiceSettings.value?.assOptions?.align) return 'center';

  // ASS alignment values to CSS text-align mapping
  const alignmentMap = {
    7: 'left',  // Top Left
    8: 'center', // Top Center
    9: 'right',  // Top Right
    4: 'left',   // Middle Left
    5: 'center', // Middle Center
    6: 'right',  // Middle Right
    1: 'left',   // Bottom Left
    2: 'center', // Bottom Center
    3: 'right'   // Bottom Right
  };

  return alignmentMap[currentVoiceSettings.value.assOptions.align] || 'center';
}

function getTransformString() {
  const align = currentVoiceSettings.value?.assOptions?.align || 5;
  const rotation = currentVoiceSettings.value?.assOptions?.rotation || 0;

  // Calculate translateX based on alignment
  // In ASS, the position point is where text is anchored
  // We need to translate the element so the anchor point aligns correctly
  let translateX = '0%';

  // Horizontal alignment
  if (align === 1 || align === 4 || align === 7) {
    // Left alignment: no translation needed
    translateX = '0%';
  } else if (align === 2 || align === 5 || align === 8) {
    // Center alignment: translate left by 50%
    translateX = '-50%';
  } else if (align === 3 || align === 6 || align === 9) {
    // Right alignment: translate left by 100%
    translateX = '-100%';
  }

  // Combine transforms
  let transforms = [`translateX(${translateX})`];

  if (rotation !== 0) {
    transforms.push(`rotate(${rotation}deg)`);
  }

  return transforms.join(' ');
}

// Media Overlay Position Functions
function getMediaOverlayPositionX(type) {
  if (!videoPlayer.value?.$refs?.video) return 0;

  const videoElement = videoPlayer.value.$refs.video;
  const displayWidth = videoElement.offsetWidth;

  let posXPercent = 50; // default center
  if (type === 'logo' && props.renderOptions.logoOptions) {
    posXPercent = props.renderOptions.logoOptions.posX;
  } else if (type === 'image' && props.renderOptions.imageOptions) {
    posXPercent = props.renderOptions.imageOptions.posX;
  } else if (type === 'fixedText' && props.renderOptions.fixedTextOptions) {
    posXPercent = props.renderOptions.fixedTextOptions.posX;
  }

  // Convert percentage to pixel position
  return displayWidth * posXPercent / 100;
}

function getMediaOverlayPositionY(type) {
  if (!videoPlayer.value?.$refs?.video) return 0;

  const videoElement = videoPlayer.value.$refs.video;
  const displayHeight = videoElement.offsetHeight;

  let posYPercent = 50; // default center
  if (type === 'logo' && props.renderOptions.logoOptions) {
    posYPercent = props.renderOptions.logoOptions.posY;
  } else if (type === 'image' && props.renderOptions.imageOptions) {
    posYPercent = props.renderOptions.imageOptions.posY;
  } else if (type === 'fixedText' && props.renderOptions.fixedTextOptions) {
    posYPercent = props.renderOptions.fixedTextOptions.posY;
  }

  // Convert percentage to pixel position
  return displayHeight * posYPercent / 100;
}

function getEffectiveFixedTextFontSize() {
  if (!props.renderOptions.fixedTextOptions?.fontSize) return 24;

  // Scale font size to display size similar to subtitle scaling
  if (videoPlayer.value?.$refs?.video) {
    const assRes = getAssResolution();
    const displayScale = videoPlayer.value.$refs.video.offsetHeight / assRes.height;
    return props.renderOptions.fixedTextOptions.fontSize * displayScale;
  }

  return props.renderOptions.fixedTextOptions.fontSize / 2; // Fallback
}

function getFilePreviewUrl(filePath) {
  if (!filePath) return '';
  return `file://${filePath}`;
}

// Multiple Overlays Style Functions
function getLogoOverlayStyle(logo, logoIndex) {
  if (!videoPlayer.value?.$refs?.video) {
    return { display: 'none' };
  }

  const videoElement = videoPlayer.value.$refs.video;
  const displayWidth = videoElement.offsetWidth;
  const displayHeight = videoElement.offsetHeight;

  // Get position from logo options
  const posX = (logo.options.posX || 50) / 100 * displayWidth;
  const posY = (logo.options.posY || 50) / 100 * displayHeight;

  // Calculate scale based on display size (similar to backend logic)
  const baseLogoSize = displayWidth * 0.2; // 20% of display width
  const finalLogoSize = baseLogoSize * (logo.options.scale || 100) / 100;

  return {
    position: 'absolute',
    top: `${posY}px`,
    left: `${posX}px`,
    transform: `translate(-50%, -50%) rotate(${logo.options.rotation || 0}deg)`,
    opacity: (logo.options.opacity || 100) / 100,
    width: `${finalLogoSize}px`,
    height: 'auto',
    maxWidth: `${finalLogoSize}px`,
    maxHeight: `${finalLogoSize}px`,
    objectFit: 'contain',
    pointerEvents: 'none',
    zIndex: 10 + logoIndex
  };
}

function getImageOverlayStyle(image, imageIndex) {
  if (!videoPlayer.value?.$refs?.video) {
    return { display: 'none' };
  }

  const videoElement = videoPlayer.value.$refs.video;
  const displayWidth = videoElement.offsetWidth;
  const displayHeight = videoElement.offsetHeight;

  // Get position from image options
  const posX = (image.options.posX || 50) / 100 * displayWidth;
  const posY = (image.options.posY || 50) / 100 * displayHeight;

  // Calculate scale based on display size (similar to backend logic)
  const baseImageSize = displayWidth * 0.25; // 25% of display width
  const finalImageSize = baseImageSize * (image.options.scale || 100) / 100;

  return {
    position: 'absolute',
    top: `${posY}px`,
    left: `${posX}px`,
    transform: `translate(-50%, -50%) rotate(${image.options.rotation || 0}deg)`,
    opacity: (image.options.opacity || 100) / 100,
    width: `${finalImageSize}px`,
    height: 'auto',
    maxWidth: `${finalImageSize}px`,
    maxHeight: `${finalImageSize}px`,
    objectFit: 'contain',
    pointerEvents: 'none',
    zIndex: 20 + imageIndex
  };
}

function getTextOverlayStyle(text, textIndex) {
  if (!videoPlayer.value?.$refs?.video) {
    return { display: 'none' };
  }

  const videoElement = videoPlayer.value.$refs.video;
  const displayWidth = videoElement.offsetWidth;
  const displayHeight = videoElement.offsetHeight;

  // Get position from text options
  const posX = (text.options.posX || 50) / 100 * displayWidth;
  const posY = (text.options.posY || 50) / 100 * displayHeight;

  // Calculate font size based on display size
  const baseFontSize = text.options.fontSize * 2;
  const assRes = getAssResolution();
  const displayScale = videoElement.offsetHeight / assRes.height;
  const effectiveFontSize = baseFontSize * displayScale;

  return {
    position: 'absolute',
    top: `${posY}px`,
    left: `${posX}px`,
    transform: `translate(-50%, -50%) rotate(${text.options.rotation || 0}deg)`,
    opacity: (text.options.opacity || 100) / 100,
    fontSize: `${effectiveFontSize}px`,
    fontFamily: text.options.fontFamily || 'Arial',
    color: text.options.color || '#ffffff',
    backgroundColor: text.options.backgroundColor === 'transparent' ? 'transparent' : text.options.backgroundColor,
    fontWeight: text.options.bold ? 'bold' : 'normal',
    textAlign: text.options.align || 'center',
    padding: `${text.options.paddingY * 100 || 8}px ${text.options.paddingX * 15 || 8}px`,
    // paddingY: text.options.paddingY,
    // paddingX: text.options.paddingX,
    borderRadius: '4px',
    textShadow: text.options.borderColor === 'transparent' ? 'none' : `
      -1px -1px 0 ${text.options.borderColor},
      1px -1px 0 ${text.options.borderColor},
      -1px  1px 0 ${text.options.borderColor},
      1px  1px 0 ${text.options.borderColor}
    `,
    pointerEvents: 'none',
    zIndex: 30 + textIndex,
    whiteSpace: 'nowrap',
    maxWidth: '90%',
    wordWrap: 'break-word'
  };
}

// Media Overlay Helper Functions
function getMediaOverlayAtPoint(x, y) {
  if (!videoPlayer.value?.$refs?.video) return null;

  const tolerance = 20; // pixels

  // Check logo
  if (props.renderOptions.showLogo && props.renderOptions.logoFile) {
    const logoX = getMediaOverlayPositionX('logo');
    const logoY = getMediaOverlayPositionY('logo');
    if (Math.abs(x - logoX) < tolerance && Math.abs(y - logoY) < tolerance) {
      return 'logo';
    }
  }

  // Check image
  if (props.renderOptions.showImage && props.renderOptions.imageFile) {
    const imageX = getMediaOverlayPositionX('image');
    const imageY = getMediaOverlayPositionY('image');
    if (Math.abs(x - imageX) < tolerance && Math.abs(y - imageY) < tolerance) {
      return 'image';
    }
  }

  // Check fixed text
  if (props.renderOptions.showFixedText && props.renderOptions.fixedTextOptions.text) {
    const textX = getMediaOverlayPositionX('fixedText');
    const textY = getMediaOverlayPositionY('fixedText');
    if (Math.abs(x - textX) < tolerance && Math.abs(y - textY) < tolerance) {
      return 'fixedText';
    }
  }

  return null;
}

function startMediaOverlayDrag(x, y) {
  if (!selectedMediaOverlay.value) return;

  isDraggingMedia.value = true;
  dragStartMediaPos.value = { x, y };

  // Store current options for reference
  if (selectedMediaOverlay.value === 'logo') {
    dragStartMediaOptions.value = { ...props.renderOptions.logoOptions };
  } else if (selectedMediaOverlay.value === 'image') {
    dragStartMediaOptions.value = { ...props.renderOptions.imageOptions };
  } else if (selectedMediaOverlay.value === 'fixedText') {
    dragStartMediaOptions.value = { ...props.renderOptions.fixedTextOptions };
  }
}

function continueMediaOverlayDrag(x, y) {
  if (!isDraggingMedia.value || !selectedMediaOverlay.value || !videoPlayer.value?.$refs?.video) return;

  const videoElement = videoPlayer.value.$refs.video;
  const deltaX = x - dragStartMediaPos.value.x;
  const deltaY = y - dragStartMediaPos.value.y;

  // Convert pixel delta to percentage delta
  const deltaXPercent = (deltaX / videoElement.offsetWidth) * 100;
  const deltaYPercent = (deltaY / videoElement.offsetHeight) * 100;

  let targetOptions;
  if (selectedMediaOverlay.value === 'logo') {
    targetOptions = props.renderOptions.logoOptions;
  } else if (selectedMediaOverlay.value === 'image') {
    targetOptions = props.renderOptions.imageOptions;
  } else if (selectedMediaOverlay.value === 'fixedText') {
    targetOptions = props.renderOptions.fixedTextOptions;
  }

  if (targetOptions && dragStartMediaOptions.value) {
    // Update position with bounds checking
    targetOptions.posX = Math.max(0, Math.min(100, dragStartMediaOptions.value.posX + deltaXPercent));
    targetOptions.posY = Math.max(0, Math.min(100, dragStartMediaOptions.value.posY + deltaYPercent));
  }
}

function stopMediaOverlayDrag() {
  isDraggingMedia.value = false;
  dragStartMediaPos.value = { x: 0, y: 0 };
  dragStartMediaOptions.value = null;
}

// Drawing functions
function setDrawingMode(mode) {
  drawingMode.value = mode;
  if (mode !== 'select') {
    selectedArea.value = null;
    redrawCanvas();
  }
}

function handleMouseDown(e) {
  if (!drawingCanvas.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  if (drawingMode.value === 'select') {
    // Check if clicking on resize handle first
    const handle = getResizeHandle(x, y);
    if (handle && selectedArea.value) {
      startResize(handle, x, y);
      return;
    }

    // Check if clicking inside an existing area
    const area = getAreaAtPoint(x, y);
    if (area) {
      selectedArea.value = area;
      startDrag(x, y);
      redrawCanvas();
      return;
    }
  } else if (drawingMode.value === 'media') {
    // Handle media overlay dragging
    const mediaOverlay = getMediaOverlayAtPoint(x, y);
    if (mediaOverlay) {
      selectedMediaOverlay.value = mediaOverlay;
      startMediaOverlayDrag(x, y);
      return;
    } else {
      selectedArea.value = null;
      redrawCanvas();
    }
  } else {
    // Start drawing new area
    startDrawing(x, y);
  }
}

function handleMouseMove(e) {
  if (!drawingCanvas.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // Update cursor based on context
  updateCursor(x, y);

  if (isDrawing.value) {
    continueDrawing(x, y);
  } else if (isResizing.value) {
    continueResize(x, y);
  } else if (isDragging.value) {
    continueDrag(x, y);
  } else if (isDraggingMedia.value) {
    continueMediaOverlayDrag(x, y);
  }
}

function handleMouseUp() {
  if (isDrawing.value) {
    stopDrawing();
  } else if (isResizing.value) {
    stopResize();
  } else if (isDragging.value) {
    stopDrag();
  } else if (isDraggingMedia.value) {
    stopMediaOverlayDrag();
  }
}

function handleMouseLeave() {
  handleMouseUp();
}

function handleCanvasClick(e) {
  // Handle single clicks for selection
  if (drawingMode.value === 'select' && !isDrawing.value && !isResizing.value && !isDragging.value) {
    const rect = drawingCanvas.value.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const area = getAreaAtPoint(x, y);
    selectedArea.value = area;
    redrawCanvas();
  }

  // Handle media overlay selection
  if (drawingMode.value === 'media' && !isDraggingMedia.value) {
    const rect = drawingCanvas.value.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const mediaOverlay = getMediaOverlayAtPoint(x, y);
    selectedMediaOverlay.value = mediaOverlay;

    if (mediaOverlay) {
      message.info(`Selected ${mediaOverlay} overlay. Drag to move.`);
    } else {
      selectedMediaOverlay.value = null;
    }
  }
}

function startDrawing(x, y) {
  isDrawing.value = true;
  currentDrawing.value = {
    type: drawingMode.value,
    x,
    y,
    width: 0,
    height: 0,
    timeStart: localTimeRange.start,
    timeEnd: localTimeRange.end
  };
  drawRectangle();
}

function continueDrawing(x, y) {
  if (!currentDrawing.value) return;

  currentDrawing.value.width = x - currentDrawing.value.x;
  currentDrawing.value.height = y - currentDrawing.value.y;
  drawRectangle();
}

function stopDrawing() {
  if (!isDrawing.value || !currentDrawing.value) return;

  isDrawing.value = false;

  // Only add if rectangle has meaningful size
  if (Math.abs(currentDrawing.value.width) > 10 && Math.abs(currentDrawing.value.height) > 10) {
    // Normalize negative dimensions
    if (currentDrawing.value.width < 0) {
      currentDrawing.value.x += currentDrawing.value.width;
      currentDrawing.value.width = Math.abs(currentDrawing.value.width);
    }
    if (currentDrawing.value.height < 0) {
      currentDrawing.value.y += currentDrawing.value.height;
      currentDrawing.value.height = Math.abs(currentDrawing.value.height);
    }

    blurAreas.value.push({ ...currentDrawing.value });
    drawingHistory.value.push([...blurAreas.value]);

    console.log('Added drawing area:', currentDrawing.value);
    console.log('Video coordinates:', getVideoCoords(currentDrawing.value));

    emitBlurAreasUpdate();
  }

  currentDrawing.value = null;
  redrawCanvas();
}

// Resize functions
let resizeStartPos = { x: 0, y: 0 };
let resizeStartArea = null;

function startResize(handle, x, y) {
  isResizing.value = true;
  resizeHandle.value = handle;
  resizeStartPos = { x, y };
  resizeStartArea = { ...selectedArea.value };
}

function continueResize(x, y) {
  if (!selectedArea.value || !resizeHandle.value) return;

  const deltaX = x - resizeStartPos.x;
  const deltaY = y - resizeStartPos.y;
  const area = selectedArea.value;

  switch (resizeHandle.value) {
    case 'nw': // Top-left
      area.x = resizeStartArea.x + deltaX;
      area.y = resizeStartArea.y + deltaY;
      area.width = resizeStartArea.width - deltaX;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 'ne': // Top-right
      area.y = resizeStartArea.y + deltaY;
      area.width = resizeStartArea.width + deltaX;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 'sw': // Bottom-left
      area.x = resizeStartArea.x + deltaX;
      area.width = resizeStartArea.width - deltaX;
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'se': // Bottom-right
      area.width = resizeStartArea.width + deltaX;
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'n': // Top
      area.y = resizeStartArea.y + deltaY;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 's': // Bottom
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'w': // Left
      area.x = resizeStartArea.x + deltaX;
      area.width = resizeStartArea.width - deltaX;
      break;
    case 'e': // Right
      area.width = resizeStartArea.width + deltaX;
      break;
  }

  // Ensure minimum size
  if (area.width < 10) area.width = 10;
  if (area.height < 10) area.height = 10;

  redrawCanvas();
  emitBlurAreasUpdate();
}

function stopResize() {
  isResizing.value = false;
  resizeHandle.value = null;
}

// Drag functions
let dragStartPos = { x: 0, y: 0 };
let dragStartArea = null;

function startDrag(x, y) {
  isDragging.value = true;
  dragStartPos = { x, y };
  dragStartArea = { ...selectedArea.value };
}

function continueDrag(x, y) {
  if (!selectedArea.value) return;

  const deltaX = x - dragStartPos.x;
  const deltaY = y - dragStartPos.y;

  selectedArea.value.x = dragStartArea.x + deltaX;
  selectedArea.value.y = dragStartArea.y + deltaY;

  redrawCanvas();
  emitBlurAreasUpdate();
}

function stopDrag() {
  isDragging.value = false;
}

// Helper functions
function getAreaAtPoint(x, y) {
  for (let i = blurAreas.value.length - 1; i >= 0; i--) {
    const area = blurAreas.value[i];
    if (x >= area.x && x <= area.x + area.width &&
      y >= area.y && y <= area.y + area.height) {
      return area;
    }
  }
  return null;
}

function getResizeHandle(x, y) {
  if (!selectedArea.value) return null;

  const area = selectedArea.value;
  const handleSize = 8;

  // Corner handles
  if (isNearPoint(x, y, area.x, area.y, handleSize)) return 'nw';
  if (isNearPoint(x, y, area.x + area.width, area.y, handleSize)) return 'ne';
  if (isNearPoint(x, y, area.x, area.y + area.height, handleSize)) return 'sw';
  if (isNearPoint(x, y, area.x + area.width, area.y + area.height, handleSize)) return 'se';

  // Edge handles
  if (isNearPoint(x, y, area.x + area.width / 2, area.y, handleSize)) return 'n';
  if (isNearPoint(x, y, area.x + area.width / 2, area.y + area.height, handleSize)) return 's';
  if (isNearPoint(x, y, area.x, area.y + area.height / 2, handleSize)) return 'w';
  if (isNearPoint(x, y, area.x + area.width, area.y + area.height / 2, handleSize)) return 'e';

  return null;
}

function isNearPoint(x, y, targetX, targetY, threshold) {
  return Math.abs(x - targetX) <= threshold && Math.abs(y - targetY) <= threshold;
}

function updateCursor(x, y) {
  if (!drawingCanvas.value) return;

  let cursor = 'default';

  if (drawingMode.value === 'select') {
    const handle = getResizeHandle(x, y);
    if (handle) {
      const cursors = {
        'nw': 'nw-resize', 'ne': 'ne-resize',
        'sw': 'sw-resize', 'se': 'se-resize',
        'n': 'n-resize', 's': 's-resize',
        'w': 'w-resize', 'e': 'e-resize'
      };
      cursor = cursors[handle];
    } else if (getAreaAtPoint(x, y)) {
      cursor = 'move';
    }
  } else {
    cursor = 'crosshair';
  }

  drawingCanvas.value.style.cursor = cursor;
}

function drawRectangle() {
  if (!drawingCanvas.value || !currentDrawing.value) return;

  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');

  // Clear and redraw all areas
  redrawCanvas();

  // Draw current rectangle being drawn
  ctx.strokeStyle = getDrawingColor(currentDrawing.value.type);
  ctx.fillStyle = getDrawingColor(currentDrawing.value.type, 0.3);
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);

  ctx.fillRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
  ctx.strokeRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
}

function redrawCanvas() {
  if (!drawingCanvas.value) return;

  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Draw existing blur areas
  blurAreas.value.forEach((area, index) => {
    const isSelected = selectedArea.value === area;

    ctx.strokeStyle = getDrawingColor(area.type);
    ctx.fillStyle = getDrawingColor(area.type, 0.3);
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.setLineDash(isSelected ? [3, 3] : []);

    ctx.fillRect(area.x, area.y, area.width, area.height);
    ctx.strokeRect(area.x, area.y, area.width, area.height);

    // Add label
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.fillText(
      `${area.type} ${index + 1}`,
      area.x + 5,
      area.y + 15
    );

    // Draw resize handles for selected area
    if (isSelected && drawingMode.value === 'select') {
      drawResizeHandles(ctx, area);
    }
  });

  // Draw media overlay indicators
  if (drawingMode.value === 'media') {
    drawMediaOverlayIndicators(ctx);
  }
}

function drawResizeHandles(ctx, area) {
  const handleSize = 6;
  const handles = [
    { x: area.x, y: area.y }, // nw
    { x: area.x + area.width, y: area.y }, // ne
    { x: area.x, y: area.y + area.height }, // sw
    { x: area.x + area.width, y: area.y + area.height }, // se
    { x: area.x + area.width / 2, y: area.y }, // n
    { x: area.x + area.width / 2, y: area.y + area.height }, // s
    { x: area.x, y: area.y + area.height / 2 }, // w
    { x: area.x + area.width, y: area.y + area.height / 2 } // e
  ];

  ctx.fillStyle = '#1890ff';
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.setLineDash([]);

  handles.forEach(handle => {
    ctx.fillRect(
      handle.x - handleSize / 2,
      handle.y - handleSize / 2,
      handleSize,
      handleSize
    );
    ctx.strokeRect(
      handle.x - handleSize / 2,
      handle.y - handleSize / 2,
      handleSize,
      handleSize
    );
  });
}

function getDrawingColor(type, alpha = 1) {
  const colors = {
    blur: `rgba(255, 255, 0, ${alpha})`, // Yellow
    delogo: `rgba(255, 0, 0, ${alpha})`, // Red
    subtitle: `rgba(0, 255, 0, ${alpha})` // Green
  };
  return colors[type] || `rgba(255, 255, 255, ${alpha})`;
}

function drawMediaOverlayIndicators(ctx) {
  const indicatorSize = 20;
  const selectedSize = 24;

  // Draw logo indicator
  if (props.renderOptions.showLogo && props.renderOptions.logoFile) {
    const x = getMediaOverlayPositionX('logo');
    const y = getMediaOverlayPositionY('logo');
    const isSelected = selectedMediaOverlay.value === 'logo';
    const size = isSelected ? selectedSize : indicatorSize;

    ctx.strokeStyle = isSelected ? '#1890ff' : '#ff6b35';
    ctx.fillStyle = isSelected ? 'rgba(24, 144, 255, 0.3)' : 'rgba(255, 107, 53, 0.3)';
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.setLineDash(isSelected ? [3, 3] : []);

    ctx.fillRect(x - size/2, y - size/2, size, size);
    ctx.strokeRect(x - size/2, y - size/2, size, size);

    // Label
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.fillText('LOGO', x - 15, y - size/2 - 5);
  }

  // Draw image indicator
  if (props.renderOptions.showImage && props.renderOptions.imageFile) {
    const x = getMediaOverlayPositionX('image');
    const y = getMediaOverlayPositionY('image');
    const isSelected = selectedMediaOverlay.value === 'image';
    const size = isSelected ? selectedSize : indicatorSize;

    ctx.strokeStyle = isSelected ? '#1890ff' : '#52c41a';
    ctx.fillStyle = isSelected ? 'rgba(24, 144, 255, 0.3)' : 'rgba(82, 196, 26, 0.3)';
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.setLineDash(isSelected ? [3, 3] : []);

    ctx.fillRect(x - size/2, y - size/2, size, size);
    ctx.strokeRect(x - size/2, y - size/2, size, size);

    // Label
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.fillText('IMG', x - 12, y - size/2 - 5);
  }

  // Draw fixed text indicator
  if (props.renderOptions.showFixedText && props.renderOptions.fixedTextOptions.text) {
    const x = getMediaOverlayPositionX('fixedText');
    const y = getMediaOverlayPositionY('fixedText');
    const isSelected = selectedMediaOverlay.value === 'fixedText';
    const size = isSelected ? selectedSize : indicatorSize;

    ctx.strokeStyle = isSelected ? '#1890ff' : '#722ed1';
    ctx.fillStyle = isSelected ? 'rgba(24, 144, 255, 0.3)' : 'rgba(114, 46, 209, 0.3)';
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.setLineDash(isSelected ? [3, 3] : []);

    ctx.fillRect(x - size/2, y - size/2, size, size);
    ctx.strokeRect(x - size/2, y - size/2, size, size);

    // Label
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.fillText('TEXT', x - 15, y - size/2 - 5);
  }

  // Reset line dash
  ctx.setLineDash([]);
}

function clearDrawings() {
  blurAreas.value = [];
  drawingHistory.value = [];
  selectedArea.value = null;
  redrawCanvas();
  emitBlurAreasUpdate();
}

function undoLastDrawing() {
  if (blurAreas.value.length > 0) {
    blurAreas.value.pop();
    redrawCanvas();
    emitBlurAreasUpdate();
  }
}

function removeBlurArea(index) {
  blurAreas.value.splice(index, 1);
  redrawCanvas();
  emitBlurAreasUpdate();
}

function setFullMinize() {
  isMinimize.value = !isMinimize.value;

  // Wait for DOM update then refresh video player dimensions
  // nextTick(() => {
  //     onVideoLoaded();
  // });
}

function splitTextIntoChunks(text, maxWords) {
  if (!text) return [text];

  const words = text.trim().split(/\s+/);
  if (words.length <= maxWords) return [text];

  const chunks = [];
  for (let i = 0; i < words.length; i += maxWords) {
    const chunk = words.slice(i, i + maxWords).join(' ');
    chunks.push(chunk);
  }

  // Đảm bảo không có chunk nào trống
  return chunks.filter(chunk => chunk.trim().length > 0);
}

function getPreviewText() {
  const text = ttsStore.currentSrtList?.items[0]?.translatedText ||
    ttsStore.currentSrtList?.items[0]?.text ||
    'Sample Subtitle Text';
  // subInLine
  return splitTextIntoChunks(text, currentVoiceSettings.value.subInLine)[0];
}


function startTimeUpdate(index){
  blurAreas.value[index].timeStart = props.currentTime
  emitBlurAreasUpdate()
}
function endTimeUpdate(index){
  blurAreas.value[index].timeEnd = props.currentTime
  emitBlurAreasUpdate()
}

function resetTimeUpdate(index){
  blurAreas.value[index].timeStart = 0
  blurAreas.value[index].timeEnd = props.videoDuration
  emitBlurAreasUpdate()
}

</script>

<style scoped>

.video-container {
  position: relative;
  margin-bottom: 16px;
  border-radius: 4px;
  overflow: hidden;

}

.preview-video {
  width: 100%;
  height: auto;
  background: #000;
}

.text-preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 9;
}

.text-preview-content {
  max-width: 90%;
  word-wrap: break-word;
  text-align: center;
}

.drawing-canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.drawing-controls {
  margin-bottom: 16px;
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #2c2828;
}

.selected-area-info {
  margin-top: 12px;
}

.video-info-debug {
  margin-bottom: 16px;
  padding: 8px;
  /* background: #f5f5f5; */
  border-radius: 4px;
  font-size: 12px;
}

.video-info-debug h4 {
  margin-bottom: 8px;
  color: #1890ff;
  font-size: 14px;
}

.blur-areas-list {
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.time-controls {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.time-controls h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

.subtitle-preview {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 9;
}

.subtitle-preview-content {
  max-width: 90%;
  word-wrap: break-word;
  text-align: center;
}


</style>
