const { app, BrowserWindow, ipcMain, Notification } = require('electron');
const path = require('path');
require('./global');
require('dotenv').config();

global.ROOT_DIR = path.join(__dirname, '../');
global.STATIC_DIR = app.isPackaged
  ? path.join(process.resourcesPath, 'app.asar.unpacked', 'static/')
  : path.join(app.getAppPath(), 'static/');


const startServer = require('./server');
const { stopProcess } = require('./utils');
const Database = require('./db');
const { registerSummaryHandlers, cleanupSummaryServices } = require('./ipc/summary-handlers');

const { setFfmpegPaths } = require('./ffmpeg-config');
const { setupIpcHandlers } = require('./ipc/ipcHandler');
if (app) {
  setFfmpegPaths(process.resourcesPath, app.isPackaged);
}
const isDev = process.env.NODE_ENV === 'development';
const isWin = process.platform === 'win32';

// Auto reload main process trong dev
if (isDev) {
  try {
    require('electron-reload')(__dirname, {
      electron: require.resolve('electron/cli.js'),
      hardResetMethod: 'exit',
    });
  } catch (err) {
    console.log('electron-reload not available:', err.message);
  }
}

async function initDb() {
  const dbPath = C.path.join(app?.getPath('userData'), 'database_v1.sqlite');
  S.db = new Database(dbPath);
  await S.db.initializeTables();
  F.l('Tables initialized');

  // Register summary handlers after database is ready
  registerSummaryHandlers();
}

let mainWindow = null;
// Create main window
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    autoHideMenuBar: true,
    icon: path.join(app.getAppPath(), './public/icon.png'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true,
      contextIsolation: true,
      webSecurity: false,
    },
  });

  // In development mode, load from vite dev server
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173');
    // mainWindow.loadFile(path.join(app.getAppPath(), 'dist/index.html'));
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the built app
    mainWindow.loadFile(path.join(app.getAppPath(), 'dist/index.html'));
  }

  // Handle window close
  mainWindow.on('close', async (event) => {
    console.log('Main window is closing...');

    // If this is the last window, trigger app quit
    if (BrowserWindow.getAllWindows().length === 1) {
      event.preventDefault();
      app.quit();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    console.log('Main window closed');
  });
  S.mainWindow = mainWindow;
  return mainWindow;
}

// Initialize app
app.whenReady().then(async () => {
  const mainWindow = createWindow();
  await initDb();
  // Start Express server
  startServer(mainWindow);

  // Handle app activation
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });

  // IPC handlers
  setupIpcHandlers(mainWindow);

  // electron version
  console.log('Electron version:', process.versions.electron);
  F.initSocket();
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Cleanup before quit
app.on('before-quit', async (event) => {
  console.log('App is about to quit, cleaning up...');

  // Prevent immediate quit to allow cleanup
  event.preventDefault();

  try {
    // Stop all active processes
    await stopProcess();

    // Cleanup summary services
    await cleanupSummaryServices();

    // Close database connections
    if (global.db) {
      await global.db.close();
    }

    // Kill any remaining child processes
    if (global.activeProcesses) {
      for (const pid of global.activeProcesses) {
        try {
          process.kill(pid, 'SIGTERM');
        } catch (e) {
          console.log(`Process ${pid} already terminated`);
        }
      }
    }

    console.log('Cleanup completed');
  } catch (error) {
    console.error('Error during cleanup:', error);
  }

  // Now actually quit
  app.exit(0);
});

// Handle app quit
app.on('will-quit', (event) => {
  console.log('App will quit');
});

ipcMain.handle('notification:send', async (_, title, message) => {
  if (mainWindow) {
    // Use Electron's built-in notification
    const notification = new Notification({
      title,
      body: message,
      icon: path.join(app.getAppPath(), './public/icon.png'),
    });

    notification.show();
  }
});

ipcMain.handle('taskbar:progress', async (_, progress) => {
  if (mainWindow && isWin) {
    mainWindow.setProgressBar(progress / 100);
  }
});

ipcMain.handle('taskbar:state', async (_, state) => {
  if (mainWindow && isWin) {
    if (state === 'normal') {
      mainWindow.setProgressBar(-1);
    }
  }
});

// Export the Express app for testing
// module.exports = { expressApp };

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle process termination signals
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, cleaning up...');
  app.quit();
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, cleaning up...');
  app.quit();
});

// Handle process exit
process.on('exit', (code) => {
  console.log(`Process exiting with code: ${code}`);
});
