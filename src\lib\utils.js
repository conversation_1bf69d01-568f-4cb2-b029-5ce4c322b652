import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Combine classNames with tailwind merge
 * @param inputs - CSS classes to combine
 * @returns - Merged classnames
 */
export function cn(...inputs) {
  return twMerge(clsx(inputs))
}

// Parse time string to seconds
export function parseTimeToSeconds(timeStr) {
  const [hours, minutes, secondsAndMs] = timeStr.split(':');
  const [seconds, milliseconds] = secondsAndMs.split(',');

  return parseInt(hours) * 3600 +
         parseInt(minutes) * 60 +
         parseInt(seconds) +
         parseInt(milliseconds) / 1000;
}

// Format seconds to time string
export function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
}
// export function frameToTime(frames, fps) {
//   const totalSeconds = frames / fps;
//   const hours = Math.floor(totalSeconds / 3600);
//   const minutes = Math.floor((totalSeconds % 3600) / 60);
//   const seconds = Math.floor(totalSeconds % 60);
//   return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
// }
// Parse SRT content
export function parseSRT(content) {
  const lines = content.split('\n');
  const items = [];
  let currentItem = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (!isNaN(parseInt(line)) && !currentItem) {
      // This is an index line, start a new item
      const id = parseInt(line);
      currentItem = { index: id,id, text: '' };
    } else if (line.includes('-->') && currentItem) {
      // This is a timestamp line
      const [start, end] = line.split('-->').map(t => t.trim());
      currentItem.startTime = parseTimeToSeconds(start);
      currentItem.endTime = parseTimeToSeconds(end);
      currentItem.start = start;
      currentItem.end = end;
    } else if (line === '' && currentItem && currentItem.text) {
      // Empty line after text, push the current item and reset
      items.push(currentItem);
      currentItem = null;
    } else if (currentItem) {
      // This is a text line
      if (currentItem.text) {
        currentItem.text += ' ' + line;
      } else {
        currentItem.text = line;
      }
    }
  }

  // Don't forget the last item if there's no empty line at the end
  if (currentItem && currentItem.text) {
    items.push(currentItem);
  }

  return items;
}

export function getVideoSrc(srtPath) {
  return srtPath.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
}

export const timeToSeconds = (timeString) => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + Number(milliseconds) / 1000;
};

export function frameToTime(frame, fps) {
    const timeInt = Math.floor(frame * 1000 / fps)
    const timeStruct = new Date(timeInt)
    return `${timeStruct.getUTCHours().toString().padStart(2, '0')}:${timeStruct.getUTCMinutes().toString().padStart(2, '0')}:${timeStruct.getUTCSeconds().toString().padStart(2, '0')}.${Math.floor(timeStruct.getUTCMilliseconds() / 10).toString().padStart(2, '0')}`
}
