
class TtsAbbreviations {
  constructor(knex) {
    this.knex = knex;
    this.abbreviationCache = null;
    this.cacheExpiry = null;
    this.cacheDuration = 5 * 60 * 1000; // 5 phút
  }
  async createTable() {
    return this.knex.schema.hasTable('tts_abbreviations').then(async (exists) => {
      if (!exists) {
        return this.knex.schema.createTable('tts_abbreviations', (table) => {
          table.increments('id').primary();
          table.string('pattern', 255).notNullable();
          table.text('replacement').notNullable();
          table.string('category', 50).nullable(); // 'measurement', 'currency', 'business', etc.
          table.boolean('is_active').defaultTo(true);
          table.integer('priority').defaultTo(0); // Thứ tự ưu tiên xử lý
          table.text('description').nullable();
          table.timestamps(true, true);

          // Index để tăng tốc query
          table.index('is_active');
          table.index('category');
          table.index('priority');
        }).then(() => {
          this.seed();
        });
      } else {
        // Table exists, check if we need to add new columns
        await this.migrateTable();
      }
    });
  }
  // Lấy danh sách abbreviations từ DB với cache
  async getAbbreviations() {
    const now = Date.now();
    
    // Kiểm tra cache
    if (this.abbreviationCache && this.cacheExpiry && now < this.cacheExpiry) {
      return this.abbreviationCache;
    }

    try {
      const abbreviations = await this.knex('tts_abbreviations')
        .select('pattern', 'replacement', 'category', 'priority')
        .where('is_active', true)
        .orderBy('priority', 'asc');

      // Chuyển đổi thành format phù hợp
      this.abbreviationCache = abbreviations.map(item => ({
        regex: new RegExp(item.pattern, 'gi'),
        replacement: item.replacement,
        category: item.category,
        priority: item.priority
      }));

      this.cacheExpiry = now + this.cacheDuration;
      return this.abbreviationCache;
    } catch (error) {
      console.error('Error fetching abbreviations:', error);
      // Fallback về cache cũ nếu có lỗi
      return this.abbreviationCache || [];
    }
  }

  // Xử lý replacement có hỗ trợ callback function
  processReplacement(match, replacement, ...args) {
    // Nếu replacement có chứa $1, $2... thì thay thế
    if (replacement.includes('$')) {
      return replacement.replace(/\$(\d+)/g, (_, index) => {
        const argIndex = parseInt(index) - 1;
        return args[argIndex] || '';
      });
    }
    return replacement;
  }

  // Hàm chính normalize text
  async normalizeTextForTTS(text) {
    const abbreviations = await this.getAbbreviations();
    let normalized = text;

    // Áp dụng tất cả các rules theo thứ tự priority
    for (const { regex, replacement } of abbreviations) {
      normalized = normalized.replace(regex, (...args) => {
        const match = args[0];
        const captureGroups = args.slice(1, -2); // Loại bỏ offset và input string
        
        return this.processReplacement(match, replacement, ...captureGroups);
      });
    }

    // Xử lý chữ cái đơn lẻ sau (nếu có function này)
    if (typeof this.normalizeSingleLetters === 'function') {
      normalized = this.normalizeSingleLetters(normalized);
    }

    return normalized;
  }

  // Thêm abbreviation mới
  async addAbbreviation(pattern, replacement, category = 'general', priority = 100, description = '') {
    try {
      const [id] = await this.knex('tts_abbreviations').insert({
        pattern,
        replacement,
        category,
        priority,
        description,
        is_active: true
      });

      // Clear cache để reload
      this.abbreviationCache = null;
      this.cacheExpiry = null;

      return id;
    } catch (error) {
      console.error('Error adding abbreviation:', error);
      throw error;
    }
  }

  // Cập nhật abbreviation
  async updateAbbreviation(id, updates) {
    try {
      const result = await this.knex('tts_abbreviations')
        .where('id', id)
        .update({
          ...updates,
          updated_at: new Date()
        });

      // Clear cache để reload
      this.abbreviationCache = null;
      this.cacheExpiry = null;

      return result;
    } catch (error) {
      console.error('Error updating abbreviation:', error);
      throw error;
    }
  }

  // Xóa abbreviation (soft delete)
  async deleteAbbreviation(id) {
    try {
      const result = await this.knex('tts_abbreviations')
        .where('id', id)
        .update({
          is_active: false,
          updated_at: new Date()
        });

      // Clear cache để reload
      this.abbreviationCache = null;
      this.cacheExpiry = null;

      return result;
    } catch (error) {
      console.error('Error deleting abbreviation:', error);
      throw error;
    }
  }

  // Lấy danh sách abbreviations theo category
  async getAbbreviationsByCategory(category) {
    try {
      return await this.knex('tts_abbreviations')
        .select('*')
        .where('category', category)
        .where('is_active', true)
        .orderBy('priority', 'asc');
    } catch (error) {
      console.error('Error fetching abbreviations by category:', error);
      throw error;
    }
  }

  // Clear cache thủ công
  clearCache() {
    this.abbreviationCache = null;
    this.cacheExpiry = null;
  }
  async migrateTable() {}

  async seed() {
    const abbreviationData = [
      // Đo lường
      {
        pattern: '\\b1m(\\d{1,2})\\b',
        replacement: '1 mét $1',
        category: 'measurement',
        priority: 1,
        description: 'Đo lường mét với số thập phân',
      },
      {
        pattern: '\\b(\\d)m(\\d{1,2})\\b',
        replacement: '$1 mét $2',
        category: 'measurement',
        priority: 2,
        description: 'Đo lường mét tổng quát',
      },
      {
        pattern: '\\b(\\d+)kg\\b',
        replacement: '$1 ki lô gam',
        category: 'measurement',
        priority: 3,
        description: 'Đơn vị kilogram',
      },
      {
        pattern: '\\b(\\d+)km\\b',
        replacement: '$1 ki lô mét',
        category: 'measurement',
        priority: 4,
        description: 'Đơn vị kilometer',
      },
      {
        pattern: '\\b(\\d+)cm\\b',
        replacement: '$1 xăng ti mét',
        category: 'measurement',
        priority: 5,
        description: 'Đơn vị centimeter',
      },
      {
        pattern: '\\b(\\d+)mm\\b',
        replacement: '$1 mi li mét',
        category: 'measurement',
        priority: 6,
        description: 'Đơn vị millimeter',
      },

      // Xưng hô
      {
        pattern: '(?:^|[^a-zA-ZÀ-ỹ])ông\\/bà(?:[^a-zA-ZÀ-ỹ]|$)',
        replacement: 'ông bà',
        category: 'honorific',
        priority: 10,
        description: 'Xưng hô ông/bà',
      },
      {
        pattern: '(?:^|[^a-zA-ZÀ-ỹ])anh\\/chị(?:[^a-zA-ZÀ-ỹ]|$)',
        replacement: 'anh chị',
        category: 'honorific',
        priority: 11,
        description: 'Xưng hô anh/chị',
      },

      // Địa danh
      {
        pattern: '\\bTP\\.HCM\\b',
        replacement: 'thành phố Hồ Chí Minh',
        category: 'location',
        priority: 20,
        description: 'Thành phố Hồ Chí Minh',
      },
      {
        pattern: '\\bHN\\b',
        replacement: 'Hà Nội',
        category: 'location',
        priority: 21,
        description: 'Hà Nội',
      },
      {
        pattern: '\\bTp\\.\\s*HCM\\b',
        replacement: 'thành phố Hồ Chí Minh',
        category: 'location',
        priority: 22,
        description: 'Thành phố HCM variant',
      },
      {
        pattern: '\\bHCM\\b',
        replacement: 'Hồ Chí Minh',
        category: 'location',
        priority: 23,
        description: 'Hồ Chí Minh',
      },
      {
        pattern: '\\bĐN\\b',
        replacement: 'Đà Nẵng',
        category: 'location',
        priority: 24,
        description: 'Đà Nẵng',
      },

      // Tiền tệ
      {
        pattern: '\\bVND\\b',
        replacement: 'đồng Việt Nam',
        category: 'currency',
        priority: 30,
        description: 'Đồng Việt Nam',
      },
      {
        pattern: '\\bUSD\\b',
        replacement: 'đô la Mỹ',
        category: 'currency',
        priority: 31,
        description: 'Đô la Mỹ',
      },
      {
        pattern: '\\bEUR\\b',
        replacement: 'ơ rô',
        category: 'currency',
        priority: 32,
        description: 'Euro',
      },
      {
        pattern: '\\bJPY\\b',
        replacement: 'yên Nhật',
        category: 'currency',
        priority: 33,
        description: 'Yên Nhật',
      },

      // Doanh nghiệp & Kinh doanh
      {
        pattern: '\\bCEO\\b',
        replacement: 'xì i ô',
        category: 'business',
        priority: 40,
        description: 'Chief Executive Officer',
      },
      {
        pattern: '\\bCTO\\b',
        replacement: 'xì tê ô',
        category: 'business',
        priority: 41,
        description: 'Chief Technology Officer',
      },
      {
        pattern: '\\bCFO\\b',
        replacement: 'xì ép ô',
        category: 'business',
        priority: 42,
        description: 'Chief Financial Officer',
      },
      {
        pattern: '\\bHR\\b',
        replacement: 'hát a',
        category: 'business',
        priority: 43,
        description: 'Human Resources',
      },
      {
        pattern: '\\bPR\\b',
        replacement: 'pê a',
        category: 'business',
        priority: 44,
        description: 'Public Relations',
      },
      {
        pattern: '\\bKPI\\b',
        replacement: 'ca pê ai',
        category: 'business',
        priority: 45,
        description: 'Key Performance Indicator',
      },
      {
        pattern: '\\bROI\\b',
        replacement: 'rô ai',
        category: 'business',
        priority: 46,
        description: 'Return on Investment',
      },
      {
        pattern: '\\bB2B\\b',
        replacement: 'bê tu bê',
        category: 'business',
        priority: 47,
        description: 'Business to Business',
      },
      {
        pattern: '\\bB2C\\b',
        replacement: 'bê tu xê',
        category: 'business',
        priority: 48,
        description: 'Business to Consumer',
      },
      {
        pattern: '\\bSEO\\b',
        replacement: 'ét i ô',
        category: 'business',
        priority: 49,
        description: 'Search Engine Optimization',
      },
      {
        pattern: '\\bSEM\\b',
        replacement: 'ét i mờ',
        category: 'business',
        priority: 50,
        description: 'Search Engine Marketing',
      },
      {
        pattern: '\\bCRM\\b',
        replacement: 'xì a mờ',
        category: 'business',
        priority: 51,
        description: 'Customer Relationship Management',
      },
      {
        pattern: '\\bERP\\b',
        replacement: 'i a pê',
        category: 'business',
        priority: 52,
        description: 'Enterprise Resource Planning',
      },

      // Giáo dục
      {
        pattern: '\\bGPA\\b',
        replacement: 'gi pê a',
        category: 'education',
        priority: 60,
        description: 'Grade Point Average',
      },
      {
        pattern: '\\bIELTS\\b',
        replacement: 'ai lờ tê ét',
        category: 'education',
        priority: 61,
        description: 'International English Language Testing System',
      },
      {
        pattern: '\\bTOEFL\\b',
        replacement: 'tô ép lờ',
        category: 'education',
        priority: 62,
        description: 'Test of English as a Foreign Language',
      },
      {
        pattern: '\\bTOEIC\\b',
        replacement: 'tô ích',
        category: 'education',
        priority: 63,
        description: 'Test of English for International Communication',
      },
      {
        pattern: '\\bPhD\\b',
        replacement: 'pê hát đê',
        category: 'education',
        priority: 64,
        description: 'Doctor of Philosophy',
      },
      {
        pattern: '\\bMBA\\b',
        replacement: 'mờ bê a',
        category: 'education',
        priority: 65,
        description: 'Master of Business Administration',
      },

      // Y tế
      {
        pattern: '\\bBMI\\b',
        replacement: 'bê mờ ai',
        category: 'medical',
        priority: 70,
        description: 'Body Mass Index',
      },
      {
        pattern: '\\bDNA\\b',
        replacement: 'đê nờ a',
        category: 'medical',
        priority: 71,
        description: 'Deoxyribonucleic Acid',
      },
      {
        pattern: '\\bRNA\\b',
        replacement: 'a nờ a',
        category: 'medical',
        priority: 72,
        description: 'Ribonucleic Acid',
      },
      {
        pattern: '\\bICU\\b',
        replacement: 'ai xì u',
        category: 'medical',
        priority: 73,
        description: 'Intensive Care Unit',
      },
      {
        pattern: '\\bER\\b',
        replacement: 'i a',
        category: 'medical',
        priority: 74,
        description: 'Emergency Room',
      },
      {
        pattern: '\\bECG\\b',
        replacement: 'i xì gờ',
        category: 'medical',
        priority: 75,
        description: 'Electrocardiogram',
      },
      {
        pattern: '\\bMRI\\b',
        replacement: 'mờ a ai',
        category: 'medical',
        priority: 76,
        description: 'Magnetic Resonance Imaging',
      },
      {
        pattern: '\\bCT\\b',
        replacement: 'xì tê',
        category: 'medical',
        priority: 77,
        description: 'Computed Tomography',
      },

      // Khác
      {
        pattern: '\\btivi\\b',
        replacement: 'ti vi',
        category: 'general',
        priority: 100,
        description: 'Television',
      },
      {
        pattern: '\\bsos\\b',
        replacement: 'ét ô ét',
        category: 'general',
        priority: 101,
        description: 'Save Our Souls',
      },
      {
        pattern: '\\bOK\\b',
        replacement: 'ô kê',
        category: 'general',
        priority: 102,
        description: 'Okay',
      },
      {
        pattern: '\\bFAQ\\b',
        replacement: 'ép a quy',
        category: 'general',
        priority: 103,
        description: 'Frequently Asked Questions',
      },
      {
        pattern: '\\bVs\\b',
        replacement: 'vơ sớt',
        category: 'general',
        priority: 104,
        description: 'Versus',
      },
      {
        pattern: '\\bETC\\b',
        replacement: 'ét tê xì',
        category: 'general',
        priority: 105,
        description: 'Et cetera',
      },
      {
        pattern: '\\bPS\\b',
        replacement: 'pê ét',
        category: 'general',
        priority: 106,
        description: 'Postscript',
      },
      {
        pattern: '\\bNB\\b',
        replacement: 'nờ bê',
        category: 'general',
        priority: 107,
        description: 'Nota Bene',
      },
      {
        pattern: '\\bFYI\\b',
        replacement: 'ép oai ai',
        category: 'general',
        priority: 108,
        description: 'For Your Information',
      },
      {
        pattern: '\\bASAP\\b',
        replacement: 'a ét a pê',
        category: 'general',
        priority: 109,
        description: 'As Soon As Possible',
      },
      {
        pattern: '\\bvideo\\b',
        replacement: 'vi đê ô',
        category: 'general',
        priority: 110,
        description: 'Video',
      },
      {
        pattern: '\\btu vi\\b',
        replacement: 'tu Vi',
        category: 'general',
        priority: 111,
        description: 'TTS',
      },
    ];
    return this.knex('tts_abbreviations').insert(abbreviationData);
  }
}

module.exports = TtsAbbreviations;
