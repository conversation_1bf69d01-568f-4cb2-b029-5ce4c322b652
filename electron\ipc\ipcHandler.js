const { app, ipcMain, dialog, shell, clipboard, session, Notification } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec, spawn } = require('child_process');

const https = require('https');
const http = require('http');
const crypto = require('crypto');

const store = require('../store');
const { getSpeakers, synthesizeService } = require('../services');
const {
  audioTTSDir,
  setCurrentDir,
  getCurrentDir,
  stopProcess,
  getActiveProcesses,
  convertVideoToWav,
  getAudioDuration,
} = require('../utils');
const adjustSpeed = require('../adjustSpeed');
const getSrtFWX = require('../getSrtFWX');
const { processVideoOcr, runOcrBatchScript, getTextFromFrameVideo } = require('../videoOcr');
const { processVideocrCli, testVideocrCli } = require('../videocrCli');
const { getVideoDuration, cutVideoSegment, splitVideoIntoParts } = require('../videoCutter');
const { renderVideoWithSrt } = require('../videoRenderer');
const { processSrtAndVideo } = require('../ffmpegProcessor');

const { processSRTAndRender } = require('../ffmpegUtils');
const { processVideoSimplified } = require('../utils/videoRendererSimplified');
const { demucs, checkFileHtdemucsFileExists } = require('../utils/demucs');
const { processVideoWithOptions } = require('../utils/processVideoWithOptions');
const { getVideoInfo, convertAll, getEncoder, createThumbail } = require('../ffmpegHandler');
const { runTests } = require('../playwright/test-extraction');
const SystemUtils = require('../utils/systemUtils');

const { ffmpegManager } = require('../ffmpeg-config');
const { getMediaFilesFromFolder, openVideo } = require('../utils/mediaFiles');
const { SimpleJSChineseOCR } = require('../ocr/simple_js_ocr');
const { detectPlatform, getVideoInfoWithService, downloadVideoWithService } = require('../downloaderService');
const douyinModule = require('../playwright/douyinGetVideoUrl');
const douyinService = require('../downloaderService/douyinService');

// Utility functions
function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const currentPath = fs.join(folderPath, file);
      if (fs.lstatSync(currentPath).isDirectory()) {
        deleteFolderRecursive(currentPath);
      } else {
        fs.unlinkSync(currentPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
}

// Dialog handlers
function setupDialogHandlers(mainWindow) {
  ipcMain.handle('showOpenDialogSync', async (event, options) => dialog.showOpenDialogSync(mainWindow, options));

  ipcMain.handle('showMessageBoxSync', async (event, options) => dialog.showMessageBoxSync(mainWindow, options));

  ipcMain.handle('showSaveDialogSync', async (event, options) => dialog.showSaveDialogSync(mainWindow, options));
}
// System handlers
function setupSystemHandlers(mainWindow) {
  const systemUtils = new SystemUtils();

  ipcMain.handle('process:platform', async (event) => process.platform);
  // ipcMain.handle("pkg:machineIdSync", async event => machineId.machineIdSync());
  ipcMain.handle(
    'dns:lookup',
    async (event, hostname) =>
      new Promise((resolve, reject) => {
        dns.lookup(hostname, (error, address, family) => {
          if (error) {
            reject(error);
          } else {
            resolve(address);
          }
        });
      }),
  );

  ipcMain.handle('close-app', () => {
    app.quit();
  });

  ipcMain.handle('app:app-path', async (event) => {
    const appRoot = process.env.APP_ROOT;
    return path.dirname(appRoot);
  });

  ipcMain.handle('app:app-product-mode', async (event) => !process.env.VITE_DEV_SERVER_URL);
  ipcMain.handle('app:child_process:executeCommand', (event, command) => systemUtils.executeCommand(command));
  ipcMain.handle('app:updateMainApp', async (event) => systemUtils.updateMainApp());
  ipcMain.handle('app:restartApp', async (event) => systemUtils.restartApp());
  ipcMain.handle('app:clear-cache-and-exit', async (event) => {
    await session.defaultSession.clearCache();
    app.quit();
  });

  ipcMain.handle('asar:extractAllAsar', async (event, asarPath, outputPath) =>
    systemUtils.extractAllAsar(asarPath, outputPath),
  );
  ipcMain.handle('asar:createPackageAsar', async (event, sourcePath, outputPath) =>
    systemUtils.createPackageAsar(sourcePath, outputPath),
  );
  ipcMain.handle('app:get-version-app', () => app.getVersion());
  ipcMain.handle('electron:clipboard:readText', () => clipboard.readText());
  ipcMain.handle('electron:clipboard:readImage', () => clipboard.readImage());
  ipcMain.handle('app:Buffer', (event, data, encoding = 'base64') => Buffer.from(data, encoding));
  ipcMain.handle('app:winreg:getRegistryValue', (event, keyPath, valueName) =>
    systemUtils.getRegistryValue(keyPath, valueName),
  );
  ipcMain.handle('app:winreg:setRegistryValue', (event, keyPath, valueName, value) =>
    systemUtils.setRegistryValue(keyPath, valueName, value),
  );
  ipcMain.handle('app:shell:openExternal', (event, url) => systemUtils.openExternal(url));
  ipcMain.handle('app:child_process:spawnCommand', (event, command, args) => systemUtils.spawnCommand(command, args));
  ipcMain.handle('app:shell:showItemInFolder', (event, filePath) => systemUtils.showItemInFolder(filePath));
}

function setupFileSystemHandlers(mainWindow) {
  ipcMain.handle('path:basename', async (event, filePath) => path.basename(filePath));
  ipcMain.handle('path:dirname', async (event, filePath) => path.dirname(filePath));
  ipcMain.handle('app:userData', async (event) => app.getAppPath('userData'));
  ipcMain.handle('app:static', async (event) => {
    return STATIC_DIR;
  });
  ipcMain.handle('path:join', async (event, ...paths) => path.join(...paths));
  ipcMain.handle('path:temp', async (event) => app.getPath('temp'));
  ipcMain.handle('fs:writeFileSync', async (event, filePath, data) => fs.writeFileSync(filePath, data));
  ipcMain.handle('fs:copyFileSync', async (event, input, output) => fs.copyFileSync(input, output));
  ipcMain.handle('fs:mkdirSync', async (event, dirPath) => fs.mkdirSync(dirPath));
  ipcMain.handle('fs:existsSync', async (event, filePath) => fs.existsSync(filePath));
  ipcMain.handle('fs:createReadStream', async (event, filePath) => fs.createReadStream(filePath));
  ipcMain.handle('fs:readFileSync', async (event, filePath, ...options) => fs.readFileSync(filePath, ...options));
  ipcMain.handle('fs:unlinkSync', async (event, filePath) => fs.unlinkSync(filePath));
  ipcMain.handle('fs:isFile', async (event, filePath) => fs.lstatSync(filePath).isFile());
  ipcMain.handle('fs:rmdirSync', async (event, dirPath) => fs.rmdirSync(dirPath));
  ipcMain.handle('crypto:hashMd5File', async (event, filePath) => {
    const fileBuffer = fs.readFileSync(filePath);
    return crypto.createHash('md5').update(fileBuffer).digest('hex');
  });
  ipcMain.handle('fs:deleteFolderRecursive', async (event, folderPath) => deleteFolderRecursive(folderPath));
}

// FFmpeg class

// FFmpeg handlers
function setupFFmpegHandlers(mainWindow) {
  ipcMain.handle('run-cmd-ffmpeg', async (event, command) => ffmpegManager.executeFFmpegCommand(command));

  ipcMain.handle('ffmpeg:ffmpegPath', async (event) => ffmpegManager.ffmpegPath);
  ipcMain.handle('ffmpeg:ffprobePath', async (event) => ffmpegManager.ffprobePath);
  ipcMain.handle('device:getEncoder', async (event) => getEncoder());
}

// Set up IPC handlers
function setupIpcHandlers(mainWindow) {
  // TTS API
  setupDialogHandlers(mainWindow);
  setupFileSystemHandlers(mainWindow);
  setupFFmpegHandlers(mainWindow);
  setupSystemHandlers(mainWindow);

  ipcMain.handle('database', async (event, operation, ...args) => {
    console.log('Database operation:', operation, 'with args:', args);

    const [tableName, methodName] = operation.split('.');
    const result = await S.db[tableName][methodName](...args);
    return result;
  });

  ipcMain.handle(
    'generate-tts',
    async (event, { text, speaker, typeEngine, audio_config, openaiConfig, mimimaxConfig }) => {
      try {
        const data = await synthesizeService(event, {
          text,
          speaker,
          typeEngine,
          audio_config,
          openaiConfig,
          mimimaxConfig,
        });
        return data;
      } catch (error) {
        console.error('Error generating TTS:', error);
        return {
          success: false,
          error: error.message,
        };
      }
    },
  );

  // Get speakers list
  ipcMain.handle('get-speakers', async (event, type) => {
    try {
      const data = getSpeakers(type);
      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error('Error getting speakers:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  // File system operations
  ipcMain.handle('save-file', async (event, { filePath, content }) => {
    try {
      await fs.promises.writeFile(filePath, content);
      return { success: true };
    } catch (error) {
      console.error('Error saving file:', error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle('read-file', async (event, { filePath }) => {
    try {
      const content = await fs.promises.readFile(filePath, 'utf8');
      console.log('Read file content:', content);
      return { success: true, content };
    } catch (error) {
      console.error('Error reading file:', error);
      return { success: false, error: error.message };
    }
  });

  // Dialog operations
  ipcMain.handle('open-file-dialog', async (event, options) => {
    try {
      const { canceled, filePaths } = await dialog.showOpenDialog(options);
      return { canceled, filePaths };
    } catch (error) {
      console.error('Error opening file dialog:', error);
      return { success: false, error: error.message };
    }
  });

  // Directory operations
  ipcMain.handle('read-directory', async (event, dirPath) => {
    try {
      const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
      const fileList = files
        .filter((file) => file.isFile())
        .map((file) => ({
          name: file.name,
          path: path.join(dirPath, file.name),
          isFile: true,
        }));

      return fileList;
    } catch (error) {
      console.error('Error reading directory:', error);
      return { success: false, error: error.message };
    }
  });

  // Read directory with subdirectories
  ipcMain.handle('read-directory-with-subdirs', async (event, dirPath) => {
    try {
      if (!fs.existsSync(dirPath)) {
        return { success: false, error: 'Directory does not exist' };
      }

      const items = await fs.promises.readdir(dirPath, { withFileTypes: true });
      const result = [];

      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        if (item.isDirectory()) {
          result.push({
            name: item.name,
            path: itemPath,
            isDirectory: true,
            isFile: false,
          });
        } else {
          result.push({
            name: item.name,
            path: itemPath,
            isDirectory: false,
            isFile: true,
          });
        }
      }

      return { success: true, items: result };
    } catch (error) {
      console.error('Error reading directory with subdirs:', error);
      return { success: false, error: error.message };
    }
  });

  // Read JSON file
  ipcMain.handle('read-json-file', async (event, filePath) => {
    try {
      if (!fs.existsSync(filePath)) {
        return { success: false, error: 'File does not exist' };
      }

      const content = await fs.promises.readFile(filePath, 'utf8');
      const jsonData = JSON.parse(content);
      return { success: true, data: jsonData };
    } catch (error) {
      console.error('Error reading JSON file:', error);
      return { success: false, error: error.message };
    }
  });

  // yt-dlp operations with fallback services
  ipcMain.handle('yt-dlp-get-info', async (event, url) => {
    try {
      // Try yt-dlp first (except for Douyin)
      const platform = detectPlatform(url);

      if (platform !== 'douyin') {
        try {
          const ytdlpResult = await new Promise((resolve) => {
            const ytdlp = spawn('yt-dlp', ['--dump-json', '--no-download', url]);

            let stdout = '';
            let stderr = '';

            ytdlp.stdout.on('data', (data) => {
              stdout += data.toString();
            });

            ytdlp.stderr.on('data', (data) => {
              stderr += data.toString();
            });

            ytdlp.on('close', (code) => {
              if (code === 0) {
                try {
                  const info = JSON.parse(stdout);
                  resolve({
                    success: true,
                    source: 'yt-dlp',
                    info: {
                      title: info.title,
                      duration: info.duration,
                      uploader: info.uploader,
                      upload_date: info.upload_date,
                      view_count: info.view_count,
                      thumbnail: info.thumbnail,
                      formats:
                        info.formats?.map((f) => ({
                          format_id: f.format_id,
                          ext: f.ext,
                          quality: f.quality,
                          filesize: f.filesize,
                          format_note: f.format_note,
                        })) || [],
                    },
                  });
                } catch (parseError) {
                  resolve({ success: false, error: 'Failed to parse video info' });
                }
              } else {
                resolve({ success: false, error: stderr || 'Failed to get video info' });
              }
            });
          });

          if (ytdlpResult.success) {
            return ytdlpResult;
          }
        } catch (ytdlpError) {
          console.log('yt-dlp failed, trying fallback service...');
        }
      }

      // Fallback to custom services
      try {
        let serviceInfo;
        try {
          console.log('🎯 Calling getVideoInfoWithService for URL:', url);
          serviceInfo = await getVideoInfoWithService(url);
          console.log('📋 getVideoInfoWithService result:', serviceInfo);
        } catch (serviceError) {
          console.error('❌ getVideoInfoWithService failed:', serviceError);
          // If service fails, continue with yt-dlp
          serviceInfo = null;
        }
        return {
          success: true,
          source: platform,
          info: {
            title: serviceInfo.title,
            duration: serviceInfo.duration,
            uploader: serviceInfo.uploader || 'Unknown',
            upload_date: serviceInfo.upload_date,
            view_count: serviceInfo.view_count,
            thumbnail: serviceInfo.thumbnail,
            formats:
              serviceInfo.formats?.map((f) => ({
                format_id: f.quality || f.type,
                ext: f.extension || f.ext,
                quality: f.quality,
                filesize: f.filesize,
                format_note: f.label || f.quality,
                download_url: f.url, // Store the direct download URL
              })) || [],
          },
        };
      } catch (serviceError) {
        return {
          success: false,
          error: `Both yt-dlp and ${platform} service failed: ${serviceError.message}`,
        };
      }
    } catch (error) {
      console.error('Error getting video info:', error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle('yt-dlp-download', async (event, { url, outputPath, format }) => {
    try {
      const platform = detectPlatform(url);

      // For Douyin, always use custom service
      if (platform === 'douyin') {
        event.sender.send('yt-dlp-progress', 'Using Douyin service for download...\n');

        try {
          console.log('🎯 Calling downloadVideoWithService for Douyin...');
          const result = await downloadVideoWithService(url, outputPath, format);
          console.log('✅ downloadVideoWithService result:', result);

          event.sender.send('yt-dlp-progress', `✅ Downloaded successfully using ${result.platform} service\n`);
          return { success: true, message: 'Download completed successfully', source: result.platform };
        } catch (serviceError) {
          console.error('❌ Douyin service error:', serviceError);
          event.sender.send('yt-dlp-progress', `❌ Douyin service failed: ${serviceError.message}\n`);
          return { success: false, error: `Douyin service failed: ${serviceError.message}` };
        }
      }

      // For other platforms, try yt-dlp first
      try {
        const ytdlpResult = await new Promise((resolve) => {
          const args = ['--output', `${outputPath}/%(title)s.%(ext)s`, url];

          if (format) {
            args.unshift('--format', format);
          }

          const ytdlp = spawn('yt-dlp', args);
          let stderr = '';

          ytdlp.stdout.on('data', (data) => {
            const output = data.toString();
            event.sender.send('yt-dlp-progress', output);
          });

          ytdlp.stderr.on('data', (data) => {
            stderr += data.toString();
            event.sender.send('yt-dlp-progress', data.toString());
          });

          ytdlp.on('close', (code) => {
            if (code === 0) {
              resolve({ success: true, message: 'Download completed successfully', source: 'yt-dlp' });
            } else {
              resolve({ success: false, error: stderr || 'Download failed' });
            }
          });
        });

        if (ytdlpResult.success) {
          return ytdlpResult;
        }
      } catch (ytdlpError) {
        console.log('yt-dlp failed, trying fallback service...');
      }

      // Fallback to custom services
      event.sender.send('yt-dlp-progress', `yt-dlp failed, trying ${platform} service...\n`);

      try {
        const result = await downloadVideoWithService(url, outputPath, format);
        event.sender.send('yt-dlp-progress', `✅ Downloaded successfully using ${result.platform} service\n`);
        return { success: true, message: 'Download completed successfully', source: result.platform };
      } catch (serviceError) {
        return {
          success: false,
          error: `Both yt-dlp and ${platform} service failed: ${serviceError.message}`,
        };
      }
    } catch (error) {
      console.error('Error downloading video:', error);
      return { success: false, error: error.message };
    }
  });

  // Get user's Videos directory
  ipcMain.handle('get-videos-dir', async (event) => {
    try {
      const videosPath = app.getPath('videos');
      return { success: true, path: videosPath };
    } catch (error) {
      console.error('Error getting videos directory:', error);
      return { success: false, error: error.message };
    }
  });

  // Douyin video info extractor
  ipcMain.handle('get-douyin-video-info', async (event, url) => {
    console.log('🎯 IPC Handler: get-douyin-video-info called with URL:', url);

    try {
      // Check if URL is valid
      if (!url || typeof url !== 'string') {
        throw new Error('Invalid URL provided');
      }

      // Try to require the module
      let getDouyinVideoUrl;
      try {
        getDouyinVideoUrl = douyinModule.getDouyinVideoUrl;

        if (!getDouyinVideoUrl || typeof getDouyinVideoUrl !== 'function') {
          throw new Error('getDouyinVideoUrl function not found in module');
        }
      } catch (requireError) {
        console.error('❌ Failed to require Douyin module:', requireError);
        throw new Error(`Failed to load Douyin extractor: ${requireError.message}`);
      }

      console.log('📡 Calling Douyin extractor...');
      const result = await getDouyinVideoUrl(url);

    //   console.log('📋 Douyin extractor result:', result);

      if (result && result.success) {
        const response = {
          success: true,
          videoUrl: result.videoUrl,
          fileName: result.fileName,
          title: result.fileName,
          author: result.author,
          duration: result.duration,
          cover: result.cover,
          formats: result.videoUrl
            ? [
                {
                  format_id: 'douyin-direct',
                  url: result.videoUrl,
                  ext: 'mp4',
                  quality: 'original',
                  filesize: null,
                },
              ]
            : [],
          currentUrl: result.currentUrl,
          aweme_id: result.aweme_id,
        };

        // console.log('✅ IPC Handler returning success:', response);
        return response;
      } else {
        const errorMsg = result?.error || 'Failed to extract Douyin video info';
        console.log('❌ IPC Handler returning error:', errorMsg);
        return {
          success: false,
          error: errorMsg,
        };
      }
    } catch (error) {
      console.error('💥 IPC Handler exception:', error);
      return {
        success: false,
        error: `Douyin service failed: ${error.message}`,
      };
    }
  });

  // Original Douyin service (using douyinService.js)
  ipcMain.handle('douyin-download', async (event, options) => {
    // console.log('🎯 IPC Handler: douyin-download called with options:', options);

    try {
      const { url, outputPath } = options;

      if (!url || !outputPath) {
        throw new Error('Missing required parameters: url, outputPath');
      }

      // Try to require the douyinService module
      let fetchDouyinVideoInfo;
      try {
        fetchDouyinVideoInfo = douyinService.fetchDouyinVideoInfo;

        if (!fetchDouyinVideoInfo || typeof fetchDouyinVideoInfo !== 'function') {
          throw new Error('fetchDouyinVideoInfo function not found in douyinService');
        }
      } catch (requireError) {
        console.error('❌ Failed to require douyinService:', requireError);
        throw new Error(`Failed to load douyinService: ${requireError.message}`);
      }

      console.log('📡 Calling douyinService...');
      const result = await fetchDouyinVideoInfo(url);

      console.log('📋 DouyinService result:', result);

      if (result && result.success) {
        console.log('✅ DouyinService success:', result);
        return {
          success: true,
          data: result,
        };
      } else {
        const errorMsg = result?.error || 'DouyinService failed to extract video info';
        console.log('❌ DouyinService returning error:', errorMsg);
        return {
          success: false,
          error: errorMsg,
        };
      }
    } catch (error) {
      console.error('💥 DouyinService exception:', error);
      return {
        success: false,
        error: `DouyinService failed: ${error.message}`,
      };
    }
  });

  // Direct download for Douyin videos (bypass yt-dlp)
  ipcMain.handle('direct-download', async (event, options) => {
    // console.log('🎬 IPC Handler: direct-download called with options:', options);

    try {
      const { url, outputPath, filename, headers } = options;

      if (!url || !outputPath || !filename) {
        throw new Error('Missing required parameters: url, outputPath, filename');
      }

      const fullPath = path.join(outputPath, filename);
      console.log('📁 Download path:', fullPath);

      // Create write stream
      const fileStream = fs.createWriteStream(fullPath);
      // convert size to MB
      const sizeValue = (size) => {
        const i = Math.floor(Math.log(size) / Math.log(1024));
        return (size / Math.pow(1024, i)).toFixed(2) * 1 + ' ' + ['B', 'KB', 'MB', 'GB', 'TB'][i];
      };
      return new Promise((resolve, reject) => {
        const client = url.startsWith('https') ? https : http;

        // Send initial progress
        event.sender.send('yt-dlp-progress', '🚀 Connecting to Douyin server...\n');

        const request = client.get(
          url,
          {
            headers: headers || {},
          },
          (response) => {
            console.log('📡 Response status:', response.statusCode);
            // console.log('📋 Response headers:', response.headers);

            if (response.statusCode !== 200) {
              event.sender.send('yt-dlp-progress', `❌ HTTP Error: ${response.statusCode} ${response.statusMessage}\n`);
              reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
              return;
            }

            const totalSize = parseInt(response.headers['content-length'] || '0');
            let downloadedSize = 0;

            // Send connection success message
            if (totalSize > 0) {
              event.sender.send('yt-dlp-progress', `📊 File size: ${sizeValue(totalSize)}\n`);
              event.sender.send('yt-dlp-progress', '⬇️ Starting download...\n');
            } else {
              event.sender.send('yt-dlp-progress', '⬇️ Starting download (unknown size)...\n');
            }

            let lastProgressTime = 0;

            response.on('data', (chunk) => {
              downloadedSize += chunk.length;

              // Throttle progress updates (every 500ms)
              const now = Date.now();
              if (now - lastProgressTime > 500 || downloadedSize === totalSize) {
                if (totalSize > 0) {
                  const progress = Math.round((downloadedSize / totalSize) * 100);
                  event.sender.send(
                    'yt-dlp-progress',
                    `📊 Progress: ${progress}% (${sizeValue(downloadedSize)} of ${sizeValue(totalSize)})\n`,
                  );
                } else {
                  event.sender.send('yt-dlp-progress', `📊 Downloaded: ${sizeValue(downloadedSize)}\n`);
                }
                lastProgressTime = now;
              }
            });

            response.pipe(fileStream);

            fileStream.on('finish', () => {
              fileStream.close();
            //   console.log('✅ Download completed:', fullPath);

              // Send completion message
              event.sender.send('yt-dlp-progress', '✅ Download completed successfully!\n');
              event.sender.send('yt-dlp-progress', `📁 Saved to: ${fullPath}\n`);
              event.sender.send('yt-dlp-progress', `📊 Total size: ${sizeValue(downloadedSize)}\n`);

              resolve({
                success: true,
                filePath: fullPath,
                fileSize: downloadedSize,
              });
            });

            fileStream.on('error', (error) => {
              event.sender.send('yt-dlp-progress', `❌ File write error: ${error.message}\n`);
              fs.unlink(fullPath, () => {}); // Delete partial file
              reject(error);
            });
          },
        );

        request.on('error', (error) => {
          event.sender.send('yt-dlp-progress', `❌ Network error: ${error.message}\n`);
          reject(error);
        });

        request.setTimeout(30000, () => {
          event.sender.send('yt-dlp-progress', '❌ Download timeout (30s)\n');
          request.destroy();
          reject(new Error('Download timeout'));
        });
      });
    } catch (error) {
      console.error('💥 Direct download error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle('save-file-dialog', async (event, options) => {
    try {
      const { canceled, filePath } = await dialog.showSaveDialog(options);
      return { canceled, filePath };
    } catch (error) {
      console.error('Error opening save dialog:', error);
      return { success: false, error: error.message };
    }
  });

  // Concatenate audio files
  ipcMain.handle('concatenate-audio', async (event, { inputFiles, outputFile, timings }) => {
    try {
      // Check if FFmpeg is installed
      const ffmpegPath = process.platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';

      // Create a command to concatenate the files
      let command = `${ffmpegPath} -y -f concat -safe 0 -i "${inputFiles}" -c copy "${outputFile}"`;

      // Execute the command
      return new Promise((resolve) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error(`FFmpeg error: ${error.message}`);
            resolve({ success: false, error: error.message });
            return;
          }

          resolve({ success: true, outputFile });
        });
      });
    } catch (error) {
      console.error('Error concatenating audio files:', error);
      return { success: false, error: error.message };
    }
  });

  // Run FFmpeg command
  ipcMain.handle('run-ffmpeg', async (event, fileListPath, outputPath) => {
    try {
      // Check if FFmpeg is installed
      const ffmpegPath = process.platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';

      // Create a command to concatenate the files
      let command = `${ffmpegPath} -y -f concat -safe 0 -i "${fileListPath}" -c copy "${outputPath}"`;

      // Execute the command
      return new Promise((resolve) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error(`FFmpeg error: ${error.message}`);
            resolve({ success: false, error: error.message });
            return;
          }

          resolve({ success: true, outputPath });
        });
      });
    } catch (error) {
      console.error('Error running FFmpeg:', error);
      return { success: false, error: error.message };
    }
  });

  // Create temporary directory
  ipcMain.handle('create-temp-dir', async (event, { dirName = 'temp_audio', filePath }) => {
    try {
      if (filePath) {
        const baseName = path.basename(filePath, path.extname(filePath));
        const setDirPath = path.dirname(filePath);
        const tempDir = baseName + '_audio';
        const outputDirectory = path.join(setDirPath, tempDir);

        // Ensure output directory exists
        if (!fs.existsSync(outputDirectory)) {
          fs.mkdirSync(outputDirectory, { recursive: true });
        }
        return { success: true, path: outputDirectory, dirPath: setDirPath };
      }
      // check if path

      const tempDir = path.join(app.getPath('temp'), dirName);

      // Create directory if it doesn't exist
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      return { success: true, path: tempDir };
    } catch (error) {
      console.error('Error creating temporary directory:', error);
      return { success: false, error: error.message };
    }
  });

  // Download file from URL
  ipcMain.handle('download-file', async (event, url, filePath) => {
    try {
      return new Promise((resolve) => {
        const protocol = url.startsWith('https') ? https : http;

        const file = fs.createWriteStream(filePath);
        protocol
          .get(url, (response) => {
            response.pipe(file);

            file.on('finish', () => {
              file.close();
              resolve({ success: true, filePath });
            });
          })
          .on('error', (error) => {
            fs.unlink(filePath, () => {}); // Delete the file on error
            console.error(`Error downloading file: ${error.message}`);
            resolve({ success: false, error: error.message });
          });
      });
    } catch (error) {
      console.error('Error downloading file:', error);
      return { success: false, error: error.message };
    }
  });

  // Write file
  ipcMain.handle('write-file', async (event, filePath, content) => {
    try {
      // Check if content is a string or binary data
      const isString = typeof content === 'string';

      // Write the file with appropriate encoding
      if (isString) {
        await fs.promises.writeFile(filePath, content, 'utf8');
      } else {
        // For binary data (Buffer or Uint8Array)
        await fs.promises.writeFile(filePath, Buffer.from(content));
      }

      return { success: true, filePath };
    } catch (error) {
      console.error('Error writing file:', error);
      return { success: false, error: error.message };
    }
  });

  // Open file with default application
  ipcMain.handle('open-file', async (event, filePath) => {
    try {
      await shell.openPath(filePath);
      return { success: true };
    } catch (error) {
      console.error('Error opening file:', error);
      return { success: false, error: error.message };
    }
  });

  // Get app paths
  ipcMain.handle('get-app-path', async (event, name) => {
    try {
      const appPath = app.getPath(name);
      return { success: true, path: appPath };
    } catch (error) {
      console.error('Error getting app path:', error);
      return { success: false, error: error.message };
    }
  });

  // open folder
  ipcMain.handle('open-folder', async (event, folderPath) => {
    try {
      await shell.openPath(folderPath || audioTTSDir || app.getPath('downloads'));
      return { success: true };
    } catch (error) {
      console.error('Error opening folder:', error);
      return { success: false, error: error.message };
    }
  });

  // Get audio duration
  ipcMain.handle('audio:getDuration', async (event, filePath) => {
    try {
      const duration = await getAudioDuration(filePath);
      return duration;
    } catch (error) {
      console.error('Error getting audio duration:', error);
      return 0; // Return 0 as fallback
    }
  });
  ipcMain.handle('set-session', async (event, session) => {
    try {
      store.set('session', session);
      return { success: true };
    } catch (error) {
      console.error('Error setting session:', error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle('get-session', async (event) => {
    try {
      const session = store.get('session');
      return { success: true, session };
    } catch (error) {
      console.error('Error getting session:', error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle('set-current-dir', async (event, dirPath) => {
    try {
      setCurrentDir(dirPath);
      return { success: true };
    } catch (error) {
      console.error('Error setting current dir:', error);
      return { success: false, error: error.message };
    }
  });
  ipcMain.handle('get-current-dir', async (event) => {
    try {
      const currentDir = getCurrentDir();
      return { success: true, currentDir };
    } catch (error) {
      console.error('Error getting current dir:', error);
      return { success: false, error: error.message };
    }
  });

  // Video speed adjustment
  ipcMain.handle('adjust-speed', async (event, options) => {
    try {
      const { input, speedPercent, videoBitrateKbps, audioBitrateKbps } = options;

      if (!input || !fs.existsSync(input)) {
        return {
          success: false,
          error: 'Input file does not exist',
        };
      }

      // Process the video and get the process ID
      const result = await adjustSpeed(event, {
        input,
        speedPercent,
        videoBitrateKbps,
        audioBitrateKbps,
      });

      return result;
    } catch (error) {
      console.error('Error adjusting video speed:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
        stack: error.stack,
        details: error.toString(),
      };
    }
  });

  // Stop a running process
  ipcMain.handle('stop-process', async (event, processId) => {
    try {
      if (!processId) {
        return { success: false, error: 'Process ID is required' };
      }

      const result = stopProcess(processId);
      return result;
    } catch (error) {
      console.error('Error stopping process:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Get all active processes
  ipcMain.handle('get-active-processes', async (event) => {
    try {
      const processes = getActiveProcesses();
      return { success: true, processes };
    } catch (error) {
      console.error('Error getting active processes:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Convert video to WAV
  ipcMain.handle('convert-video-to-wav', async (event, { input, outputDir }) => {
    try {
      if (!input) {
        return { success: false, error: 'Input file is required' };
      }

      const result = await convertVideoToWav(input, outputDir);
      return result;
    } catch (error) {
      console.error('Error converting video to WAV:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });
  ipcMain.handle('demucs', async (event, { fileInput }) => {
    try {
      if (!fileInput) {
        return { success: false, error: 'Input file is required' };
      }

      const result = await demucs(event, { fileInput });
      return result;
    } catch (error) {
      console.error('Error running demucs:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Process audio with Whisper to generate SRT
  ipcMain.handle('process-audio-whisper', async (event, { fileInput, language, model, model_dir, outputFile }) => {
    try {
      if (!fileInput) {
        return { success: false, error: 'Input file is required' };
      }

      if (!model_dir) {
        return { success: false, error: 'Model directory is required' };
      }
      const result = await getSrtFWX(event, {
        fileInput,
        language: language || 'zh',
        model: model || 'large-v3',
        model_dir,
        outputFile,
      });

      // Get the SRT file path (same name as input but with .srt extension)
      const { dir, name } = path.parse(fileInput);
      const srtFilePath = path.join(dir, `${name}.srt`);

      return {
        success: true,
        processId: result.processId,
        srtFilePath,
        result,
      };
    } catch (error) {
      console.error('Error processing audio with Whisper:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Process video with OCR to generate SRT
  ipcMain.handle(
    'process-video-ocr',
    async (event, { videoPath, outputPath, lang, frameRate, crop, pythonPath, scriptPath }) => {
      try {
        if (!videoPath) {
          return { success: false, error: 'Video path is required' };
        }

        const result = await processVideoOcr(event, {
          videoPath,
          outputPath,
          lang: lang || 'zh',
          frameRate: frameRate || 3,
          crop,
          pythonPath,
          scriptPath,
        });

        return result;
      } catch (error) {
        console.error('Error processing video with OCR:', error);
        return {
          success: false,
          error: error.message || 'Unknown error',
        };
      }
    },
  );

  // Run OCR batch script
  ipcMain.handle('run-ocr-batch', async (event, { videoPath, batchPath }) => {
    try {
      if (!videoPath) {
        return { success: false, error: 'Video path is required' };
      }

      if (!batchPath) {
        return { success: false, error: 'Batch script path is required' };
      }

      const result = await runOcrBatchScript({
        videoPath,
        batchPath,
      });

      return result;
    } catch (error) {
      console.error('Error running OCR batch script:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Process video with VideoCR CLI
  ipcMain.handle('process-videocr-cli', async (event, options) => {
    try {
      if (!options.videoPath) {
        return { success: false, error: 'Video path is required' };
      }

      if (!options.videocrPath) {
        return { success: false, error: 'VideoCR CLI path is required' };
      }

      const result = await processVideocrCli(event, options);
      return result;
    } catch (error) {
      console.error('Error processing video with VideoCR CLI:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Test VideoCR CLI
  ipcMain.handle('test-videocr-cli', async (event, options) => {
    try {
      if (!options.videoPath) {
        return { success: false, error: 'Video path is required' };
      }

      if (!options.videocrPath) {
        return { success: false, error: 'VideoCR CLI path is required' };
      }

      const result = await testVideocrCli(event, options);
      return result;
    } catch (error) {
      console.error('Error testing VideoCR CLI:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Get video duration
  ipcMain.handle('get-video-duration', async (event, videoPath) => {
    try {
      if (!videoPath) {
        return { success: false, error: 'Video path is required' };
      }

      const duration = await getVideoDuration(videoPath);

      return {
        success: true,
        duration,
      };
    } catch (error) {
      console.error('Error getting video duration:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Cut video segment
  ipcMain.handle('cut-video-segment', async (event, options) => {
    try {
      if (!options.inputPath) {
        return { success: false, error: 'Input path is required' };
      }

      const result = await cutVideoSegment(event, options);

      return result;
    } catch (error) {
      console.error('Error cutting video segment:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Split video into parts
  ipcMain.handle('split-video-into-parts', async (event, options) => {
    try {
      if (!options.inputPath) {
        return { success: false, error: 'Input path is required' };
      }

      if (!options.parts || options.parts < 2) {
        return { success: false, error: 'Number of parts must be at least 2' };
      }

      const result = await splitVideoIntoParts(options);

      return result;
    } catch (error) {
      console.error('Error splitting video into parts:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  // Render video with SRT array
  ipcMain.handle('render-video-with-srt', async (event, options) => {
    try {
      if (!options.videoPath) {
        return { success: false, error: 'Video path is required' };
      }

      if (!options.srtArray || !Array.isArray(options.srtArray) || options.srtArray.length === 0) {
        return { success: false, error: 'SRT array is required and must not be empty' };
      }

      // Set up event listener for progress updates
      ipcMain.on('render-video-progress', (progressEvent, data) => {
        event.sender.send('render-video-progress', data);
      });

      const result = await renderVideoWithSrt(event, options);

      // Remove event listener
      ipcMain.removeAllListeners('render-video-progress');

      return result;
    } catch (error) {
      console.error('Error rendering video with SRT:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  ipcMain.handle('process-srt-and-video', async (event, options) => {
    try {
      const result = await processSrtAndVideo(options);
      return { success: true, result };
    } catch (error) {
      console.error('Error processing SRT and video:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });
  ipcMain.handle('process-srt-and-render', async (event, srtArray, videoPath, options = {}) => {
    try {
      const result = await processSRTAndRender(srtArray, videoPath, options);
      return { success: true, outputPath: result };
    } catch (error) {
      console.error('Error processing SRT and rendering video:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });
  ipcMain.handle('process-video-simplified', async (event, srtArray, videoPath, options = {}) => {
    try {
      if (!srtArray || !Array.isArray(srtArray) || srtArray.length === 0) {
        return { success: false, error: 'SRT array is required and must not be empty' };
      }
      const videoInput = videoPath.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
      const outputDir = path.dirname(videoInput);
      const outputVideo = path.join(outputDir, 'output.mp4');

      const result = await processVideoSimplified(event, videoInput, srtArray, outputDir, outputVideo);
      return { success: true, outputPath: result };
    } catch (error) {
      console.error('Error processing SRT and rendering video:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  ipcMain.handle('process-video-with-options', processVideoWithOptions);
  ipcMain.handle('get-text-from-frame-video', getTextFromFrameVideo);
  ipcMain.handle('get-video-info', getVideoInfo);
  ipcMain.handle('create-thumb', createThumbail);

  ipcMain.handle('convert-video-to-video', async (event, { input }) => {
    try {
      if (!input) {
        return { success: false, error: 'Input file is required' };
      }
      const output = input.replace('.mp4', '_converted.mp4');

      const result = await convertAll(event, input, output);
      // if success to remove input and rename output to input
      if (result.success) {
        fs.unlinkSync(input);
        fs.renameSync(output, input);
      }
      return result;
    } catch (error) {
      console.error('Error converting video to video:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  ipcMain.handle('check-file-htdemucs-file-exists', async (event, { fileInput }) => {
    try {
      if (!fileInput) {
        return { success: false, error: 'Input file is required' };
      }

      const result = await checkFileHtdemucsFileExists(event, { fileInput });
      return result;
    } catch (error) {
      console.error('Error checking file htdemucs file exists:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });

  ipcMain.handle('request-any', async (event, { cmd, args }) => {
    try {
      const foundCmd = F[`h_${cmd}`];
      if (!foundCmd) {
        // runWebService
        let url = 'https://44xw.com/a/149/148289/';
        const res = await runTests(event);
        return { success: true, result: res };
      }
      const result = await foundCmd(event, ...args);
      if (result && result.error) {
        return { success: false, error: result.error };
      }
      if (result && result.success === false) {
        return { success: false, error: result.error || 'Unknown error' };
      }
    } catch (error) {
      console.error('Error getting video info:', error);
      return { success: false, error: error.message || 'Unknown error' };
    }
  });

  // Font management
  ipcMain.handle('get-fonts', async (event) => {
    try {
      const fontsDir = path.join(STATIC_DIR, 'fonts');

      // console.log('Fonts directory:', fontsDir);

      if (!fs.existsSync(fontsDir)) {
        console.log('Fonts directory does not exist, returning system fonts only');
        return [
          { name: 'Arial', path: 'Arial', isSystem: true },
          { name: 'Helvetica', path: 'Helvetica', isSystem: true },
          { name: 'Times New Roman', path: 'Times New Roman', isSystem: true },
          { name: 'Courier New', path: 'Courier New', isSystem: true },
          { name: 'Verdana', path: 'Verdana', isSystem: true },
        ];
      }

      const fontFiles = fs
        .readdirSync(fontsDir)
        .filter(
          (file) =>
            file.toLowerCase().endsWith('.ttf') ||
            file.toLowerCase().endsWith('.otf') ||
            file.toLowerCase().endsWith('.woff') ||
            file.toLowerCase().endsWith('.woff2'),
        );

      const customFonts = fontFiles.map((file) => {
        const name = path
          .basename(file, path.extname(file))
          .replace(/[-_]/g, ' ')
          .replace(/\b\w/g, (l) => l.toUpperCase());

        return {
          name: name,
          path: file,
          isSystem: false,
          fullPath: path.join(fontsDir, file),
        };
      });

      // Combine system fonts with custom fonts
      const systemFonts = [
        { name: 'Arial', path: 'Arial', isSystem: true },
        { name: 'Helvetica', path: 'Helvetica', isSystem: true },
        { name: 'Times New Roman', path: 'Times New Roman', isSystem: true },
        { name: 'Courier New', path: 'Courier New', isSystem: true },
        { name: 'Verdana', path: 'Verdana', isSystem: true },
      ];

      const allFonts = [...systemFonts, ...customFonts];
      // console.log('Found fonts:', allFonts.length);

      return allFonts;
    } catch (error) {
      console.error('Error getting fonts:', error);
      return [
        { name: 'Arial', path: 'Arial', isSystem: true },
        { name: 'Helvetica', path: 'Helvetica', isSystem: true },
      ];
    }
  });

  ipcMain.handle('get-media-files-from-folder', async (event, folderPath) => {
    return await getMediaFilesFromFolder(folderPath);
  });
  ipcMain.handle('VideoPlayer:OpenVideo', async (e, ...args) => {
    try {
      return await openVideo(args[0]);
    } catch (error) {
      console.error(error.message);
      return null;
    }
  });

  ipcMain.handle('ocr:image', async (event, { type = 'local', imagePath }) => {
    try {
      if (!imagePath) {
        return { success: false, error: 'Image path is required' };
      }
      if (type === 'cloud') {
        return await optimizedChineseOCR(imagePath);
      }
      if (type === 'local') {
        const ocr = new SimpleJSChineseOCR();
        return await ocr.recognizeText(imagePath);
      }
    } catch (error) {
      console.error('Error processing image with OCR:', error);
      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  });
}

module.exports = { setupIpcHandlers };
