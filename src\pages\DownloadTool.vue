<template>
  <div class="absolute w-full p-4 h-full overflow-y-auto download-tool-container">
    <div class="header">
      <div class="header-top">
        <a-button
          type="text"
          @click="$router.back()"
          class="back-button"
          size="small"
        >
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          Back
        </a-button>
      </div>
      <h1 class="title">Video Download Tool</h1>
      <p class="subtitle">Download videos from YouTube, TikTok, Douyin, Instagram, Facebook and other platforms</p>
      <div class="supported-platforms">
        <span class="platforms-label">Supported:</span>
        <span class="platform-tag">YouTube</span>
        <span class="platform-tag">TikTok</span>
        <span class="platform-tag">Douyin</span>
        <span class="platform-tag">Instagram</span>
        <span class="platform-tag">Facebook</span>
        <span class="platform-tag">+ More</span>
      </div>
    </div>

    <div class="main-content">
      <div class="content-grid">
        <!-- Left Column -->
        <div class="left-column">
          <!-- URL Input Section -->
          <div class="section">
            <h2>Video URL</h2>
            <div class="url-input-group">
              <a-textarea
                v-model:value="videoUrl"
                placeholder="Paste video URLs here - one URL per line for batch download (YouTube, TikTok, Instagram, Douyin, etc.)"
                size="large"
                :disabled="isLoading"
                @keyup.enter="handleEnterKey"
                @input="onUrlChange"
                :auto-size="{ minRows: 3, maxRows: 8 }"
              />
              <div class="button-group">
                <a-button
                  v-if="!isBatchMode"
                  type="primary"
                  size="large"
                  :loading="isGettingInfo"
                  @click="getVideoInfo"
                  :disabled="!videoUrl.trim() || (!extractedUrl && !videoUrl.includes('http'))"
                >
                  Get Info
                </a-button>
                <a-button
                  v-if="!isBatchMode"
                  type="primary"
                  size="large"
                  :loading="isGettingDouyinInfo"
                  @click="getDouyinInfoAndDownload"
                  :disabled="!videoUrl.trim()"
                  class="douyin-quick-btn"
                >
                  <template #icon>
                    <DownloadOutlined />
                  </template>
                  Quick Download
                </a-button>
                <a-button
                  v-if="isBatchMode"
                  type="primary"
                  size="large"
                  :loading="isBatchDownloading"
                  @click="startBatchDownload"
                  :disabled="!downloadPath || urlList.length === 0"
                  class="batch-download-btn"
                >
                  <template #icon>
                    <DownloadOutlined />
                  </template>
                  {{ isBatchDownloading ? `Downloading ${currentBatchIndex}/${totalBatchCount}...` : `Batch Download (${urlList.length} videos)` }}
                </a-button>
              </div>
            </div>

            <!-- Douyin Options -->
            <div v-if="isDouyinUrl" class="douyin-options">
              <a-checkbox v-model:checked="useDouyinExtractor" @change="onDouyinExtractorChange">
                <span class="checkbox-label">
                  🚀 Use NEW Douyin Direct Extractor
                  <span class="checkbox-note">(Experimental - Faster, better quality)</span>
                </span>
              </a-checkbox>
              <div v-if="useDouyinExtractor" class="douyin-note">
                <span class="note-icon">ℹ️</span>
                <span class="note-text">
                  NEW FEATURE: This will extract video URL directly from Douyin API, bypassing yt-dlp for better quality and speed. Leave unchecked to use the original method.
                </span>
              </div>
            </div>
            <!-- Batch Mode Info -->
            <div v-if="isBatchMode" class="batch-info">
              <div class="batch-header">
                <span class="batch-label">📋 Batch Mode:</span>
                <span class="batch-count">{{ urlList.length }} videos detected</span>
              </div>
              <div class="batch-urls">
                <div
                  v-for="(urlItem, index) in urlList.slice(0, 5)"
                  :key="index"
                  class="batch-url-item"
                >
                  <span class="batch-index">{{ index + 1 }}.</span>
                  <span class="platform-badge" :class="urlItem.platform">{{ urlItem.platform.toUpperCase() }}</span>
                  <span class="batch-url">{{ urlItem.extracted.substring(0, 60) }}{{ urlItem.extracted.length > 60 ? '...' : '' }}</span>
                </div>
                <div v-if="urlList.length > 5" class="batch-more">
                  ... and {{ urlList.length - 5 }} more videos
                </div>
              </div>
            </div>

            <!-- Single Mode Info -->
            <div v-if="!isBatchMode">
              <div v-if="extractedUrl && extractedUrl !== videoUrl" class="extracted-url">
                <span class="extracted-label">Extracted URL:</span>
                <span class="extracted-link">{{ extractedUrl }}</span>
              </div>
              <div v-if="detectedPlatform" class="platform-info">
                <span class="platform-label">Detected Platform:</span>
                <span class="platform-badge" :class="detectedPlatform">{{ detectedPlatform.toUpperCase() }}</span>
                <span v-if="detectedPlatform === 'douyin'" class="platform-note">
                  (Will use Douyin service - yt-dlp not supported)
                </span>
              </div>
            </div>
          </div>

          <!-- Video Info Section -->
          <div v-if="videoInfo" class="section">
            <h2>Video Information</h2>
            <div class="video-info-card">
              <div class="video-thumbnail" v-if="videoInfo.thumbnail">
                <img :src="videoInfo.thumbnail" alt="Video thumbnail" />
              </div>
              <div class="video-details">
                <h3>{{ videoInfo.title }}</h3>
                <div class="info-row" v-if="videoSource">
                  <span class="label">Source:</span>
                  <span class="source-badge" :class="videoSource">{{ videoSource.toUpperCase() }}</span>
                </div>
                <div class="info-row">
                  <span class="label">Uploader:</span>
                  <span>{{ videoInfo.uploader }}</span>
                </div>
                <div class="info-row" v-if="videoInfo.duration">
                  <span class="label">Duration:</span>
                  <span>{{ formatDuration(videoInfo.duration) }}</span>
                </div>
                <div class="info-row" v-if="videoInfo.view_count">
                  <span class="label">Views:</span>
                  <span>{{ formatNumber(videoInfo.view_count) }}</span>
                </div>
                <div class="info-row" v-if="videoInfo.upload_date">
                  <span class="label">Upload Date:</span>
                  <span>{{ formatDate(videoInfo.upload_date) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Display -->
          <div v-if="error" class="section">
            <a-alert
              :message="error"
              type="error"
              show-icon
              closable
              @close="error = ''"
            />
          </div>
        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Format Selection -->
          <div v-if="videoInfo && videoInfo.formats.length > 0" class="section">
            <h2>Quality Selection</h2>
            <a-select
              v-model:value="selectedFormat"
              placeholder="Best Quality (Auto)"
              size="large"
              style="width: 100%"
            >
              <a-select-option value="">Best Quality (Auto)</a-select-option>
              <a-select-option
                v-for="format in filteredFormats"
                :key="format.format_id || format.quality"
                :value="format.format_id || format.quality"
              >
                {{ getFormatDisplayText(format) }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Download Path Selection -->
          <div class="section">
            <h2>Download Location</h2>
            <div class="path-input-group">
              <a-input
                v-model:value="downloadPath"
                placeholder="Select download folder"
                size="large"
                readonly
              />
              <a-button
                size="large"
                @click="selectDownloadPath"
                :disabled="isLoading"
              >
                Browse
              </a-button>
              <a-button
                size="large"
                @click="resetDownloadPath"
                :disabled="isLoading"
                title="Reset to default Videos folder"
              >
                Reset
              </a-button>
            </div>
            <div v-if="isSavedPath" class="path-info">
              <span class="path-saved-indicator">📁 Using saved location</span>
            </div>
          </div>

          <!-- Download Section -->
          <div v-if="videoInfo" class="section">
            <h2>Download</h2>
            <div class="download-controls">
              <a-button
                type="primary"
                size="large"
                :loading="isDownloading"
                @click="downloadVideo"
                :disabled="!downloadPath || (!extractedUrl && !videoUrl.includes('http'))"
                class="download-btn"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                {{ isDownloading ? 'Downloading...' : 'Download Video' }}
              </a-button>
            </div>
          </div>

          <!-- Progress Section -->
          <div v-if="downloadProgress.length > 0 || batchProgress.length > 0" class="section">
            <h2>{{ isBatchDownloading ? 'Batch Download Progress' : 'Download Progress' }}</h2>

            <!-- Batch Progress -->
            <div v-if="isBatchDownloading && batchProgress.length > 0" class="batch-progress-container">
              <div class="batch-progress-header">
                <span class="batch-current">Video {{ currentBatchIndex }} of {{ totalBatchCount }}</span>
                <span class="batch-percentage">{{ Math.round((currentBatchIndex / totalBatchCount) * 100) }}%</span>
              </div>
              <div class="batch-progress-bar">
                <div
                  class="batch-progress-fill"
                  :style="{ width: `${(currentBatchIndex / totalBatchCount) * 100}%` }"
                ></div>
              </div>
              <div class="batch-progress-list">
                <div
                  v-for="(item, index) in batchProgress"
                  :key="index"
                  class="batch-progress-item"
                  :class="item.status"
                >
                  <span class="batch-item-index">{{ item.index }}.</span>
                  <span class="batch-item-platform" :class="item.platform">{{ item.platform.toUpperCase() }}</span>
                  <span class="batch-item-url">{{ item.url.substring(0, 40) }}...</span>
                  <span class="batch-item-status">{{ item.statusText }}</span>
                </div>
              </div>
            </div>

            <!-- Regular Progress -->
            <div v-if="downloadProgress.length > 0" class="progress-container">
              <div
                v-for="(line, index) in downloadProgress"
                :key="index"
                class="progress-line"
                v-html="formatProgressText(line)"
              >
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { DownloadOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';

// Reactive data
const videoUrl = ref('');
const extractedUrl = ref(''); // Store extracted URL from share text
const videoInfo = ref(null);
const videoSource = ref(''); // Track which service was used
const detectedPlatform = ref(''); // Track detected platform
const selectedFormat = ref('');
const downloadPath = ref('');
const isGettingInfo = ref(false);
const isDownloading = ref(false);
const isGettingDouyinInfo = ref(false);
const downloadProgress = ref([]);
const error = ref('');

// Batch download data
const isBatchDownloading = ref(false);
const batchProgress = ref([]);
const currentBatchIndex = ref(0);
const totalBatchCount = ref(0);

// Douyin specific
const useDouyinExtractor = ref(false); // Default to false to keep original behavior
const douyinVideoInfo = ref(null);

// Computed properties
const isLoading = computed(() => isGettingInfo.value || isDownloading.value || isGettingDouyinInfo.value || isBatchDownloading.value);

const urlList = computed(() => {
  if (!videoUrl.value.trim()) return [];

  const lines = videoUrl.value.split('\n').map(line => line.trim()).filter(line => line);
  const urls = [];

  for (const line of lines) {
    const extracted = extractUrlFromText(line);
    if (extracted) {
      urls.push({
        original: line,
        extracted: extracted,
        platform: detectPlatformFromUrl(extracted)
      });
    }
  }

  return urls;
});

const isBatchMode = computed(() => urlList.value.length > 1);

const isDouyinUrl = computed(() => {
  const url = extractedUrl.value || videoUrl.value.trim();
  return url.includes('douyin.com') || url.includes('v.douyin.com') || detectedPlatform.value === 'douyin';
});

const isSavedPath = computed(() => {
  const savedPath = localStorage.getItem('download-tool-path');
  return savedPath && savedPath === downloadPath.value;
});

const filteredFormats = computed(() => {
  if (!videoInfo.value?.formats) return [];

  // Handle different format structures based on source
  if (videoSource.value === 'yt-dlp') {
    // yt-dlp format structure
    const videoFormats = videoInfo.value.formats.filter(format =>
      format.ext && format.ext !== 'mhtml' && format.format_note !== 'audio only'
    );

    const uniqueFormats = videoFormats.filter((format, index, self) =>
      index === self.findIndex(f => f.format_id === format.format_id)
    );

    return uniqueFormats.sort((a, b) => {
      const aQuality = parseInt(a.format_note?.match(/\d+/)?.[0] || '0');
      const bQuality = parseInt(b.format_note?.match(/\d+/)?.[0] || '0');
      return bQuality - aQuality;
    });
  } else {
    // Custom service format structure
    return videoInfo.value.formats.filter(format =>
      format.type === 'video' || !format.type
    ).sort((a, b) => {
      // Sort by quality string (e.g., "HD", "720p", etc.)
      const aQuality = parseInt(a.quality?.match(/\d+/)?.[0] || '0');
      const bQuality = parseInt(b.quality?.match(/\d+/)?.[0] || '0');
      return bQuality - aQuality;
    });
  }
});

// Initialize default download path
onMounted(async () => {
  try {
    // Check if user has saved a download path
    const savedPath = localStorage.getItem('download-tool-path');

    if (savedPath) {
      // Use saved path
      downloadPath.value = savedPath;
    } else {
      // Get user's Videos folder path as default
      const result = await window.electronAPI.invoke('get-videos-dir');
      if (result.success) {
        downloadPath.value = result.path;
      } else {
        console.error('Failed to get videos directory:', result.error);
        downloadPath.value = '';
      }
    }
  } catch (err) {
    console.error('Failed to get default download path:', err);
    downloadPath.value = '';
  }
});

// Progress listener cleanup
let progressCleanup = null;

onUnmounted(() => {
  try {
    if (progressCleanup && typeof progressCleanup === 'function') {
      progressCleanup();
    }
  } catch (error) {
    console.error('Error cleaning up progress listener:', error);
  }
});

// Methods
function extractUrlFromText(text) {
  if (!text) return '';

  // Extract URLs using regex patterns
  const urlPatterns = [
    // Douyin URLs
    /https?:\/\/v\.douyin\.com\/[A-Za-z0-9\-_]+\/?/g,
    /https?:\/\/www\.douyin\.com\/[A-Za-z0-9\-_\/]+/g,
    /https?:\/\/www\.iesdouyin\.com\/[A-Za-z0-9\-_\/]+/g,

    // TikTok URLs
    /https?:\/\/(?:www\.)?tiktok\.com\/@[A-Za-z0-9\-_.]+\/video\/\d+/g,
    /https?:\/\/vm\.tiktok\.com\/[A-Za-z0-9]+\/?/g,

    // YouTube URLs
    /https?:\/\/(?:www\.)?youtube\.com\/watch\?v=[A-Za-z0-9\-_]+/g,
    /https?:\/\/youtu\.be\/[A-Za-z0-9\-_]+/g,

    // Instagram URLs
    /https?:\/\/(?:www\.)?instagram\.com\/(?:p|reel|tv)\/[A-Za-z0-9\-_]+\/?/g,

    // Facebook URLs
    /https?:\/\/(?:www\.)?facebook\.com\/[A-Za-z0-9\-_.\/]+\/videos\/\d+/g,

    // Generic URL pattern as fallback
    /https?:\/\/[^\s]+/g
  ];

  for (const pattern of urlPatterns) {
    const matches = text.match(pattern);
    if (matches && matches.length > 0) {
      return matches[0]; // Return first match
    }
  }

  return '';
}

function detectPlatformFromUrl(url) {
  const urlLower = url.toLowerCase();

  if (urlLower.includes('douyin.com') || urlLower.includes('iesdouyin.com')) {
    return 'douyin';
  }
  if (urlLower.includes('tiktok.com')) {
    return 'tiktok';
  }
  if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
    return 'youtube';
  }
  if (urlLower.includes('facebook.com') || urlLower.includes('instagram.com')) {
    return 'facebook_insta';
  }
  if (urlLower.includes('linkedin.com')) {
    return 'linkedin';
  }
  if (urlLower.includes('pinterest.com')) {
    return 'pinterest';
  }
  if (urlLower.includes('reddit.com')) {
    return 'reddit';
  }
  if (urlLower.includes('threads.net')) {
    return 'threads';
  }
  if (urlLower.includes('twitter.com') || urlLower.includes('x.com')) {
    return 'twitter';
  }

  return '';
}

function onUrlChange() {
  const inputText = videoUrl.value.trim();

  if (isBatchMode.value) {
    // In batch mode, don't set single URL values
    extractedUrl.value = '';
    detectedPlatform.value = '';
  } else {
    // Single URL mode
    const extracted = extractUrlFromText(inputText);

    if (extracted && extracted !== inputText) {
      // Found a URL in the text
      extractedUrl.value = extracted;
      detectedPlatform.value = detectPlatformFromUrl(extracted);
    } else {
      // Input is already a URL or no URL found
      extractedUrl.value = inputText;
      detectedPlatform.value = detectPlatformFromUrl(inputText);
    }
  }
}

function handleEnterKey() {
  if (isBatchMode.value) {
    startBatchDownload();
  } else if (isDouyinUrl.value && useDouyinExtractor.value) {
    getDouyinInfoAndDownload();
  } else {
    getVideoInfo();
  }
}

function onDouyinExtractorChange() {
  // Reset video info when switching extractors
  if (videoInfo.value) {
    videoInfo.value = null;
    selectedFormat.value = '';
  }
}

async function getDouyinVideoInfo(url) {
  try {
    console.log('🎯 Calling Douyin extractor for URL:', url);

    // Test if IPC is available
    if (!window.electronAPI || !window.electronAPI.invoke) {
      throw new Error('Electron API not available');
    }

    // Call the Douyin extractor
    const result = await window.electronAPI.invoke('get-douyin-video-info', url);

    console.log('📡 Douyin extractor response:', result);

    if (result && result.success) {
      const formattedResult = {
        success: true,
        title: result.fileName || result.title || 'Douyin Video',
        uploader: result.author || 'Unknown',
        duration: result.duration,
        thumbnail: result.cover,
        formats: result.formats || (result.videoUrl ? [{
          format_id: 'douyin-direct',
          url: result.videoUrl,
          ext: 'mp4',
          quality: 'original',
          filesize: null
        }] : []),
        webpage_url: url,
        extractor: 'douyin-direct'
      };

      console.log('✅ Formatted result:', formattedResult);

      // Double check if we actually have video URL
      if (!formattedResult.formats || formattedResult.formats.length === 0) {
        throw new Error('No video formats found in result');
      }

      const hasValidUrl = formattedResult.formats.some(f => f.url && f.url.startsWith('http'));
      if (!hasValidUrl) {
        throw new Error('No valid video URL found in formats');
      }

      return formattedResult;
    } else {
      const errorMsg = result?.error || 'Failed to extract Douyin video info';
      console.error('❌ Douyin extraction failed:', errorMsg);
      throw new Error(errorMsg);
    }
  } catch (error) {
    console.error('💥 Douyin extraction error:', error);

    // Fallback to regular yt-dlp if Douyin extractor fails
    console.log('🔄 Falling back to yt-dlp...');
    throw new Error(`Douyin service failed: ${error.message}. Try using regular "Get Info" button instead.`);
  }
}

// Direct download for Douyin videos (bypass yt-dlp)
async function downloadDouyinDirect(videoInfo) {
  try {
    if (!videoInfo.formats || videoInfo.formats.length === 0) {
      throw new Error('No video formats available');
    }

    const format = videoInfo.formats[0]; // Use first (best) format
    const videoUrl = format.url;

    if (!videoUrl || !videoUrl.startsWith('http')) {
      throw new Error('Invalid video URL');
    }

    console.log('🎬 Starting direct download from:', videoUrl);

    // Clear previous progress and setup progress listener
    downloadProgress.value = [];

    // Setup progress listener for direct download (reuse existing listener)
    if (progressCleanup) {
      progressCleanup();
    }

    progressCleanup = window.electronAPI.on('yt-dlp-progress', (data) => {
      try {
        if (typeof data === 'string' && data.trim()) {
          const lines = data.split('\n').filter(line => line.trim());
          downloadProgress.value.push(...lines);

          // Keep only last 20 lines to prevent memory issues
          if (downloadProgress.value.length > 20) {
            downloadProgress.value = downloadProgress.value.slice(-20);
          }
        }
      } catch (error) {
        console.error('Error processing progress data:', error);
      }
    });

    // Generate filename
    const title = videoInfo.title || 'Douyin Video';
    const safeTitle = title.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
    const filename = `${safeTitle}.mp4`;

    const downloadOptions = {
      url: videoUrl,
      outputPath: downloadPath.value,
      filename: filename,
      headers: {
        'Referer': 'https://www.douyin.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    };

    console.log('📡 Calling direct download with options:', downloadOptions);

    // Add initial progress message
    downloadProgress.value.push('🎯 Starting Douyin direct download...');
    downloadProgress.value.push(`📁 File: ${filename}`);
    downloadProgress.value.push(`🔗 URL: ${videoUrl.substring(0, 100)}...`);

    // Use direct download instead of yt-dlp
    const result = await window.electronAPI.invoke('direct-download', downloadOptions);

    if (result.success) {
      console.log('✅ Direct download successful:', result);
      downloadProgress.value.push('✅ Download completed successfully!');
      downloadProgress.value.push(`📁 Saved to: ${result.filePath}`);
      return result;
    } else {
      throw new Error(result.error || 'Direct download failed');
    }

  } catch (error) {
    console.error('💥 Direct download error:', error);
    downloadProgress.value.push(`❌ Download failed: ${error.message}`);
    throw error;
  }
}



// Batch download functionality
async function startBatchDownload() {
  if (!downloadPath.value) {
    message.error('Please select a download path first');
    return;
  }

  if (urlList.value.length === 0) {
    message.error('No valid URLs found');
    return;
  }

  isBatchDownloading.value = true;
  batchProgress.value = [];
  currentBatchIndex.value = 0;
  totalBatchCount.value = urlList.value.length;
  downloadProgress.value = [];
  error.value = '';

  // Initialize batch progress
  urlList.value.forEach((urlItem, index) => {
    batchProgress.value.push({
      index: index + 1,
      url: urlItem.extracted,
      platform: urlItem.platform,
      status: 'pending',
      statusText: 'Waiting...'
    });
  });

  try {
    message.info(`Starting batch download of ${urlList.value.length} videos...`);

    for (let i = 0; i < urlList.value.length; i++) {
      currentBatchIndex.value = i + 1;
      const urlItem = urlList.value[i];

      // Update current item status
      batchProgress.value[i].status = 'downloading';
      batchProgress.value[i].statusText = 'Downloading...';

      try {
        downloadProgress.value = [];
        downloadProgress.value.push(`📥 [${i + 1}/${urlList.value.length}] Starting download: ${urlItem.extracted}`);
        downloadProgress.value.push(`🎯 Platform: ${urlItem.platform.toUpperCase()}`);

        await downloadSingleVideo(urlItem.extracted, urlItem.platform);

        // Success
        batchProgress.value[i].status = 'success';
        batchProgress.value[i].statusText = '✅ Completed';
        downloadProgress.value.push(`✅ [${i + 1}/${urlList.value.length}] Download completed successfully!`);

        // Small delay between downloads
        if (i < urlList.value.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (err) {
        console.error(`Error downloading video ${i + 1}:`, err);
        batchProgress.value[i].status = 'error';
        batchProgress.value[i].statusText = '❌ Failed';
        downloadProgress.value.push(`❌ [${i + 1}/${urlList.value.length}] Download failed: ${err.message}`);

        // Continue with next video even if one fails
        continue;
      }
    }

    const successCount = batchProgress.value.filter(item => item.status === 'success').length;
    const failCount = batchProgress.value.filter(item => item.status === 'error').length;

    if (successCount === urlList.value.length) {
      message.success(`🎉 All ${successCount} videos downloaded successfully!`);
    } else if (successCount > 0) {
      message.warning(`⚠️ Batch download completed: ${successCount} successful, ${failCount} failed`);
    } else {
      message.error(`❌ All downloads failed`);
    }

  } catch (err) {
    console.error('Batch download error:', err);
    error.value = err.message;
    message.error(`Batch download failed: ${err.message}`);
  } finally {
    isBatchDownloading.value = false;
  }
}

async function downloadSingleVideo(url, platform) {
  // Setup progress listener for this single download
  if (progressCleanup) {
    progressCleanup();
  }

  progressCleanup = window.electronAPI.on('yt-dlp-progress', (data) => {
    try {
      if (typeof data === 'string' && data.trim()) {
        const lines = data.split('\n').filter(line => line.trim());
        downloadProgress.value.push(...lines);

        // Keep only last 15 lines to prevent memory issues during batch
        if (downloadProgress.value.length > 15) {
          downloadProgress.value = downloadProgress.value.slice(-15);
        }
      }
    } catch (error) {
      console.error('Error processing progress data:', error);
    }
  });

  try {
    // Check if it's a Douyin URL and use appropriate method
    if (platform === 'douyin' && useDouyinExtractor.value) {
      const info = await getDouyinVideoInfo(url);
      if (info.success && info.formats && info.formats.length > 0) {
        await downloadDouyinDirect(info);
      } else {
        throw new Error('No video formats found for Douyin video');
      }
    } else {
      // Use regular yt-dlp download
      const downloadOptions = {
        url: url,
        outputPath: downloadPath.value,
        format: selectedFormat.value || undefined
      };

      const result = await window.electronAPI.invoke('yt-dlp-download', downloadOptions);

      if (!result.success) {
        throw new Error(result.error || 'Download failed');
      }
    }
  } finally {
    // Clean up progress listener
    try {
      if (progressCleanup && typeof progressCleanup === 'function') {
        progressCleanup();
        progressCleanup = null;
      }
    } catch (error) {
      console.error('Error cleaning up progress listener:', error);
    }
  }
}

async function getDouyinInfoAndDownload() {
  if (!videoUrl.value.trim()) {
    message.error('Please enter a Douyin video URL');
    return;
  }

  const urlToUse = extractedUrl.value || videoUrl.value.trim();

  if (!urlToUse.startsWith('http')) {
    message.error('No valid URL found in the text');
    return;
  }

  if (!downloadPath.value) {
    message.error('Please select a download path first');
    return;
  }

  isGettingDouyinInfo.value = true;
  error.value = '';
  downloadProgress.value = [];

  try {
    if (useDouyinExtractor.value) {
      // NEW FEATURE: Use direct Douyin extractor
      console.log('🎯 Using NEW Direct Douyin Extractor...');
      const info = await getDouyinVideoInfo(urlToUse);

      if (info.success && info.formats && info.formats.length > 0) {
        // Set video info
        videoInfo.value = info;
        videoSource.value = 'douyin-direct';
        selectedFormat.value = info.formats[0].format_id;

        message.success('Video info extracted successfully! Starting download...');

        // Start direct download
        await downloadDouyinDirect(info);
        message.success('🎉 Douyin video downloaded successfully!');
      } else {
        throw new Error('No video formats found');
      }
    } else {
      // ORIGINAL LOGIC: Just use regular downloadVideo() like before
      console.log('🎯 Using ORIGINAL method - calling downloadVideo() directly...');

      // This will use the original douyinService.js + yt-dlp logic
      await downloadVideo();
      message.success('🎉 Video downloaded successfully using original method!');
    }
  } catch (err) {
    console.error('Error in Douyin quick download:', err);
    error.value = err.message;

    // Show specific error messages
    if (err.message.includes('No video formats found')) {
      message.error('No video formats found. The video might be private or unavailable.');
    } else if (err.message.includes('Douyin service failed')) {
      message.error('Douyin direct extractor failed. Try using the regular "Get Info" button instead.');
      setTimeout(() => {
        message.info('💡 Tip: You can still use the regular "Get Info" button for Douyin videos');
      }, 2000);
    } else if (err.message.includes('Download failed')) {
      // Download error already handled above, don't show duplicate message
      return;
    } else {
      message.error(`Failed to process Douyin video: ${err.message}`);
    }
  } finally {
    isGettingDouyinInfo.value = false;
  }
}

async function getVideoInfo() {
  if (!videoUrl.value.trim()) {
    message.error('Please enter a video URL or share text');
    return;
  }

  // Use extracted URL if available, otherwise use original input
  const urlToUse = extractedUrl.value || videoUrl.value.trim();

  if (!urlToUse.startsWith('http')) {
    message.error('No valid URL found in the text');
    return;
  }

  isGettingInfo.value = true;
  error.value = '';
  videoInfo.value = null;
  videoSource.value = '';
  selectedFormat.value = '';

  try {
    const result = await window.electronAPI.invoke('yt-dlp-get-info', urlToUse);

    if (result.success) {
      videoInfo.value = result.info;
      videoSource.value = result.source || 'yt-dlp';
      message.success(`Video information loaded successfully using ${videoSource.value}`);
    } else {
      error.value = result.error || 'Failed to get video information';
      message.error(error.value);
    }
  } catch (err) {
    error.value = 'Failed to get video information: ' + err.message;
    message.error(error.value);
  } finally {
    isGettingInfo.value = false;
  }
}

async function selectDownloadPath() {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openDirectory'],
      title: 'Select Download Folder'
    });

    if (!result.canceled && result.filePaths.length > 0) {
      downloadPath.value = result.filePaths[0];
      // Save to localStorage for future use
      localStorage.setItem('download-tool-path', result.filePaths[0]);
      message.success('Download path saved');
    }
  } catch (err) {
    message.error('Failed to select download path: ' + err.message);
  }
}

async function resetDownloadPath() {
  try {
    // Remove saved path from localStorage
    localStorage.removeItem('download-tool-path');

    // Get default Videos folder path
    const result = await window.electronAPI.invoke('get-videos-dir');
    if (result.success) {
      downloadPath.value = result.path;
      message.success('Download path reset to default Videos folder');
    } else {
      console.error('Failed to get videos directory:', result.error);
      message.error('Failed to reset download path');
    }
  } catch (err) {
    console.error('Failed to reset download path:', err);
    message.error('Failed to reset download path: ' + err.message);
  }
}

async function downloadVideo() {
  const urlToUse = extractedUrl.value || videoUrl.value.trim();

  if (!urlToUse || !downloadPath.value) {
    message.error('Please enter video URL and select download path');
    return;
  }

  if (!urlToUse.startsWith('http')) {
    message.error('No valid URL found in the text');
    return;
  }

  isDownloading.value = true;
  downloadProgress.value = [];
  error.value = '';

  // Setup progress listener
  progressCleanup = window.electronAPI.on('yt-dlp-progress', (data) => {
    try {
      if (typeof data === 'string' && data.trim()) {
        const lines = data.split('\n').filter(line => line.trim());
        downloadProgress.value.push(...lines);

        // Keep only last 20 lines to prevent memory issues
        if (downloadProgress.value.length > 20) {
          downloadProgress.value = downloadProgress.value.slice(-20);
        }
      }
    } catch (error) {
      console.error('Error processing progress data:', error);
    }
  });

  try {
    const downloadOptions = {
      url: urlToUse,
      outputPath: downloadPath.value,
      format: selectedFormat.value || undefined
    };

    const result = await window.electronAPI.invoke('yt-dlp-download', downloadOptions);

    if (result.success) {
      const sourceMsg = result.source ? ` using ${result.source}` : '';
      message.success(`Video downloaded successfully${sourceMsg}!`);
      downloadProgress.value.push(`✅ Download completed successfully${sourceMsg}!`);
    } else {
      error.value = result.error || 'Download failed';
      message.error(error.value);
    }
  } catch (err) {
    error.value = 'Download failed: ' + err.message;
    message.error(error.value);
  } finally {
    isDownloading.value = false;
    try {
      if (progressCleanup && typeof progressCleanup === 'function') {
        progressCleanup();
        progressCleanup = null;
      }
    } catch (error) {
      console.error('Error cleaning up progress listener:', error);
    }
  }
}

// Utility functions
function formatDuration(seconds) {
  if (!seconds) return 'Unknown';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

function formatNumber(num) {
  if (!num) return 'Unknown';
  return new Intl.NumberFormat().format(num);
}

function formatDate(dateStr) {
  if (!dateStr) return 'Unknown';

  // yt-dlp returns date in YYYYMMDD format
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);

  return `${day}/${month}/${year}`;
}

function formatFileSize(bytes) {
  if (!bytes) return '';

  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
}

function getFormatDisplayText(format) {
  if (videoSource.value === 'yt-dlp') {
    // yt-dlp format
    const text = format.format_note || format.ext || 'Unknown';
    const size = format.filesize ? ` (${formatFileSize(format.filesize)})` : '';
    return text + size;
  } else {
    // Custom service format
    const quality = format.quality || 'Default';
    const ext = format.extension || format.ext || 'mp4';
    return `${quality} (${ext})`;
  }
}



function clearProgress() {
  downloadProgress.value = [];
  message.success('Progress cleared');
}

// Format progress text with terminal-like highlighting
function formatProgressText(text) {
  if (!text) return '';

  let formatted = String(text);

  // Decode HTML entities first to prevent double encoding
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = formatted;
  formatted = tempDiv.textContent || tempDiv.innerText || formatted;

  // Highlight success indicators first
  formatted = formatted.replace(/(✅|SUCCESS|COMPLETED|DONE)/gi, '<span style="color: #7ee787; font-weight: 600;">$1</span>');

  // Highlight error indicators
  formatted = formatted.replace(/(❌|ERROR|FAILED|FAIL)/gi, '<span style="color: #ff7b72; font-weight: 600;">$1</span>');

  // Highlight warning indicators
  formatted = formatted.replace(/(⚠️|WARNING|WARN)/gi, '<span style="color: #f0883e; font-weight: 600;">$1</span>');

  // Highlight time indicators
  formatted = formatted.replace(/(\d{1,2}:\d{2}:\d{2})/g, '<span style="color: #ffa657; font-weight: 500;">$1</span>');

  // Highlight ETA
  formatted = formatted.replace(/(ETA\s+\d+:\d+:\d+)/gi, '<span style="color: #a5a5a5; font-style: italic;">$1</span>');

  // Highlight service names
  formatted = formatted.replace(/(yt-dlp|douyin|tiktok|youtube|instagram|facebook)/gi, '<span style="color: #f0883e; font-weight: 500; text-transform: uppercase;">$1</span>');

  // Highlight download progress bars
  formatted = formatted.replace(/(\[[\d\s\/=>\-]+\])/g, '<span style="color: #d2a8ff; font-weight: 500;">$1</span>');

  // Highlight file sizes and speeds (with units)
  formatted = formatted.replace(/\b(\d+(?:\.\d+)?(?:MB|KB|GB|TB)(?:\/s)?)\b/gi, '<span style="color: #58a6ff; font-weight: 500;">$1</span>');

  // Highlight percentages (standalone)
  formatted = formatted.replace(/\b(\d+(?:\.\d+)?%)\b/g, '<span style="color: #79c0ff; font-weight: 600;">$1</span>');

  // Highlight standalone numbers (not part of IDs or text)
  formatted = formatted.replace(/\b(\d+(?:\.\d+)?)\b(?![a-zA-Z%])/g, '<span style="color: #79c0ff; font-weight: 600;">$1</span>');

  return formatted;
}
</script>

<style scoped>
.download-tool-container {
  padding: 12px;
  /* max-width: 1200px; */
  margin: 0 auto;
  background: #1a1a1a;
  /* min-height: 100vh; */
}

.header {
  text-align: center;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  position: relative;
}

.header-top {
  position: absolute;
  top: 12px;
  left: 12px;
}

.back-button {
  color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 6px !important;
  font-size: 0.85rem;
  height: 28px;
  padding: 0 8px !important;
  display: flex;
  align-items: center;
  gap: 4px;
}

.back-button:hover {
  color: white !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  background: rgba(255, 255, 255, 0.2) !important;
}

.title {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0 0 6px 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 0.95rem;
  margin: 0 0 12px 0;
  opacity: 0.9;
}

.supported-platforms {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.platforms-label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-right: 4px;
}

.platform-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 800px;
  gap: 20px;
  align-items: start;
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.right-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #1e1e1e;
  border: 1px solid #333333;
  border-radius: 8px;
  padding: 16px;
}

.right-column .section {
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  margin: 0;
}

.right-column .section h2 {
  color: #ffffff;
  margin-bottom: 12px;
}

.section {
  background: #2a2a2a;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.section h2 {
  color: #ffffff;
  font-size: 1.2rem;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.url-input-group {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.url-input-group .ant-input {
  flex: 1;
  background: #1a1a1a;
  border: 1px solid #4a4a4a;
  color: #ffffff;
}

.url-input-group .ant-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.platform-info {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.platform-label {
  color: #888888;
  font-weight: 500;
}

.platform-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.platform-badge.douyin {
  background: #FF6B6B;
  color: white;
}

.platform-badge.tiktok {
  background: #000000;
  color: white;
}

.platform-badge.youtube {
  background: #FF0000;
  color: white;
}

.platform-badge.facebook_insta {
  background: #1877F2;
  color: white;
}

.platform-badge.twitter {
  background: #1DA1F2;
  color: white;
}

.platform-note {
  color: #FFA726;
  font-style: italic;
  font-size: 0.8rem;
}

.extracted-url {
  margin-top: 8px;
  padding: 8px 12px;
  background: #2a2a2a;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.extracted-label {
  color: #4CAF50;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.extracted-link {
  color: #81C784;
  font-family: 'Courier New', monospace;
  word-break: break-all;
  flex: 1;
}

.path-info {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.path-saved-indicator {
  color: #4CAF50;
  font-size: 0.85rem;
  font-weight: 500;
}

.video-info-card {
  display: flex;
  gap: 16px;
  background: #1a1a1a;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #3a3a3a;
}

.video-thumbnail {
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 160px;
  height: 90px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #4a4a4a;
}

.video-details {
  flex: 1;
}

.video-details h3 {
  color: #ffffff;
  font-size: 1.1rem;
  margin: 0 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.info-row {
  display: flex;
  margin-bottom: 6px;
  color: #cccccc;
  font-size: 0.9rem;
}

.info-row .label {
  font-weight: 600;
  min-width: 100px;
  color: #888888;
}

.source-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.source-badge.yt-dlp {
  background: #4CAF50;
  color: white;
}

.source-badge.douyin {
  background: #FF6B6B;
  color: white;
}

.source-badge.tiktok {
  background: #000000;
  color: white;
}

.source-badge.youtube {
  background: #FF0000;
  color: white;
}

.source-badge.facebook_insta {
  background: #1877F2;
  color: white;
}

.source-badge.twitter {
  background: #1DA1F2;
  color: white;
}

.source-badge.linkedin {
  background: #0077B5;
  color: white;
}

.source-badge.pinterest {
  background: #BD081C;
  color: white;
}

.source-badge.reddit {
  background: #FF4500;
  color: white;
}

.source-badge.threads {
  background: #000000;
  color: white;
}

.path-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.path-input-group .ant-input {
  flex: 1;
  background: #1a1a1a;
  border: 1px solid #4a4a4a;
  color: #ffffff;
}

.download-controls {
  display: flex;
  justify-content: center;
}

.right-column .download-controls {
  justify-content: stretch;
}

.right-column .download-btn {
  width: 100%;
  min-width: auto;
}

.download-btn {
  min-width: 160px;
  height: 40px;
  font-size: 1rem;
  font-weight: 600;
}

.progress-container {
  background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.05);
}

.progress-container::before {
  content: '●';
  position: absolute;
  top: 8px;
  right: 12px;
  color: #7ee787;
  font-size: 10px;
  animation: pulse 2s infinite;
}

.progress-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(126, 231, 135, 0.03) 50%,
    transparent 100%
  );
  pointer-events: none;
  animation: scan 4s linear infinite;
}

.progress-line {
  color: #c9d1d9;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-all;
  padding: 2px 0;
  border-left: 2px solid transparent;
  padding-left: 8px;
  transition: all 0.2s ease;
}

.progress-line:hover {
  background: rgba(88, 166, 255, 0.1);
  border-left-color: #58a6ff;
}

.progress-line:last-child {
  margin-bottom: 0;
}

/* Ant Design component overrides */
.ant-select {
  background: #1a1a1a !important;
}

.ant-select .ant-select-selector {
  background: #1a1a1a !important;
  border: 1px solid #4a4a4a !important;
  color: #ffffff !important;
}

.ant-select:focus .ant-select-selector,
.ant-select-focused .ant-select-selector {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.ant-select-arrow {
  color: #888888 !important;
}

.ant-btn-primary {
  background: #667eea;
  border-color: #667eea;
}

.ant-btn-primary:hover {
  background: #5a6fd8;
  border-color: #5a6fd8;
}

.ant-alert-error {
  background: #2a1a1a;
  border: 1px solid #d32f2f;
}

/* Responsive design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr 350px;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .download-tool-container {
    padding: 8px;
  }

  .title {
    font-size: 1.5rem;
  }

  .header-top {
    position: static;
    margin-bottom: 8px;
    text-align: left;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .right-column {
    order: -1; /* Move right column to top on mobile */
  }

  .video-info-card {
    flex-direction: column;
  }

  .video-thumbnail img {
    width: 100%;
    height: auto;
  }

  .url-input-group,
  .path-input-group {
    flex-direction: column;
  }

  .url-input-group .ant-textarea,
  .path-input-group .ant-input,
  .path-input-group .ant-btn {
    margin-bottom: 6px;
  }

  .section {
    padding: 12px;
  }

  .section h2 {
    font-size: 1.1rem;
  }
}

/* Batch download styles */
.batch-info {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
  border: 1px solid #4a5568;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(74, 85, 104, 0.2);
}

.batch-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.batch-label {
  font-size: 14px;
  font-weight: 600;
  color: #4299e1;
}

.batch-count {
  font-size: 14px;
  color: #68d391;
  font-weight: 500;
  background: rgba(104, 211, 145, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.batch-urls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.batch-url-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.batch-index {
  font-size: 12px;
  color: #a0aec0;
  font-weight: 600;
  min-width: 20px;
}

.batch-url {
  font-size: 12px;
  color: #e2e8f0;
  font-family: 'Courier New', monospace;
  flex: 1;
}

.batch-more {
  font-size: 12px;
  color: #a0aec0;
  font-style: italic;
  text-align: center;
  padding: 8px;
}

.batch-progress-container {
  background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 16px;
}

.batch-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.batch-current {
  font-size: 14px;
  color: #58a6ff;
  font-weight: 600;
}

.batch-percentage {
  font-size: 14px;
  color: #7ee787;
  font-weight: 600;
}

.batch-progress-bar {
  width: 100%;
  height: 8px;
  background: #21262d;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.batch-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #58a6ff 0%, #7ee787 100%);
  transition: width 0.3s ease;
}

.batch-progress-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.batch-progress-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid transparent;
}

.batch-progress-item.pending {
  background: rgba(161, 161, 170, 0.1);
  border-color: rgba(161, 161, 170, 0.2);
}

.batch-progress-item.downloading {
  background: rgba(88, 166, 255, 0.1);
  border-color: rgba(88, 166, 255, 0.3);
  animation: pulse 2s infinite;
}

.batch-progress-item.success {
  background: rgba(126, 231, 135, 0.1);
  border-color: rgba(126, 231, 135, 0.3);
}

.batch-progress-item.error {
  background: rgba(255, 123, 114, 0.1);
  border-color: rgba(255, 123, 114, 0.3);
}

.batch-item-index {
  font-weight: 600;
  color: #a1a1aa;
  min-width: 20px;
}

.batch-item-platform {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 2px 6px;
  border-radius: 3px;
  min-width: 50px;
  text-align: center;
}

.batch-item-url {
  flex: 1;
  color: #c9d1d9;
  font-family: 'Courier New', monospace;
}

.batch-item-status {
  font-weight: 500;
  min-width: 80px;
  text-align: right;
}

.batch-progress-item.pending .batch-item-status {
  color: #a1a1aa;
}

.batch-progress-item.downloading .batch-item-status {
  color: #58a6ff;
}

.batch-progress-item.success .batch-item-status {
  color: #7ee787;
}

.batch-progress-item.error .batch-item-status {
  color: #ff7b72;
}

/* Douyin specific styles */
.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.batch-download-btn {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
  transition: all 0.3s ease;
}

.batch-download-btn:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
  transform: translateY(-2px);
}

.douyin-quick-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.douyin-quick-btn:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d63031 100%) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.douyin-options {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 1px solid #0f3460;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(15, 52, 96, 0.2);
}

.checkbox-label {
  font-size: 14px;
  font-weight: 500;
  color: #e94560;
}

.checkbox-note {
  font-size: 12px;
  color: #a8a8a8;
  font-weight: normal;
  margin-left: 4px;
}

.douyin-note {
  margin-top: 12px;
  padding: 12px;
  background: rgba(233, 69, 96, 0.1);
  border: 1px solid rgba(233, 69, 96, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.note-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 1px;
}

.note-text {
  font-size: 13px;
  color: #c9d1d9;
  line-height: 1.4;
}

.debug-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(233, 69, 96, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .douyin-quick-btn,
  .batch-download-btn {
    width: 100%;
  }

  .douyin-options {
    margin-top: 12px;
    padding: 12px;
  }

  .batch-info {
    margin-top: 12px;
    padding: 12px;
  }

  .batch-url-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .batch-progress-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .batch-item-platform {
    align-self: flex-start;
  }

  .batch-item-status {
    align-self: flex-end;
    text-align: left;
  }

  .batch-progress-header {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
</style>