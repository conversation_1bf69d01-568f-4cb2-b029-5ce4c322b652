<template>
  <div class="content-analysis-section">
    <a-divider />
    <h5>AI Content Analysis & Metadata Generator:</h5>
    <div class="analysis-controls">
      <a-space direction="vertical" style="width: 100%;">
        <a-button @click="analyzeVideoContent" :loading="isAnalyzing" type="primary" size="small"
          :disabled="!hasContentToAnalyze">
          <template #icon>
            <font-colors-outlined />
          </template>
          {{ isAnalyzing ? 'Analyzing...' : 'Analyze Content & Generate Metadata' }}
        </a-button>

        <div v-if="!hasContentToAnalyze" class="warning-text">
          <a-alert message="No content available for analysis"
            description="Please ensure there are translated texts in the current SRT list." type="warning" show-icon
            size="small" />
        </div>

        <div v-if="metadata" class="analysis-results">
          <a-card size="small" title="Generated Video Metadata">
            <template #extra>
              <a-space>
                <a-button @click="saveMetadata" size="small" type="link" :disabled="!hasChanges">
                  {{ hasChanges ? 'Save*' : 'Saved' }}
                </a-button>
                <a-button @click="copyMetadata" size="small" type="link">
                  Copy All
                </a-button>
              </a-space>
            </template>

            <div class="metadata-item">
              <a-form-item label="Title:">
              <a-input-group compact>
                <a-input v-model:value="metadata.title" size="small" @change="markAsChanged"
                  :auto-size="{ minRows: 1, maxRows: 3 }" style="width: 90%" />
                <a-button @click="copyTitle" size="small">
                  Copy
                </a-button>
              </a-input-group>
              </a-form-item>
            </div>

            <div class="metadata-item">
              <strong>Description:</strong>
              <a-textarea v-model:value="metadata.description" size="small" class="metadata-input"
                @change="markAsChanged" :auto-size="{ minRows: 3, maxRows: 6 }" />
            </div>

            <div class="metadata-item">
              <strong>Tags:</strong>
              <a-textarea v-model:value="metadata.tags" size="small" class="metadata-input" @change="markAsChanged"
                :auto-size="{ minRows: 2, maxRows: 4 }" placeholder="Comma-separated tags" />
            </div>

            <div class="metadata-item">
              <strong>Keywords:</strong>
              <a-textarea v-model:value="metadata.keywords" size="small" class="metadata-input" @change="markAsChanged"
                :auto-size="{ minRows: 2, maxRows: 4 }" placeholder="Comma-separated keywords" />
            </div>
          </a-card>
        </div>

        <div v-if="analysisError" class="error-message">
          <a-alert :message="analysisError" type="error" show-icon closable @close="analysisError = ''" />
        </div>
      </a-space>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { FontColorsOutlined } from '@ant-design/icons-vue';
import { useTTSStore } from '@/stores/ttsStore';

const ttsStore = useTTSStore();

// Component state
const isAnalyzing = ref(false);
const metadata = ref(null);
const analysisError = ref('');
const hasChanges = ref(false);

// Computed properties
const hasContentToAnalyze = computed(() => {
  const items = ttsStore.currentSrtList?.items || [];
  return items.length > 0 && items.some(item => item.translatedText || item.text);
});

// Watch for currentSrtList changes to load existing metadata
watch(() => ttsStore.currentSrtList, (newSrtList) => {
  if (newSrtList?.metadata) {
    metadata.value = { ...newSrtList.metadata };
    hasChanges.value = false;
  } else {
    metadata.value = null;
    hasChanges.value = false;
  }
}, { immediate: true });

// Load existing metadata on mount
onMounted(() => {
  if (ttsStore.currentSrtList?.metadata) {
    metadata.value = { ...ttsStore.currentSrtList.metadata };
    hasChanges.value = false;
  }
});

// Mark as changed when user edits
function markAsChanged() {
  hasChanges.value = true;
}

// Main analysis function
async function analyzeVideoContent() {
  if (!hasContentToAnalyze.value) {
    message.warning('No content available for analysis');
    return;
  }

  isAnalyzing.value = true;
  analysisError.value = '';

  try {
    // Collect all translated texts
    const items = ttsStore.currentSrtList?.items || [];
    const translatedTexts = items
      .map(item => item.translatedText || item.text)
      .filter(text => text && text.trim())
      .slice(0, 20); // Limit to first 20 items to avoid token limits

    // Get video title if available
    const videoTitle = ttsStore.currentSrtList?.title || 
                      ttsStore.currentSrtList?.name || 
                      'Untitled Video';

    // Combine all content for analysis
    const fullContent = translatedTexts.join(' ');
    
    // Create analysis prompt
    const analysisPrompt = `Analyze the following video content and generate engaging metadata for YouTube/social media platforms. Focus on creating titles, descriptions, tags, and keywords that will attract viewers and increase engagement.

Video Title: ${videoTitle}

Video Content (subtitles):
${fullContent}

Please generate the following metadata in JSON format:
{
  "title": "An engaging, clickbait-style title that creates curiosity and encourages clicks (max 60 characters)",
  "description": "A compelling description that hooks viewers, explains what they'll learn/see, and encourages engagement (2-3 paragraphs)",
  "tags": "Relevant hashtags and tags separated by commas (focus on trending and discoverable tags)",
  "keywords": "SEO keywords and phrases separated by commas (focus on searchable terms)"
}

Make the content appealing to Vietnamese audiences and focus on elements that create curiosity, suspense, or emotional engagement.`;

    // Call AI API based on selected service
    const selectedService = ttsStore.selectedAiService || 'deepseek';
    const apiKey = ttsStore.aiServices[selectedService]?.apiKey;
    
    if (!apiKey) {
      throw new Error(`API key not found for ${selectedService}. Please configure it in settings.`);
    }

    let result;
    
    if (selectedService === 'openai') {
      result = await callOpenAIAPI(analysisPrompt, apiKey);
    } else if (selectedService === 'deepseek') {
      result = await callDeepSeekAPI(analysisPrompt, apiKey);
    } else if (selectedService === 'gemini') {
      result = await callGeminiAPI(analysisPrompt, apiKey);
    }  else if (selectedService === 'nebula') {
      result = await callNebulaAPI(analysisPrompt, apiKey);
    } else {
      throw new Error(`Unsupported AI service: ${selectedService}`);
    }

    // Parse the result
    try {
      const parsedResult = JSON.parse(result);
      metadata.value = {
        title: parsedResult.title || '',
        description: parsedResult.description || '',
        tags: parsedResult.tags || '',
        keywords: parsedResult.keywords || '',
        generatedAt: new Date().toISOString(),
        aiService: selectedService,
        model: ttsStore.selectedModel
      };
      
      // Auto-save the metadata
      await saveMetadata();
      message.success('Content analysis completed and saved successfully!');
    } catch (parseError) {
      // If JSON parsing fails, try to extract content manually
      metadata.value = {
        ...extractMetadataFromText(result),
        generatedAt: new Date().toISOString(),
        aiService: selectedService,
        model: ttsStore.selectedModel
      };
      
      // Auto-save the metadata
      await saveMetadata();
      message.success('Content analysis completed and saved (with manual parsing)!');
    }

  } catch (error) {
    console.error('Content analysis error:', error);
    analysisError.value = error.message || 'Failed to analyze content';
    message.error('Failed to analyze content: ' + error.message);
  } finally {
    isAnalyzing.value = false;
  }
}

// Save metadata to ttsStore
async function saveMetadata() {
  if (!metadata.value || !ttsStore.currentSrtList) {
    message.warning('No metadata to save or no current SRT list');
    return;
  }

  try {
    // Update the current SRT list with metadata
    ttsStore.currentSrtList.metadata = { ...metadata.value };

    // Also update in the srtLists array
    const index = ttsStore.srtLists.findIndex(
      (item) => item.name === ttsStore.currentSrtList.name
    );
    if (index !== -1) {
      ttsStore.srtLists[index].metadata = { ...metadata.value };
    }

    hasChanges.value = false;
    message.success('Metadata saved successfully!');
  } catch (error) {
    console.error('Save metadata error:', error);
    message.error('Failed to save metadata: ' + error.message);
  }
}

// API call functions
async function callOpenAIAPI(prompt, apiKey) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: ttsStore.selectedModel || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
  }

  const data = await response.json();
  return data.choices?.[0]?.message?.content || '';
}
// API call functions
async function callNebulaAPI(prompt, apiKey) {
  const response = await fetch('https://inference.nebulablock.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: ttsStore.selectedModel || 'openai/gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
  }

  const data = await response.json();
  return data.choices?.[0]?.message?.content || '';
}
async function callDeepSeekAPI(prompt, apiKey) {
  const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: ttsStore.selectedModel || 'deepseek-chat',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
  }

  const data = await response.json();
  return data.choices?.[0]?.message?.content || '';
}

async function callGeminiAPI(prompt, apiKey) {
  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 2000
      }
    })
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
  }

  const data = await response.json();
  return data.candidates?.[0]?.content?.parts?.[0]?.text || '';
}

// Fallback function to extract metadata from text if JSON parsing fails
function extractMetadataFromText(text) {
  const lines = text.split('\n').filter(line => line.trim());
  const result = {
    title: '',
    description: '',
    tags: '',
    keywords: ''
  };

  let currentSection = '';
  let descriptionLines = [];

  for (const line of lines) {
    const lowerLine = line.toLowerCase();

    if (lowerLine.includes('title') && lowerLine.includes(':')) {
      result.title = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '');
      currentSection = 'title';
    } else if (lowerLine.includes('description') && lowerLine.includes(':')) {
      const desc = line.split(':').slice(1).join(':').trim();
      if (desc) descriptionLines.push(desc);
      currentSection = 'description';
    } else if (lowerLine.includes('tag') && lowerLine.includes(':')) {
      result.tags = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '');
      currentSection = 'tags';
    } else if (lowerLine.includes('keyword') && lowerLine.includes(':')) {
      result.keywords = line.split(':').slice(1).join(':').trim().replace(/['"]/g, '');
      currentSection = 'keywords';
    } else if (currentSection === 'description' && line.trim()) {
      descriptionLines.push(line.trim());
    }
  }

  result.description = descriptionLines.join(' ').replace(/['"]/g, '');

  return result;
}

// Copy metadata to clipboard
async function copyMetadata() {
  if (!metadata.value) {
    message.warning('No metadata to copy');
    return;
  }

  const metadataText = `Title: ${metadata.value.title}

Description:
${metadata.value.description}

Tags:
${metadata.value.tags}

Keywords:
${metadata.value.keywords}`;

  try {
    await navigator.clipboard.writeText(metadataText);
    message.success('Metadata copied to clipboard!');
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    message.error('Failed to copy to clipboard');
  }
}

async function copyTitle() {

  try {
    await navigator.clipboard.writeText(metadata.value.title);
    message.success('Metadata copied to clipboard!');
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    message.error('Failed to copy to clipboard');
  }
}



</script>

<style scoped>
.content-analysis-section {
  margin-top: 16px;
}

.analysis-controls {
  margin-top: 8px;
}

.analysis-results {
  margin-top: 12px;
}

.metadata-item {
  margin-bottom: 12px;
}

.metadata-item strong {
  display: block;
  margin-bottom: 4px;
  color: #1890ff;
  font-size: 12px;
}

.metadata-input {
  font-size: 11px !important;
}

.warning-text {
  margin-top: 8px;
}

.error-message {
  margin-top: 8px;
}
</style>
