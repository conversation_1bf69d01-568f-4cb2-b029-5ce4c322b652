<template>
    <div class="video-wrapper card">
        <div class="video-img">
            <div v-if="errorMessage !== ''">{{ errorMessage }}</div>
            <video
                ref="videoRef"
                :src="videoUrl"
                :muted="false"
                :controls="true"
                @loadedmetadata="onLoadedMetadata"
                @seeked="onSeeked"
                class="aspect-16/9"
            />
        </div>
        <VideoBar v-model="currentPercent" :totalFrame="videoProperties.lastFrame" :fps="fps" :disabled="disabled" @bar-drag-start="play = false"></VideoBar>
        <div class="video-control">
            <span class="video-control-currentframe">{{currentTime}}</span>
            <div class="video-control-buttons">
                <button :disabled="disabled" @click="prevSubtitleEvent" v-if="showSubtitleJumpButton">
                    <img src="@/assets/prev_line.svg" alt />
                </button>
                <button @click="prevFrameEvent" :disabled="disabled || currentFrame <= 0">
                    <img src="@/assets/prev_frame.svg" alt />
                </button>
                <button v-if="play" @click="stopVideo()" :disabled="disabled">
                    <img src="@/assets/pause.svg" alt />
                </button>
                <button v-else @click="playVideo()" :disabled="disabled">
                    <img src="@/assets/play.svg" alt />
                </button>
                <button
                    @click="nextFrameEvent"
                    :disabled="disabled || currentFrame >= videoProperties.lastFrame"
                >
                    <img src="@/assets/next_frame.svg" alt />
                </button>
                <button :disabled="disabled" @click="nextSubtitleEvent" v-if="showSubtitleJumpButton">
                    <img src="@/assets/next_line.svg" alt />
                </button>
            </div>
            <span class="video-control-duration">{{durationTime}}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import lodash from 'lodash'
import VideoBar from '@/components/subtitle/VideoBar.vue'
import { frameToTime } from '@/lib/utils'
import { VideoProperties } from '@/lib/VideoProperties'

const props = defineProps<{
  videoProperties: VideoProperties
  currentFrame: number
  showSubtitleJumpButton?: boolean
  disabled?: boolean
  videoPath: string
}>()

const emit = defineEmits<{
  (e: 'change', value: number): void
  (e: 'prev-subtitle'): void
  (e: 'next-subtitle'): void
}>()

const videoRef = ref<HTMLVideoElement>()
const videoUrl = ref('')

const frameData = ref<Buffer | null>(null)
const play = ref(false)
const errorMessage = ref('')
const currentDisplayFrame = ref(-1)
const updatePicDataPromise = ref(Promise.resolve())

// Debounced update
// const debouncedUpdatePicData = lodash.debounce((frame: number) => {
//   updatePicDataPromise.value = updatePicDataPromise.value.then(() => updatePicData(frame))
// }, 500, { leading: true })

// fps & time
const fps = computed(() => props.videoProperties.fps[0] / props.videoProperties.fps[1])

const currentTime = computed(() =>
  frameToTime(props.currentFrame, fps.value)
)

const durationTime = computed(() =>
  frameToTime(props.videoProperties.duration / props.videoProperties.unitFrame, fps.value)
)

const currentPercent = computed({
  get: () => props.currentFrame / props.videoProperties.lastFrame,
  set: (value) => emit('change', value * props.videoProperties.lastFrame),
})

const picData = computed(() => {
  if (!frameData.value) return ''
  const blob = new Blob([frameData.value.buffer], { type: 'image/bmp' })
  return window.URL.createObjectURL(blob)
})

watch(() => props.currentFrame, async (newVal, oldVal) => {
  if (!videoRef.value) return
  const targetTime = newVal * props.videoProperties.unitFrame
  if (Math.abs(videoRef.value.currentTime - targetTime) > 0.01) {
    videoRef.value.currentTime = targetTime
  }
})

watch(() => props.currentFrame, async (newVal, oldVal) => {
  if (newVal === 0) {
    currentDisplayFrame.value = -1
    // updatePicDataPromise.value = updatePicDataPromise.value.then(() => updatePicData(newVal))
  } else if (newVal !== oldVal) {
    // updatePicDataPromise.value = updatePicDataPromise.value.then(() => updatePicData(newVal))
    // Nếu muốn dùng debounce thay thế, bật dòng sau:
    // await debouncedUpdatePicData(newVal)
  }
})
watch(() => props.disabled, (val) => {
  if (val) stopVideo()
})
function onSeeked() {
  currentDisplayFrame.value = Math.round(videoRef.value!.currentTime / props.videoProperties.unitFrame)
}


// async function updatePicData(frame: number) {
//   const timestamp = Math.round(frame * props.videoProperties.unitFrame)
//   if (frame === currentDisplayFrame.value) return

//   const result = await electronAPI.invoke('VideoPlayer:GetImage', timestamp)
//   if (result instanceof Error) {
//     errorMessage.value = result.message
//   } else {
//     frameData.value = (result as RenderedVideo).data as Buffer
//     errorMessage.value = ''
//     currentDisplayFrame.value = Math.round((result as RenderedVideo).timestamp / props.videoProperties.unitFrame)
//   }
// }
const playVideo4 = async () => {
  play.value = true
  const timeoutTime = (1000 * props.videoProperties.fps[1]) / props.videoProperties.fps[0]

  while (props.currentFrame < props.videoProperties.lastFrame && play.value) {
    emit('change', props.currentFrame + 1)
    await new Promise((resolve) => setTimeout(resolve, timeoutTime))
  }

  play.value = false
}
async function playVideo() {
  play.value = true
  try {
    await videoRef.value?.play()
    
  } catch (err) {
    errorMessage.value = 'Playback failed'
  }
}

function stopVideo() {
  play.value = false
  videoRef.value?.pause()
}

function prevSubtitleEvent() {
  emit('prev-subtitle')
}

function nextSubtitleEvent() {
  emit('next-subtitle')
}

function prevFrameEvent() {
  emit('change', props.currentFrame - 1)
}

function nextFrameEvent() {
  emit('change', props.currentFrame + 1)
}

onMounted(() => {
    const pathToVideo = props.videoPath.replace(/\\/g, '/')
//   updatePicData(0)
    videoUrl.value = `file://${pathToVideo}`

})

function setVideoPath(videoPath){
    const pathToVideo = videoPath.replace(/\\/g, '/')
//   updatePicData(0)
    videoUrl.value = `file://${pathToVideo}`
}



defineExpose({
  setVideoPath
})


</script>

<style scoped>
.video-player {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.video-frame {
  max-width: 100%;
  border: 1px solid #ccc;
}

.controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 8px;
}
</style>

<style scoped>
.video-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 32px;
    flex-grow: 1;
}

.video-img {
    background-color: #000;
    display: flex;
    position: relative;
    flex-grow: 1;
    max-height: calc(100% - 48px);
}

.video-img img {
    max-width: 100%;
    max-height: 100%;
    pointer-events: none;
    margin: auto;
}

.video-control {
    display: flex;
    justify-content: space-between;
    align-content: center;
    padding: 8px 24px;
}

.video-control-currentframe,
.video-control-duration {
    font-size: 14px;
    line-height: 24px;
    margin: auto 0;
    font-weight: 600;
    width: 200px;
    user-select: text;
}

.video-control-currentframe {
    color: #18a1b4;
}

.video-control-duration {
    color: rgba(255, 255, 255, 0.4);
    text-align: right;
    cursor: default;
}

.video-control-buttons {
    display: flex;
    justify-content: center;
}

.video-control-buttons button {
    display: flex;
    padding: 2px 6px;
    margin: 0 12px;
    border-radius: 2px;
    background: transparent;
    border: transparent;
    transition: 0.2s all;
    cursor: pointer;
}

.video-control-buttons button:focus {
    outline: none;
}

.video-control-buttons button:hover {
    background: #fff 20;
    border: #fff 20;
}

.video-control-buttons button:disabled {
    cursor: not-allowed;
    background: transparent;
    border: transparent;
}

</style>
