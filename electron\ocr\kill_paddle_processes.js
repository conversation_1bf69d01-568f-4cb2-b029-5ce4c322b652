const { exec } = require('child_process');
const os = require('os');

// Utility to kill all PaddleOCR-json.exe processes
class PaddleProcessKiller {
  static async killAllPaddleProcesses() {
    const platform = os.platform();
    
    try {
      if (platform === 'win32') {
        // Windows
        await this.killWindowsProcesses();
      } else {
        // Linux/Mac
        await this.killUnixProcesses();
      }
    } catch (error) {
      console.error('❌ Error killing PaddleOCR processes:', error);
    }
  }

  static killWindowsProcesses() {
    return new Promise((resolve, reject) => {
      // Kill all PaddleOCR-json.exe processes
      exec('taskkill /F /IM PaddleOCR-json.exe', (error, stdout, stderr) => {
        if (error) {
          // If no processes found, it's not an error
          if (error.message.includes('not found') || error.message.includes('No tasks')) {
            console.log('✅ No PaddleOCR processes found to kill');
            resolve();
          } else {
            console.error('❌ Error killing Windows processes:', error);
            reject(error);
          }
        } else {
          console.log('💀 Killed PaddleOCR processes:', stdout);
          resolve();
        }
      });
    });
  }

  static killUnixProcesses() {
    return new Promise((resolve, reject) => {
      // Find and kill PaddleOCR processes
      exec('pkill -f PaddleOCR-json', (error, stdout, stderr) => {
        if (error) {
          // If no processes found, it's not an error
          if (error.code === 1) {
            console.log('✅ No PaddleOCR processes found to kill');
            resolve();
          } else {
            console.error('❌ Error killing Unix processes:', error);
            reject(error);
          }
        } else {
          console.log('💀 Killed PaddleOCR processes');
          resolve();
        }
      });
    });
  }

  static async listPaddleProcesses() {
    const platform = os.platform();
    
    try {
      if (platform === 'win32') {
        return await this.listWindowsProcesses();
      } else {
        return await this.listUnixProcesses();
      }
    } catch (error) {
      console.error('❌ Error listing processes:', error);
      return [];
    }
  }

  static listWindowsProcesses() {
    return new Promise((resolve, reject) => {
      exec('tasklist /FI "IMAGENAME eq PaddleOCR-json.exe"', (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          const lines = stdout.split('\n').filter(line => line.includes('PaddleOCR-json.exe'));
          console.log(`🔍 Found ${lines.length} PaddleOCR processes:`);
          lines.forEach(line => console.log(`   ${line.trim()}`));
          resolve(lines);
        }
      });
    });
  }

  static listUnixProcesses() {
    return new Promise((resolve, reject) => {
      exec('ps aux | grep PaddleOCR-json', (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          const lines = stdout.split('\n').filter(line => 
            line.includes('PaddleOCR-json') && !line.includes('grep')
          );
          console.log(`🔍 Found ${lines.length} PaddleOCR processes:`);
          lines.forEach(line => console.log(`   ${line.trim()}`));
          resolve(lines);
        }
      });
    });
  }

  // Auto cleanup on app exit
  static setupAutoCleanup() {
    const cleanup = async () => {
      console.log('🧹 Cleaning up PaddleOCR processes on app exit...');
      await this.killAllPaddleProcesses();
    };

    // Handle different exit scenarios
    process.on('exit', cleanup);
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('uncaughtException', async (error) => {
      console.error('Uncaught Exception:', error);
      await cleanup();
      process.exit(1);
    });

    console.log('✅ Auto cleanup for PaddleOCR processes enabled');
  }
}

module.exports = { PaddleProcessKiller };

// If run directly, kill all processes
if (require.main === module) {
  console.log('🔍 Checking for PaddleOCR processes...');
  
  PaddleProcessKiller.listPaddleProcesses().then(() => {
    console.log('\n💀 Killing all PaddleOCR processes...');
    return PaddleProcessKiller.killAllPaddleProcesses();
  }).then(() => {
    console.log('✅ Cleanup completed');
  }).catch(error => {
    console.error('❌ Cleanup failed:', error);
  });
}
