const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const { processVideoSimplified } = require('./videoRendererSimplified');
const { getVideoInfo, getEncoder } = require('../ffmpegHandler');
const {demucs} = require('./demucs');
const { ffmpegManager } = require('../ffmpeg-config');

/**
 * Auto-wrap text to fit video width by adding line breaks
 * @param {string} text - Text to wrap
 * @param {number} maxCharsPerLine - Max characters per line (estimated)
 * @returns {string} - Text with line breaks
 */
function autoWrapText(text, maxCharsPerLine = 40) {
  if (!text) return '';

  const words = text.split(' ');
  const lines = [];
  let currentLine = '';

  for (const word of words) {
    const testLine = currentLine ? `${currentLine} ${word}` : word;

    if (testLine.length <= maxCharsPerLine) {
      currentLine = testLine;
    } else {
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        // Word is longer than max chars, split it
        lines.push(word);
        currentLine = '';
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines.join('\\n'); // Use \\n for FFmpeg line breaks
}

/**
 * Escape text for FFmpeg drawtext filter to handle special characters
 * @param {string} text - Text to escape
 * @param {number} videoWidth - Video width for text wrapping calculation
 * @param {number} fontSize - Font size for character estimation
 * @returns {string} - Escaped text safe for FFmpeg with auto-wrapping
 */
function escapeTextForFFmpeg(text, videoWidth = 1280, fontSize = 24) {
  if (!text) return '';

  // Estimate characters per line based on video width and font size
  // Rough estimation: 1 character ≈ 0.6 * fontSize pixels
  const availableWidth = videoWidth * 0.85; // 85% of video width
  const charWidth = fontSize * 0.6;
  // const maxCharsPerLine = Math.floor(availableWidth / charWidth);

  // Auto-wrap text first
  // const wrappedText = autoWrapText(text, maxCharsPerLine);

  return text
    // Escape single quotes
    .replace(/'/g, "\\'")
    // Escape colons
    .replace(/:/g, '\\:')
    // Escape backslashes
    .replace(/\\/g, '\\\\')
    // Escape square brackets
    .replace(/\[/g, '\\[')
    .replace(/\]/g, '\\]')
    // Escape percent signs
    .replace(/%/g, '\\%')
    // Handle newlines (already added by autoWrapText)
    .replace(/\r/g, '')
    // Escape semicolons
    .replace(/;/g, '\\;')
    // Escape commas in text (but not in filter parameters)
    .replace(/,/g, '\\,');
}

// Helper function to check process status
async function checkProcessStatus(processId, event, maxWaitTime = 300000) {
  // 5 minutes max
  const type = 'video-task';
  const startTime = Date.now();

  return new Promise((resolve, reject) => {
    const checkInterval = setInterval(async () => {
      try {
        const elapsed = Date.now() - startTime;

        // Check if max wait time exceeded
        if (elapsed > maxWaitTime) {
          clearInterval(checkInterval);
          reject(new Error(`Demucs process timeout after ${maxWaitTime / 1000} seconds`));
          return;
        }

        // Check if process is still in activeProcesses (from utils.js)
        const { getActiveProcesses } = require('../utils');
        const activeProcesses = getActiveProcesses();
        const isProcessActive = activeProcesses.some((p) => p.processId === processId);

        if (!isProcessActive) {
          // Process has completed (no longer in active processes)
          clearInterval(checkInterval);
          console.log('✅ Demucs process completed successfully');
          event?.sender?.send(type, {
            data: '✅ Demucs process completed successfully',
            code: 0,
          });
          resolve({ completed: true, processId });
        } else {
          // Process still running, send progress update
          const progressMsg = `⏳ Demucs running... (${Math.round(elapsed / 1000)}s elapsed)`;
          console.log(progressMsg);
          event?.sender?.send(type, {
            data: progressMsg,
            code: 0,
          });
        }
      } catch (error) {
        clearInterval(checkInterval);
        console.error('❌ Error checking process status:', error);
        event?.sender?.send(type, {
          data: `❌ Error checking process status: ${error.message}`,
          code: 1,
        });
        reject(error);
      }
    }, 2000); // Check every 2 seconds
  });
}

async function holdMusicHandler(event, videoInput, outputDir, name= 'no_vocals') {
  const htdemucsDir = path.basename(videoInput, path.extname(videoInput));
  const volcalAudio = path.join(outputDir, '..','htdemucs', htdemucsDir, `${name}.wav`);

  // kiểm tra xem volcalAudio có tồn tại không
  if (!fs.existsSync(volcalAudio)) {
    console.log('🎵 No vocals audio not found, running demucs...');
    event?.sender?.send('video-task', {
      data: '🎵 Starting Demucs to separate vocals from music...',
      code: 0,
    });

    try {
      // Call demucs and get the process ID
      const demucsResult = await demucs(event, { fileInput: videoInput });

      if (!demucsResult.success) {
        throw new Error(demucsResult.error || 'Failed to start Demucs process');
      }

      console.log('🚀 Demucs process started with ID:', demucsResult.processId);
      event?.sender?.send('video-task', {
        data: `🚀 Demucs process started (ID: ${demucsResult.processId})`,
        code: 0,
      });

      // Wait for demucs to complete
      // wait 50 min max
      const maxWaitTime = 300000 * 10
      await checkProcessStatus(demucsResult.processId, event, maxWaitTime);

      // Verify the output file was created
      if (!fs.existsSync(volcalAudio)) {
        throw new Error(`Demucs completed but output file not found: ${volcalAudio}`);
      }

      console.log('✅ Demucs completed successfully, output file created:', volcalAudio);
      event?.sender?.send('video-task', {
        data: '✅ Demucs completed successfully',
        code: 0,
      });
    } catch (error) {
      console.error('❌ Demucs process failed:', error);
      event?.sender?.send('video-task', {
        data: `❌ Demucs failed: ${error.message}`,
        code: 1,
      });
      throw error;
    }
  } else {
    console.log('✅ No vocals audio file already exists:', volcalAudio);
    event?.sender?.send('video-task', {
      data: '✅ Using existing separated audio file',
      code: 0,
    });
  }

  // Create the final video with music only
  console.log('🎬 Creating video with music only...');
  event?.sender?.send('video-task', {
    data: '🎬 Creating video with music only...',
    code: 0,
  });

  const outputPath = path.join(outputDir, 'output_music_only.mp4');
  const encoder = await getEncoder();
  const cmd = `${ffmpegManager.ffmpegPath} -i "${videoInput}" -i "${volcalAudio}" -map 0:v -map 1:a -c:v ${encoder} -c:a aac -shortest -y "${outputPath}"`;

  console.log('📝 FFmpeg command:', cmd);
  await execPromise(cmd);

  console.log('✅ Music-only video created:', outputPath);
  event?.sender?.send('video-task', {
    data: '✅ Music-only video created successfully',
    code: 0,
  });

  return outputPath;
}



function generateDirectionExprSlow(start, segment, directionType = 'random') {
  const half = segment / 2;
  const progress = `(abs(mod(t-${start}\\,${segment})-${half})/${half})`;

  const directionMap = {
    updown: [2, 3],
    leftright: [0, 1],
    diagonal: [4, 5, 6, 7],
    all: [0, 1, 2, 3, 4, 5, 6, 7],
    random: [0, 1, 2, 3, 4, 5, 6, 7],
  };

  const validDirections = directionMap[directionType] || directionMap.random;
  const direction = validDirections[Math.floor(Math.random() * validDirections.length)];

  switch (direction) {
    case 0:
      return { x: `(w-text_w)*${progress}`, y: `(h-text_h)/2` }; // Left to Right
    case 1:
      return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)/2` }; // Right to Left
    case 2:
      return { x: `(w-text_w)/2`, y: `(h-text_h)*${progress}` }; // Top to Bottom
    case 3:
      return { x: `(w-text_w)/2`, y: `(h-text_h)*(1-${progress})` }; // Bottom to Top
    case 4:
      return { x: `(w-text_w)*${progress}`, y: `(h-text_h)*${progress}` }; // TL → BR
    case 5:
      return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)*${progress}` }; // TR → BL
    case 6:
      return { x: `(w-text_w)*${progress}`, y: `(h-text_h)*(1-${progress})` }; // BL → TR
    case 7:
      return { x: `(w-text_w)*(1-${progress})`, y: `(h-text_h)*(1-${progress})` }; // BR → TL
  }
}

// Helper function to convert media overlay percentage position to video coordinates
// This matches the logic used in VideoPreviewDraw.vue getVideoCoords function
function getMediaOverlayVideoCoords(posX, posY, videoInfo, displayDimensions = null) {
  const { width: videoWidth, height: videoHeight } = videoInfo;

  // If display dimensions are provided, use scale factors like VideoPreviewDraw
  // Otherwise, use direct percentage to pixel conversion
  if (displayDimensions) {
    const scaleX = videoWidth / displayDimensions.width;
    const scaleY = videoHeight / displayDimensions.height;

    // Convert percentage to display coordinates first, then to video coordinates
    const displayX = (posX / 100) * displayDimensions.width;
    const displayY = (posY / 100) * displayDimensions.height;

    return {
      x: Math.round(displayX * scaleX),
      y: Math.round(displayY * scaleY)
    };
  } else {
    // Direct percentage to video pixel conversion
    return {
      x: Math.round((posX / 100) * videoWidth),
      y: Math.round((posY / 100) * videoHeight)
    };
  }
}

// Helper function to get frame number from time
function getFrameNumber(timeInSeconds, fps = 30) {
  return Math.round(timeInSeconds * fps);
}

// Helper function to generate time-based enable expression
function getTimeEnableExpression(timeStart, timeEnd, duration, fps = 30) {
  const totalFrames = Math.round(duration * fps);
  const startFrame = getFrameNumber(timeStart, fps);
  const endFrame = timeEnd === duration ? totalFrames - 1 : getFrameNumber(timeEnd, fps);

  let enableExpr;
  if (timeStart === 0 && timeEnd === duration) {
    enableExpr = '1';
  } else if (timeStart === 0) {
    enableExpr = `lte(n,${endFrame})`;
  } else if (timeEnd === duration) {
    enableExpr = `gte(n,${startFrame})`;
  } else {
    enableExpr = `between(n,${startFrame},${endFrame})`;
  }

  return enableExpr;
}

// Helper function to normalize overlays (support both old and new formats)
function normalizeOverlays(options) {
  // If new format exists, use it
  if (options.overlays) {
    return options.overlays;
  }

  // Convert old format to new format for backward compatibility
  const overlays = { logos: [], images: [], texts: [] };

  if (options.logo?.enabled) {
    overlays.logos.push({
      id: 'legacy_logo',
      ...options.logo
    });
  }

  if (options.image?.enabled) {
    overlays.images.push({
      id: 'legacy_image',
      ...options.image
    });
  }

  if (options.fixedText?.enabled) {
    overlays.texts.push({
      id: 'legacy_text',
      ...options.fixedText
    });
  }

  return overlays;
}

// Helper function to apply a single logo overlay
function applyLogoOverlay(logo, inputIndex, logoIndex, videoInfo, currentLabel) {
  const logoOptions = logo.options || {};
  const { scale = 100, rotation = 0, opacity = 100 } = logoOptions;

  // Time controls
  const timeStart = logoOptions.timeStart || 0;
  const timeEnd = logoOptions.timeEnd || videoInfo.duration;

  // Calculate scale first - normalize to video dimensions
  const logoScale = scale / 100;
  // Base logo size should be proportional to video size (increased to match UI preview)
  const baseLogoSize = Math.round(videoInfo.width * 0.2); // 20% of video width as base size
  const finalLogoSize = Math.round(baseLogoSize * logoScale);

  // Use video coordinates from frontend if available, otherwise calculate from percentage
  let logoX, logoY;
  if (logo.coords?.videoCoords) {
    // Frontend coordinates are center-based (transform: translate(-50%, -50%))
    // FFmpeg overlay uses top-left corner, so we need to adjust
    const centerX = logo.coords.videoCoords.x;
    const centerY = logo.coords.videoCoords.y;

    // Adjust for anchor point: center → top-left
    // Subtract half of logo size to convert from center to top-left
    logoX = Math.round(centerX - (finalLogoSize / 2));
    logoY = Math.round(centerY - (finalLogoSize / 2));
  } else {
    // Fallback to percentage calculation
    const { posX = 90, posY = 10 } = logoOptions;
    const logoCoords = getMediaOverlayVideoCoords(posX, posY, videoInfo);
    // These coordinates are also center-based, adjust for top-left
    logoX = Math.round(logoCoords.x - (finalLogoSize / 2));
    logoY = Math.round(logoCoords.y - (finalLogoSize / 2));
  }

  const nextLabel = `with_logo_${logoIndex}`;
  let logoFilter = `[${inputIndex}:v]`;

  // Generate time-based enable expression
  const enableExpr = getTimeEnableExpression(timeStart, timeEnd, videoInfo.duration, videoInfo.fps || 30);

  // Apply transformations to logo
  if (logoScale !== 1 || rotation !== 0 || opacity !== 100) {
    // Scale to final size (not relative to original size)
    const scaleFilter = logoScale !== 1 ? `scale=${finalLogoSize}:-1` : `scale=${baseLogoSize}:-1`;
    const rotateFilter = rotation !== 0 ? `rotate=${rotation * Math.PI / 180}:c=none:ow=rotw(${rotation * Math.PI / 180}):oh=roth(${rotation * Math.PI / 180})` : '';
    const opacityFilter = opacity !== 100 ? `format=rgba,colorchannelmixer=aa=${opacity / 100}` : '';

    const transforms = [scaleFilter, rotateFilter, opacityFilter].filter(Boolean).join(',');
    if (transforms) {
      logoFilter += `${transforms}[logo_${logoIndex}_transformed];`;
      logoFilter += `[${currentLabel}][logo_${logoIndex}_transformed]overlay=x=${logoX}:y=${logoY}:enable='${enableExpr}'`;
    } else {
      logoFilter = `[${currentLabel}][${inputIndex}:v]overlay=x=${logoX}:y=${logoY}:enable='${enableExpr}'`;
    }
  } else {
    // Even when scale is 100%, normalize to base size
    logoFilter += `scale=${baseLogoSize}:-1[logo_${logoIndex}_normalized];`;
    logoFilter += `[${currentLabel}][logo_${logoIndex}_normalized]overlay=x=${logoX}:y=${logoY}:enable='${enableExpr}'`;
  }

  return {
    filter: `${logoFilter}[${nextLabel}];`,
    nextLabel: nextLabel
  };
}

// Helper function to apply a single image overlay
function applyImageOverlay(image, inputIndex, imageIndex, videoInfo, currentLabel) {
  const imageOptions = image.options || {};
  const { scale = 100, rotation = 0, opacity = 100, autoFit, blendMode } = imageOptions;

  const timeStart = imageOptions.timeStart || 0;
  const timeEnd = imageOptions.timeEnd || videoInfo.duration;

  const imageScale = scale / 100;
  const baseImageSize = Math.round(videoInfo.width * 0.25);
  const finalImageSize = Math.round(baseImageSize * imageScale);

  let imageX, imageY;
  if (image.coords?.videoCoords) {
    const centerX = image.coords.videoCoords.x;
    const centerY = image.coords.videoCoords.y;
    imageX = Math.round(centerX - (finalImageSize / 2));
    imageY = Math.round(centerY - (finalImageSize / 2));
  } else {
    const { posX = 50, posY = 50 } = imageOptions;
    const imageCoords = getMediaOverlayVideoCoords(posX, posY, videoInfo);
    imageX = Math.round(imageCoords.x - (finalImageSize / 2));
    imageY = Math.round(imageCoords.y - (finalImageSize / 2));
  }

  const nextLabel = `with_image_${imageIndex}`;
  let imageFilter = `[${inputIndex}:v]`;
  const enableExpr = getTimeEnableExpression(timeStart, timeEnd, videoInfo.duration, videoInfo.fps || 30);

  if(blendMode !== 'normal'){
    
  }
  // ⚙️ AutoFit logic
  if (autoFit) {
    const videoW = videoInfo.width;
    const videoH = videoInfo.height;

    const scaleFit = `scale='if(gt(a,${videoW}/${videoH}),${videoW},-1)':'if(gt(a,${videoW}/${videoH}),-1,${videoH})'`;
    const pad = `pad=${videoW}:${videoH}:(ow-iw)/2:(oh-ih)/2:color=0x00000000`; // transparent padding

    imageFilter += `${scaleFit},${pad}[image_${imageIndex}_autofit];`;
    imageFilter += `[${currentLabel}][image_${imageIndex}_autofit]overlay=enable='${enableExpr}'`;
  } else {
    // Normal transformation (scale, rotate, opacity)
    const scaleFilter = imageScale !== 1 ? `scale=${finalImageSize}:-1` : `scale=${baseImageSize}:-1`;
    const rotateFilter = rotation !== 0 ? `rotate=${rotation * Math.PI / 180}:c=none:ow=rotw(${rotation * Math.PI / 180}):oh=roth(${rotation * Math.PI / 180})` : '';
    const opacityFilter = opacity !== 100 ? `format=rgba,colorchannelmixer=aa=${opacity / 100}` : '';

    const transforms = [scaleFilter, rotateFilter, opacityFilter].filter(Boolean).join(',');
    if (transforms) {
      imageFilter += `${transforms}[image_${imageIndex}_transformed];`;
      imageFilter += `[${currentLabel}][image_${imageIndex}_transformed]overlay=x=${imageX}:y=${imageY}:enable='${enableExpr}'`;
    } else {
      imageFilter += `scale=${baseImageSize}:-1[image_${imageIndex}_normalized];`;
      imageFilter += `[${currentLabel}][image_${imageIndex}_normalized]overlay=x=${imageX}:y=${imageY}:enable='${enableExpr}'`;
    }
  }

  return {
    filter: `${imageFilter}[${nextLabel}];`,
    nextLabel: nextLabel
  };
}


// Helper function to apply a single text overlay
function applyTextOverlay(text, textIndex, videoInfo, currentLabel) {
  const textOptions = text.options || {};
  const {
    text: textContent = 'Sample Text',
    fontSize = 24,
    color = '#ffffff',
    backgroundColor = 'transparent',
    borderColor = '#000000',
    fontFamily = 'Arial',
    bold = false,
    posX = 50,
    posY = 10,
    rotation = 0,
    opacity = 100,
    align = 'center',
  } = textOptions;

  // Time controls
  const timeStart = textOptions.timeStart || 0;
  const timeEnd = textOptions.timeEnd || videoInfo.duration;

  // Use video coordinates from frontend if available, otherwise calculate from percentage
  let textX, textY;
  if (text.coords?.videoCoords) {
    textX = Math.round(text.coords.videoCoords.x);
    textY = Math.round(text.coords.videoCoords.y);
  } else {
    // Fallback to percentage calculation
    const textCoords = getMediaOverlayVideoCoords(posX, posY, videoInfo);
    textX = textCoords.x;
    textY = textCoords.y;
  }

  // Build font parameter for drawtext
  const fontPath = textOptions.fontPath;
  const fontParam = fontPath && !fontPath.includes('Arial') && !fontPath.includes('Helvetica')
    ? `:fontfile='${fontPath.replace('static', '.')}'`
    : '';

  // Build text style parameters
  const boldParam = bold ? ':bold=1' : '';

  // Border/shadow parameters for text outline
  let borderParam = '';
  if (borderColor !== 'transparent') {
    const borderColorHex = borderColor.replace('#', '');
    borderParam = `:shadowcolor=${borderColorHex}:shadowx=2:shadowy=2`;
  }

  // Calculate text position based on alignment
  let textXPos, textYPos;
  switch (align) {
    case 'left':
      textXPos = textX;
      textYPos = textY;
      break;
    case 'right':
      textXPos = `${textX}-text_w`;
      textYPos = textY;
      break;
    case 'center':
      textXPos = `${textX}-text_w/2`;
      textYPos = `${textY}-text_h/2`;
      break;
    default:
      textXPos = `${textX}-text_w/2`;
      textYPos = `${textY}-text_h/2`;
      break;
  }

  const nextLabel = `with_text_${textIndex}`;

  // Generate time-based enable expression
  const enableExpr = getTimeEnableExpression(timeStart, timeEnd, videoInfo.duration, videoInfo.fps || 30);

  // Build drawtext filter with automatic text wrapping
  const escapedText = escapeTextForFFmpeg(textContent, videoInfo.width, fontSize);

  let textFilter = `drawtext=text='${escapedText}':fontsize=${fontSize}:fontcolor=${color}@${opacity / 100}`;
  textFilter += `${fontParam}${boldParam}${borderParam}`;
  textFilter += `:x='${textXPos}':y='${textYPos}':enable='${enableExpr}'`;

  // Add thick background box if needed
  if (backgroundColor !== 'transparent') {
    const bgColor = backgroundColor.replace('#', '');

    // Calculate padding based on user settings or default values
    const paddingX = textOptions.paddingX || 0.6; // Default 0.6x font size
    const paddingY = textOptions.paddingY || 0.6; // Default 0.6x font size

    // Use average of X and Y padding for boxborderw (FFmpeg limitation)
    const avgPadding = (paddingX + paddingY) / 2;
    const paddingSize = Math.round(fontSize * avgPadding);

    // Use box parameter with user-controlled padding
    textFilter += `:box=1:boxcolor=${bgColor}@${opacity / 100}:boxborderw=${paddingSize}`;
  }

  if (rotation !== 0) {
    textFilter += `:angle=${rotation * Math.PI / 180}`;
  }
  
  return {
    filter: `[${currentLabel}]${textFilter}[${nextLabel}];`,
    nextLabel: nextLabel
  };
}

// Combined handler for blur, text animation, and media overlays - SINGLE FFmpeg pass
async function applyVideoEffects(event, videoInput, outputDir, blurAreas, textAnimation, textSubtitle, videoInfo, options) {
  // Check which effects are enabled
  const shouldApplyBlur = blurAreas.length > 0 && textSubtitle?.enabled;
  const isLutFileEnabled = options?.luts?.enabled
  const lutFilePath = options?.luts?.file
  const shouldApplyTextAnimation = textAnimation.enabled
  const { flipVideo, scaleFactor } = options.output || {};

  // Normalize overlays (support both old and new formats)
  const overlays = normalizeOverlays(options);

  // Check media overlay conditions
  const enabledLogos = overlays.logos.filter(logo => logo.enabled && logo.file && fs.existsSync(logo.file));
  const enabledImages = overlays.images.filter(image => image.enabled && image.file && fs.existsSync(image.file));
  const enabledTexts = overlays.texts.filter(text => text.enabled && text.options?.text);

  const shouldApplyLogo = enabledLogos.length > 0;
  const shouldApplyImage = enabledImages.length > 0;
  const shouldApplyFixedText = enabledTexts.length > 0;

  if (!shouldApplyBlur && !shouldApplyTextAnimation && !flipVideo && scaleFactor == 1 &&
      !shouldApplyLogo && !shouldApplyImage && !shouldApplyFixedText) {
    return videoInput; // No effects to apply
  }

  // console.log('Applying combined video effects in single FFmpeg pass');

  // Get flip video setting for text animation
  const {width, height} = options?.videoInfo || {}
  const flipFilter = flipVideo ? "hflip" : "";
  const zoom = scaleFactor
  const scaledW = Math.ceil(width * zoom);
  const scaledH = Math.ceil(height * zoom);
  // Use high-quality scaling algorithm for better results
  const zoomFilter = `scale=${scaledW}:${scaledH}:flags=lanczos,crop=${width}:${height}`;
  const videoFilters = [flipFilter, zoomFilter].filter(Boolean).join(",");
  // console.log(`🔄 FlipVideo setting: ${flipVideo}, options.output:`, options?.output);

  const outputFinal = path.basename(videoInput, path.extname(videoInput));
  const outputVideo = path.join(outputDir, `${outputFinal}_effects.mp4`);

  // Build combined filter complex
  let filterComplex = '';
  let currentLabel = '0:v'; // Start with input video stream

  // Add blur filters if enabled
  if (shouldApplyBlur) {
    const { fps, total_frames, duration } = videoInfo;
    const getFrameNumber = (timeInSeconds) => {
      const frame = Math.round(timeInSeconds * fps);
      return Math.min(frame, total_frames - 1);
    };

    blurAreas.forEach((box, index) => {
      const videoCoords = box.videoCoords;
      const startFrame = getFrameNumber(box.timeStart);
      const endFrame = box.timeEnd === duration ? total_frames - 1 : getFrameNumber(box.timeEnd);

      let enableExpr;
      if (box.timeStart === 0 && box.timeEnd === duration) {
        enableExpr = '1';
      } else if (box.timeStart === 0) {
        enableExpr = `lte(n,${endFrame})`;
      } else if (box.timeEnd === duration) {
        enableExpr = `gte(n,${startFrame})`;
      } else {
        enableExpr = `between(n,${startFrame},${endFrame})`;
      }

      const nextLabel = `blur${index}`;
      
      if (box.type === 'blur') {
        const cropLabel = `crop${index}`;
        // Split the current stream - one for cropping, one for overlay base
        filterComplex += `[${currentLabel}]split=2[base${index}][crop_input${index}];`;
        filterComplex += `[crop_input${index}]crop=w=${Math.round(videoCoords.width)}:h=${Math.round(videoCoords.height)}:x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)},boxblur=15[${cropLabel}];`;
        filterComplex += `[base${index}][${cropLabel}]overlay=x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)}:enable='${enableExpr}'[${nextLabel}];`;
      } else {
        // delogo filter
        filterComplex += `[${currentLabel}]delogo=enable='${enableExpr}':x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)}:w=${Math.round(videoCoords.width)}:h=${Math.round(videoCoords.height)}[${nextLabel}];`;
      }
      
      currentLabel = nextLabel;
    });
  }

  // Add video filters (flip, zoom) if enabled
  if (videoFilters) {
    const nextLabel = 'transformed';
    filterComplex += `[${currentLabel}]${videoFilters}[${nextLabel}];`;
    currentLabel = nextLabel;
  }
  // Add LUT filter if enabled
  if (isLutFileEnabled) {
    const lutFileName = path.basename(lutFilePath);
    const lutRelativePath = `./luts/${lutFileName}`;
    const nextLabel = 'with_lut';
    filterComplex += `[${currentLabel}]format=rgb24,lut3d='${lutRelativePath}'[${nextLabel}];`;
    currentLabel = nextLabel;
    console.log(`🎨 Applied LUT filter: ${lutFileName}`);
  }
  // Add text animation filter if enabled
  if (shouldApplyTextAnimation) {
    const { fontSize, color, value, opacity = 0.5, directionType = 'random', textSpeed = 100, fontPath } = textAnimation;
    const { duration } = videoInfo;

    const segment = textSpeed;
    const count = Math.ceil(duration / segment);
    let textFilters = '';
    const fadeTime = 1;

    // Build font parameter for drawtext
    const fontParam = fontPath && !fontPath.includes('Arial') && !fontPath.includes('Helvetica')
      ? `:fontfile='${fontPath.replace('static', '.')}'`
      : '';
    console.log('fontParam', fontParam);

    for (let i = 0; i < count; i++) {
      const start = i * segment;
      const end = Math.min((i + 1) * segment, duration);
      const { x, y } = generateDirectionExprSlow(start, segment, directionType);

      // Mirror text coordinates if video is flipped to keep text readable
      const textX = flipVideo ? `w-text_w-(${x})` : x;
      const textY = y;

      const alpha = `if(lt(t\\,${start}+${fadeTime}),(t-${start})/${fadeTime},if(lt(t\\,${end}-${fadeTime}),1,(${end}-t)/${fadeTime}))`;

      const escapedValue = escapeTextForFFmpeg(value, videoInfo.width, fontSize);

      textFilters += `drawtext=text='${escapedValue}':fontsize=${fontSize}:fontcolor=${color}@${opacity}:alpha='${alpha}'${fontParam}:`;
      textFilters += `x='${textX}':y='${textY}':enable='between(t\\,${start}\\,${end})',`;
    }

    if (!textFilters) {
      // Mirror fallback text coordinates if video is flipped to keep text readable
      const fallbackX = flipVideo ?
        `w-text_w-((w-text_w)*abs(mod(t\\,180)-90)/90)` :
        `(w-text_w)*abs(mod(t\\,180)-90)/90`;
      const escapedFallbackValue = escapeTextForFFmpeg(value, videoInfo.width, fontSize);

      textFilters = `drawtext=text='${escapedFallbackValue}':fontsize=${fontSize}:fontcolor=${color}@${opacity}${fontParam}:`;
      textFilters += `x='${fallbackX}':y='(h-text_h)*abs(mod(t\\,180)-90)/90'`;
    }

    textFilters = textFilters.replace(/,$/, '');
    const nextLabel = 'with_text';
    filterComplex += `[${currentLabel}]${textFilters}[${nextLabel}];`;
    currentLabel = nextLabel;
  }

  // Add media overlays
  let inputIndex = 1; // Start from 1 since 0 is the main video

  // Add logo overlays (support multiple logos)
  if (shouldApplyLogo) {
    enabledLogos.forEach((logo, logoIndex) => {
      const logoResult = applyLogoOverlay(logo, inputIndex, logoIndex, videoInfo, currentLabel);
      filterComplex += logoResult.filter;
      currentLabel = logoResult.nextLabel;
      inputIndex++;
    });
  }

  // Add image overlays (support multiple images)
  if (shouldApplyImage) {
    enabledImages.forEach((image, imageIndex) => {
      const imageResult = applyImageOverlay(image, inputIndex, imageIndex, videoInfo, currentLabel);
      filterComplex += imageResult.filter;
      currentLabel = imageResult.nextLabel;
      inputIndex++;
    });
  }

  // Add text overlays (support multiple texts)
  if (shouldApplyFixedText) {
    enabledTexts.forEach((text, textIndex) => {
      const textResult = applyTextOverlay(text, textIndex, videoInfo, currentLabel);
      filterComplex += textResult.filter;
      currentLabel = textResult.nextLabel;
    });
  }

  // Remove trailing semicolon and map to final output
  filterComplex = filterComplex.replace(/;$/, '');
  
  // Always map the final stream to 'vout' label for consistent output mapping
  if (currentLabel !== '0:v') {
    // If we applied effects, map the last label to vout
    filterComplex += `;[${currentLabel}]copy[vout]`;
  } else {
    // If no effects were applied (shouldn't happen due to early return), use direct mapping
    filterComplex = `[0:v]copy[vout]`;
  }

  // Write filter to file
  const filterFile = path.join(outputDir, 'combined_effects_filter.txt');
  fs.writeFileSync(filterFile, filterComplex);

  // Build input files array
  let inputFiles = [`-i "${videoInput}"`];

  // Add logo inputs if enabled (support multiple logos)
  if (shouldApplyLogo) {
    enabledLogos.forEach(logo => {
      inputFiles.push(`-i "${logo.file}"`);
    });
  }

  // Add image inputs if enabled (support multiple images)
  if (shouldApplyImage) {
    enabledImages.forEach(image => {
      inputFiles.push(`-i "${image.file}"`);
    });
  }

  // Single FFmpeg command for all effects with high-quality settings
  const encoder = await getEncoder();
  const inputsStr = inputFiles.join(' ');

  // Get encoding quality preference (default: balanced)
  const encodingQuality = options?.encoding?.quality || 'balanced'; // 'fast', 'balanced', 'high'

  // Build encoding parameters based on quality preference
  let encodingParams;
  if (encoder.includes('nvenc')) {
    // NVIDIA hardware encoding
    if (encodingQuality === 'fast') {
      encodingParams = `-c:v ${encoder} -preset fast -cq 20 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    } else if (encodingQuality === 'high') {
      encodingParams = `-c:v ${encoder} -preset slow -cq 15 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    } else {
      encodingParams = `-c:v ${encoder} -preset medium -cq 17 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    }
  } else if (encoder.includes('qsv')) {
    // Intel QSV hardware encoding
    if (encodingQuality === 'fast') {
      encodingParams = `-c:v ${encoder} -preset fast -global_quality 20 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    } else if (encodingQuality === 'high') {
      encodingParams = `-c:v ${encoder} -preset slow -global_quality 15 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    } else {
      encodingParams = `-c:v ${encoder} -preset medium -global_quality 17 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    }
  } else if (encoder.includes('amf')) {
    // AMD hardware encoding
    if (encodingQuality === 'fast') {
      encodingParams = `-c:v ${encoder} -quality speed -rc cqp -qp_i 20 -qp_p 22 -qp_b 24 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    } else if (encodingQuality === 'high') {
      encodingParams = `-c:v ${encoder} -quality quality -rc cqp -qp_i 15 -qp_p 17 -qp_b 19 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    } else {
      encodingParams = `-c:v ${encoder} -quality balanced -rc cqp -qp_i 17 -qp_p 19 -qp_b 21 -profile:v high -level 4.1 -pix_fmt yuv420p`;
    }
  } else {
    // Software encoding (libx264)
    if (encodingQuality === 'fast') {
      encodingParams = `-c:v ${encoder} -preset fast -crf 20 -profile:v high -level 4.1 -pix_fmt yuv420p -tune film`;
    } else if (encodingQuality === 'high') {
      encodingParams = `-c:v ${encoder} -preset slow -crf 15 -profile:v high -level 4.1 -pix_fmt yuv420p -tune film`;
    } else {
      encodingParams = `-c:v ${encoder} -preset medium -crf 17 -profile:v high -level 4.1 -pix_fmt yuv420p -tune film`;
    }
  }

  const cmd = `${ffmpegManager.ffmpegPath} ${inputsStr} -filter_complex_script "${filterFile}" -map "[vout]" -map 0:a? ${encodingParams} -c:a copy -movflags +faststart -y "${outputVideo}"`;

  // console.log('Filter complex:', filterComplex);
  console.log('FFmpeg command:', cmd);

  await execPromise(cmd, { cwd: outputDir });
  fs.unlinkSync(filterFile);

  console.log('✅ Combined effects applied in single pass:', outputVideo);
  return outputVideo;
}

async function processVideoWithOptions(event, renderConfig = {}) {
  try {
    let videoInput = renderConfig.srtPath.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
    const videoInfo = await getVideoInfo(event, videoInput);
    renderConfig.options.videoInfo = videoInfo;
    const outputDir = path.dirname(videoInput);
    const outputFinal = path.basename(videoInput, path.extname(videoInput)) + '_final.mp4';
    const outputVideo = path.join(outputDir, outputFinal);
    const holdMusicOnly = renderConfig?.options?.audio?.holdMusicOnly || false;
    const removeMusicOnly = renderConfig?.options?.audio?.removeMusicOnly || false;
    const isLutFileEnabled = renderConfig?.options?.luts?.enabled || false;
    const lutFilePath = renderConfig?.options?.luts?.file
    // const textAnimation = renderConfig?.options?.textAnimation?.enabled || false;
    const blurAreas = renderConfig?.blurAreas || [];
    const workDirTemp = path.join(outputDir, '_temp');
    if (!fs.existsSync(workDirTemp)) fs.mkdirSync(workDirTemp, { recursive: true });
    const staticFontsDir = path.join(STATIC_DIR, 'fonts');
    const tempFontsDir = path.join(workDirTemp, 'fonts');
    const tempLutsDir = path.join(workDirTemp, 'luts');

    if (isLutFileEnabled && fs.existsSync(lutFilePath)) {
      if (!fs.existsSync(tempLutsDir)) {
        fs.mkdirSync(tempLutsDir, { recursive: true });
      }
      
      // Lấy tên file từ đường dẫn gốc
      const lutFileName = path.basename(lutFilePath);
      const destLutPath = path.join(tempLutsDir, lutFileName);
      
      fs.copyFileSync(lutFilePath, destLutPath);
    }

    // Copy fonts to temp directory and create fontconfig
    if (fs.existsSync(staticFontsDir)) {
      // Ensure temp fonts directory exists
      if (!fs.existsSync(tempFontsDir)) {
        fs.mkdirSync(tempFontsDir, { recursive: true });
      }

      // Copy all fonts to temp directory
      const fontFiles = fs.readdirSync(staticFontsDir).filter(file =>
        file.toLowerCase().endsWith('.ttf') ||
        file.toLowerCase().endsWith('.otf') ||
        file.toLowerCase().endsWith('.woff') ||
        file.toLowerCase().endsWith('.woff2')
      );

      fontFiles.forEach(fontFile => {
        const sourcePath = path.join(staticFontsDir, fontFile);
        const destPath = path.join(tempFontsDir, fontFile);
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
        }
      });
    }
    if (holdMusicOnly) {
      console.log('Extracting music only');
      videoInput = await holdMusicHandler(event, videoInput, workDirTemp, 'no_vocals');
      console.log('Music only extracted', videoInput);
    }
    if (removeMusicOnly) {
      console.log('Extracting vocals only');
      videoInput = await holdMusicHandler(event, videoInput, workDirTemp, 'vocals');
      console.log('Music only extracted', videoInput);
    }
    // Apply video effects (blur and/or text animation) in one pass
    videoInput = await applyVideoEffects(
      event,
      videoInput,
      workDirTemp,
      blurAreas,
      renderConfig?.options?.textAnimation,
      renderConfig?.options.textSubtitle,
      videoInfo,
      renderConfig?.options
    );
    const result = await processVideoSimplified(
      event,
      videoInput,
      renderConfig.srtItems,
      workDirTemp,
      outputVideo,
      renderConfig.options,
    );
    // console.log('Result:', result);
    return result;
  } catch (error) {
    console.error('Error processing video with options:', error);
    throw error;
  }
}

module.exports = {
  processVideoWithOptions,
  escapeTextForFFmpeg,
  autoWrapText,
};
