<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test TTS Normalization</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-section h3 {
            color: #007bff;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .test-case {
            margin-bottom: 15px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .input {
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 5px;
        }
        .output {
            color: #28a745;
        }
        .custom-test {
            margin-top: 30px;
            padding: 20px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        .custom-test h3 {
            color: #856404;
            margin-top: 0;
        }
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test TTS Normalization Function</h1>
        
        <div class="test-section">
            <h3>📏 Test đơn vị đo lường (mét)</h3>
            <div id="meter-tests"></div>
        </div>

        <div class="test-section">
            <h3>👥 Test xưng hô</h3>
            <div id="pronoun-tests"></div>
        </div>

        <div class="test-section">
            <h3>🏙️ Test địa danh</h3>
            <div id="location-tests"></div>
        </div>

        <div class="test-section">
            <h3>💰 Test tiền tệ</h3>
            <div id="currency-tests"></div>
        </div>

        <div class="test-section">
            <h3>🔤 Test chữ cái đơn lẻ</h3>
            <div id="letter-tests"></div>
        </div>

        <div class="test-section">
            <h3>📱 Test từ viết tắt khác</h3>
            <div id="other-tests"></div>
        </div>

        <div class="custom-test">
            <h3>✏️ Test tùy chỉnh</h3>
            <textarea id="customInput" placeholder="Nhập văn bản để test..." rows="3"></textarea>
            <button onclick="testCustom()">Test ngay</button>
            <div id="customResult"></div>
        </div>
    </div>

    <script>
        const letterPronounceMap = {
            A: 'a',
            Ă: 'ă',
            Â: 'â',
            B: 'bê',
            C: 'xê',
            D: 'dê',
            Đ: 'đê',
            E: 'e',
            Ê: 'ê',
            G: 'gờ',
            H: 'hát',
            I: 'i',
            K: 'ca',
            L: 'lờ',
            M: 'mờ',
            N: 'nờ',
            O: 'o',
            Ô: 'ô',
            Ơ: 'ơ',
            P: 'pê',
            Q: 'quy',
            R: 'rờ',
            S: 'sờ',
            T: 'tê',
            U: 'u',
            Ư: 'ư',
            V: 'vê',
            X: 'ích',
            Y: 'i dài',
        };

        function normalizeSingleLetters(text) {
            return text.replace(/(?<=^|[^\p{L}])([A-ZĂÂĐÊÔƠƯ])(?=$|[^\p{L}])/gu, (match) => {
                return letterPronounceMap[match] || match;
            });
        }

        function normalizeTextForTTS(text) {
            const abbreviationMap = [
                { regex: /\b1m(\d{1,2})\b/gi, replace: (_, d) => `1 mét ${d}` },
                { regex: /\b(\d)m(\d{1,2})\b/gi, replace: (_, m, d) => `${m} mét ${d}` },
                { regex: /(?:^|[^a-zA-ZÀ-ỹ])ông\/bà(?:[^a-zA-ZÀ-ỹ]|$)/gi, replace: (match) => match.replace(/ông\/bà/i, 'ông bà') },
                { regex: /(?:^|[^a-zA-ZÀ-ỹ])anh\/chị(?:[^a-zA-ZÀ-ỹ]|$)/gi, replace: (match) => match.replace(/anh\/chị/i, 'anh chị') },
                { regex: /\bTP\.HCM\b/gi, replace: () => 'thành phố Hồ Chí Minh' },
                { regex: /\bHN\b/gi, replace: () => 'Hà Nội' },
                { regex: /\bCEO\b/gi, replace: () => 'xì i ô' },
                { regex: /\bVND\b/gi, replace: () => 'đồng' },
                { regex: /\bUSD\b/gi, replace: () => 'đô la Mỹ' },
                { regex: /\bTTS\b/gi, replace: () => 'ti vi ét tê ét ét ét' },
                { regex: /\btivi\b/gi, replace: () => 'ti vi' },
                { regex: /\bsos\b/gi, replace: () => 'ét ô ét' },
            ];
            let normalized = text;
            // Thay từ viết tắt trước
            for (const { regex, replace } of abbreviationMap) {
                normalized = normalized.replace(regex, replace);
            }
            // Xử lý chữ cái đơn lẻ sau
            normalized = normalizeSingleLetters(normalized);
            return normalized;
        }

        function runTest(input, containerId) {
            const result = normalizeTextForTTS(input);
            const testCase = document.createElement('div');
            testCase.className = 'test-case';
            testCase.innerHTML = `
                <div class="input">Input: "${input}"</div>
                <div class="output">Output: "${result}"</div>
            `;
            document.getElementById(containerId).appendChild(testCase);
        }

        function testCustom() {
            const input = document.getElementById('customInput').value;
            const resultDiv = document.getElementById('customResult');
            
            if (!input.trim()) {
                resultDiv.innerHTML = '<div class="result error">Vui lòng nhập văn bản để test!</div>';
                return;
            }
            
            try {
                const result = normalizeTextForTTS(input);
                resultDiv.innerHTML = `
                    <div class="result">
                        <strong>Kết quả:</strong><br>
                        "${result}"
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Lỗi: ${error.message}</div>`;
            }
        }

        // Chạy các test case
        window.onload = function() {
            // Test đơn vị mét
            const meterTests = [
                "Chiều cao 1m75",
                "Độ dài 2m50",
                "Khoảng cách 1m5",
                "Diện tích 3m90",
                "1m85 là chiều cao lý tưởng"
            ];
            meterTests.forEach(test => runTest(test, 'meter-tests'));

            // Test xưng hô
            const pronounTests = [
                "Xin chào ông/bà",
                "Kính gửi anh/chị",
                "ông/bà có khỏe không?",
                "anh/chị làm việc gì?",
                "Chúc ông/bà sức khỏe"
            ];
            pronounTests.forEach(test => runTest(test, 'pronoun-tests'));

            // Test địa danh
            const locationTests = [
                "Tôi sống ở TP.HCM",
                "HN rất đông đúc",
                "Từ TP.HCM đến HN",
                "TP.HCM và HN là 2 thành phố lớn"
            ];
            locationTests.forEach(test => runTest(test, 'location-tests'));

            // Test tiền tệ
            const currencyTests = [
                "Giá 100 USD",
                "Chi phí 5000 VND",
                "Tỷ giá USD/VND",
                "CEO kiếm nhiều USD"
            ];
            currencyTests.forEach(test => runTest(test, 'currency-tests'));

            // Test chữ cái đơn lẻ
            const letterTests = [
                "Câu A là đúng",
                "Chọn đáp án B",
                "Điểm X trên đồ thị",
                "Ô tô nhãn hiệu H",
                "Vitamin C rất tốt",
                "Tôi học lớp A, bạn học lớp B"
            ];
            letterTests.forEach(test => runTest(test, 'letter-tests'));

            // Test từ viết tắt khác
            const otherTests = [
                "CEO của công ty",
                "Xem TTS này",
                "Mua tivi mới",
                "Gọi SOS ngay",
                "CEO sử dụng TTS"
            ];
            otherTests.forEach(test => runTest(test, 'other-tests'));
        };
    </script>
</body>
</html>