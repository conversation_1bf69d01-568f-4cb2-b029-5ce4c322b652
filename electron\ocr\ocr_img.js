const Tesseract = require('tesseract.js');
const { <PERSON><PERSON> } = require('jimp')

// Enter the image path
const inputImagePath = './logs/149k.png';

// Load the image and preprocess it
Jimp.read(inputImagePath)
  .then(image => {
    return image
      .greyscale() // Grayscale the image
      .contrast(0.8) // Moderate contrast increase
      .brightness(0.1) // Slight brightness increase
      .normalize() // Normalize the image
      .write('preprocessedd.jpg');
  })
  .then(() => {
    // Use Tesseract.js to perform OCR recognition
    return Tesseract.recognize(
      'preprocessedd.jpg',
      'chi_sim+chi_tra', // Use both Simplified and Traditional Chinese
      {
        logger: m => console.log(m), // Record progress
        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK, // Better for single text blocks
        tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY, // Use LSTM engine for better accuracy
        tessedit_char_whitelist: '一二三四五六七八九十百千万亿零壹贰叁肆伍陆柒捌玖拾佰仟萬億〇的了是我你他她它们我们你们他们这那些什么怎么为什为何因为所以但是然而不过还有也都很非常特别尤其特殊普通一般平常正常异常奇怪古怪可怕恐怖害怕担心忧虑焦虑紧张放松轻松愉快高兴开心快乐幸福美好漂亮好看丑陋难看', // Common Chinese characters
      }
    );
  })
  .then(({ data: { text } }) => {
    console.log(text); // Output recognized text
  })
  .catch(err => {
    console.error(err);
  });
