const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const util = require('util');
const { getAudioDuration, getEncoder, getVideoInfo } = require('../ffmpegHandler');
const { generateASSSubtitle, clampAtTempo } = require('./assBuild');
const { addAudioToVideo, upVolume } = require('./addAudioToVideo');
const { ffmpegManager } = require('../ffmpeg-config');
const execPromise = util.promisify(exec);

// Function to validate custom font name for ASS subtitle
function validateFontName(fontFamily) {
  if (!fontFamily || fontFamily === 'Arial' || fontFamily === 'Helvetica') {
    return fontFamily; // System fonts are always valid
  }

  try {
    // Get fonts directory
    const fontsDir = path.join(STATIC_DIR, 'fonts');

    // Check if font file exists
    const fontFiles = fs.readdirSync(fontsDir).filter((file) => {
      const name = path
        .basename(file, path.extname(file))
        .replace(/[-_]/g, ' ')
        .replace(/\b\w/g, (l) => l.toUpperCase());
      return name === fontFamily;
    });

    if (fontFiles.length === 0) {
      console.log(`Font ${fontFamily} not found, using Arial`);
      return 'Arial';
    }

    console.log(`Font ${fontFamily} validated successfully`);
    return fontFamily;
  } catch (error) {
    console.error(`Error validating font ${fontFamily}:`, error);
    return 'Arial'; // Fallback to Arial
  }
}

const BATCH_SIZE = 20; // Giảm batch size để tránh memory overflow với nhiều audio inputs

// Helper function để xử lý batch với retry mechanism
async function processBatchWithRetry(
  event,
  batch,
  batchIndex,
  videoPath,
  outputDir,
  options,
  totalBatches,
  totalVideoDuration,
  totalProcessedTime,
  batchStartTime = 0,
) {
  const maxRetries = 2;
  const retrySizes = [Math.ceil(batch.length / 2), Math.ceil(batch.length / 3)]; // Chia nhỏ batch khi retry

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt === 0) {
        // Lần đầu xử lý batch bình thường
        return await processSingleBatch(
          event,
          batch,
          batchIndex,
          videoPath,
          outputDir,
          options,
          totalBatches,
          totalVideoDuration,
          totalProcessedTime,
          batchStartTime,
        );
      } else {
        // Retry với batch size nhỏ hơn
        const smallerBatchSize = retrySizes[attempt - 1];
        console.log(
          `🔄 Retry ${attempt}: Splitting batch ${batchIndex + 1} into smaller chunks of ${smallerBatchSize} items`,
        );

        const subBatches = [];
        for (let i = 0; i < batch.length; i += smallerBatchSize) {
          subBatches.push(batch.slice(i, i + smallerBatchSize));
        }

        const subBatchPaths = [];
        for (let subIndex = 0; subIndex < subBatches.length; subIndex++) {
          const subBatch = subBatches[subIndex];
          const subBatchIndex = `${batchIndex}_${subIndex}`;
          // ====== FIX: Calculate subBatchStartTime for sub-batches ======
          const subBatchStartTime = subBatch.length > 0 ? subBatch[0].startTime : batchStartTime;
          const subBatchPath = await processSingleBatch(
            event,
            subBatch,
            subBatchIndex,
            videoPath,
            outputDir,
            options,
            totalBatches,
            totalVideoDuration,
            totalProcessedTime,
            subBatchStartTime,
          );
          subBatchPaths.push(subBatchPath);
        }

        // Concatenate sub-batches
        if (subBatchPaths.length > 1) {
          const concatListPath = path.join(outputDir, `concat_batch_${batchIndex}.txt`);
          const concatListContent = subBatchPaths.map((p) => `file '${p}'`).join('\n');
          fs.writeFileSync(concatListPath, concatListContent);

          const finalBatchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);
          // const encoder = await getEncoder();
          const concatCmd = `${ffmpegManager.ffmpegPath} -f concat -safe 0 -i "${concatListPath}" -c:a aac -b:a 256k -ar 48000 -ac 2 -movflags +faststart -y "${finalBatchOutput}"`;
          await execPromise(concatCmd);
          return finalBatchOutput;
        } else {
          return subBatchPaths[0];
        }
      }
    } catch (error) {
      const isMemoryError =
        error.message.includes('Cannot allocate memory') ||
        error.message.includes('out of memory') ||
        error.message.includes('memory allocation');

      if (isMemoryError && attempt < maxRetries) {
        console.log(
          `⚠️ Memory error in batch ${batchIndex + 1}, attempt ${attempt + 1}. Retrying with smaller batch size...`,
        );
        event?.sender?.send('video-task', {
          data: `⚠️ Memory error in batch ${batchIndex + 1}, retrying with smaller chunks...`,
          code: 0,
        });
        continue;
      } else {
        console.error(`❌ Batch ${batchIndex + 1} failed after ${attempt + 1} attempts:`, error.message);
        throw error;
      }
    }
  }
}

const resolutionMap = {
  '144p': 144,
  '240p': 240,
  '360p': 360,
  '480p': 480,
  '720p': 720,
  '1080p': 1080,
  '1440p': 1440,
  '2160p': 2160,
  '4k': 2160,
};

function getResolution(options) {
  const quality = options?.output?.quality || 'custom';
  const resolution = options?.videoInfo;
  const defaultResolution = { width: resolution.width, height: resolution.height };
  if (quality === 'custom') return defaultResolution;
  if (!quality) return defaultResolution;

  const [qualityRaw, aspectRaw = '16:9'] = quality.toLowerCase().split('/');
  const qualityKey = /^\d+$/.test(qualityRaw) ? qualityRaw + 'p' : qualityRaw;

  const size = resolutionMap[qualityKey];
  if (!size) return null;

  const [wRatio, hRatio] = aspectRaw.split(':').map(Number);
  if (!wRatio || !hRatio) return null;

  let width, height;

  if (wRatio > hRatio) {
    // Landscape → height = size
    height = size;
    width = Math.round((height * wRatio) / hRatio);
  } else {
    // Portrait → width = size
    width = size;
    height = Math.round((width * hRatio) / wRatio);
  }

  return { width, height };
}

function buildBatchAndSrt(event, srtArray, BATCH_SIZE) {
  let adjustedCurrentTime = 0; // Thời gian tích luỹ sau khi điều chỉnh tốc độ

  console.log(`🔧 buildBatchAndSrt: Processing ${srtArray.length} segments`);

  for (let i = 0; i < srtArray.length; i++) {
    const currentSrt = srtArray[i];
    const nextSrt = srtArray[i + 1];

    // Thời lượng video segment gốc
    const originalVideoSegmentDuration = nextSrt
      ? nextSrt.startTime - currentSrt.startTime
      : currentSrt.endTime - currentSrt.startTime;

    // Ensure originalVideoSegmentDuration is valid and positive
    if (isNaN(originalVideoSegmentDuration) || originalVideoSegmentDuration <= 0) {
      console.warn(
        `⚠️ Invalid originalVideoSegmentDuration for segment ${i}: ${originalVideoSegmentDuration}, setting to 0.1s`,
      );
      currentSrt.originalVideoSegmentDuration = 0.1;
    } else {
      currentSrt.originalVideoSegmentDuration = originalVideoSegmentDuration;
    }

    // Tính speed ratio using the corrected originalVideoSegmentDuration
    if (currentSrt.duration > currentSrt.originalVideoSegmentDuration) {
      currentSrt.speedRatio = currentSrt.originalVideoSegmentDuration / currentSrt.duration; // < 1 = chậm lại
    } else {
      currentSrt.speedRatio = 1; // không thay đổi tốc độ
    }

    // Thời lượng thực tế sau khi điều chỉnh tốc độ
    const adjustedVideoDuration = currentSrt.originalVideoSegmentDuration / currentSrt.speedRatio;

    // Thời lượng cuối cùng của đoạn này (lấy max giữa video đã điều chỉnh và audio)
    // Handle edge cases where durations might be 0 or invalid
    const validAdjustedVideoDuration =
      isNaN(adjustedVideoDuration) || adjustedVideoDuration <= 0 ? currentSrt.duration : adjustedVideoDuration;
    const validAudioDuration = isNaN(currentSrt.duration) || currentSrt.duration <= 0 ? 0.1 : currentSrt.duration; // Minimum 0.1s

    currentSrt.finalDuration = Math.max(validAdjustedVideoDuration, validAudioDuration);

    // Ensure finalDuration is never NaN or 0
    if (isNaN(currentSrt.finalDuration) || currentSrt.finalDuration <= 0) {
      console.warn(`⚠️ Invalid finalDuration for segment ${i}, setting to 0.1s`);
      currentSrt.finalDuration = 0.1;
    }

    // ====== REVERT: Giữ nguyên logic cũ cho subtitle timing ======
    // Cập nhật thời gian start/end mới cho SRT (không tính initial offset ở đây)
    currentSrt.adjustedStartTime = adjustedCurrentTime;
    currentSrt.adjustedEndTime = adjustedCurrentTime + currentSrt.finalDuration;

    // Cập nhật thời gian tích luỹ
    adjustedCurrentTime += currentSrt.finalDuration;
  }

  const batches = [];
  for (let i = 0; i < srtArray.length; i += BATCH_SIZE) {
    batches.push(srtArray.slice(i, i + BATCH_SIZE));
  }
  return batches;
}

// Tách phần xử lý batch thành hàm riêng để có thể retry
async function processSingleBatch(
  event,
  batch,
  batchIndex,
  videoPath,
  outputDir,
  options,
  totalBatches,
  totalVideoDuration,
  totalProcessedTime,
  batchStartTime = 0,
) {
  const type = 'video-task';
  const holdMusicOnly = options?.audio?.holdMusicOnly;
  const removeMusicOnly = options?.audio?.removeMusicOnly;
  const holdOriginalAudio = options?.audio?.holdOriginalAudio || holdMusicOnly || removeMusicOnly || false;
  const originalAudioVolume = options?.audio?.originalVolume || 0.8;
  // slientOriginalAudio nếu true thì nhỏ tiếng gốc lại
  const slientOriginalAudio = options?.audio?.slientOriginalAudio || false;
  const downOriginalAudioVolume = options?.audio?.downOriginalAudioVolume || 0.1;
  const addVoiceAudio = true;
  const voiceVolumeLevel = downOriginalAudioVolume;
  const holdLastAudio = options?.audio?.holdLastAudio;

  const inputs = [`-i "${videoPath}"`];
  let segmentFilters = [];

  // ====== FIX: Use separate timing for video timeline and audio delay ======
  let currentTime = 0; // For video timeline within batch
  let audioDelayTime = 0; // For audio synchronization delay

  let videoSegments = [];
  let originalAudioSegments = [];
  let voiceAudioSegments = [];

  // Add audio files as inputs and track mapping
  const audioInputMapping = new Map(); // Map SRT index to audio input index
  if (addVoiceAudio) {
    batch.forEach((srt, index) => {
      if (srt.audioUrl) {
        const audioPath = srt.audioUrl.replace('file://', '');
        if (fs.existsSync(audioPath)) {
          inputs.push(`-i "${audioPath}"`);
          audioInputMapping.set(index, inputs.length - 1); // Store the actual input index
        } else {
          console.warn(`⚠️ Audio file not found for SRT ${index}: ${audioPath}`);
        }
      } else {
        console.warn(`⚠️ No audioUrl for SRT ${index}`);
      }
    });
  }

  // ====== FIX: Add initial video segment if first SRT doesn't start at 0 ======
  const firstSrt = batch[0];
  const isFirstBatch = batchIndex === 0;

  if (isFirstBatch && firstSrt && firstSrt.startTime > 0.1) {
    const initialDuration = firstSrt.startTime;
    console.log(
      `📺 Adding initial video segment: 0s to ${firstSrt.startTime.toFixed(2)}s (${initialDuration.toFixed(2)}s)`,
    );
    // Add initial video segment
    let initialVideoFilter = `[0:v]trim=start=0:duration=${initialDuration.toFixed(6)},setpts=PTS-STARTPTS`;
    if (options.output?.quality) {
      const resolution = getResolution(options);
      initialVideoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    initialVideoFilter += `[v_initial]`;
    segmentFilters.push(initialVideoFilter);
    videoSegments.push(`[v_initial]`);

    // Add initial audio if needed
    if (holdOriginalAudio) {
      let initialAudioFilter = `[0:a]atrim=start=0:duration=${initialDuration.toFixed(6)},asetpts=PTS-STARTPTS`;
      initialAudioFilter += `,volume=${holdLastAudio ? 0 : originalAudioVolume}`;

      initialAudioFilter += `[oa_initial]`;
      segmentFilters.push(initialAudioFilter);
      originalAudioSegments.push(`[oa_initial]`);
    }

    if (addVoiceAudio) {
      // Generate silence for initial duration
      let silentAudioFilter = `aevalsrc=0:duration=${initialDuration.toFixed(
        6,
      )}:sample_rate=44100:channel_layout=stereo[va_initial]`;
      segmentFilters.push(silentAudioFilter);
      voiceAudioSegments.push(`[va_initial]`);
    }

    currentTime += initialDuration;
    audioDelayTime += initialDuration;
  }

  batch.forEach((srt, index) => {
    const vStart = srt.startTime;
    const vDur = srt.originalVideoSegmentDuration;
    const speedRatio = srt.speedRatio || 1;
    const finalDur = srt.finalDuration;

    // Debug timing for synchronization issues
    // console.log(`🎬 Batch ${batchIndex + 1}, Segment ${index + 1}: vStart=${vStart.toFixed(2)}s, vDur=${vDur.toFixed(2)}s, finalDur=${finalDur.toFixed(2)}s, audioDelay=${audioDelayTime.toFixed(2)}s`);

    // === VIDEO PROCESSING ===
    // Cắt video từ thời điểm startTime với duration chính xác
    let videoFilter = `[0:v]trim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(6)},setpts=PTS-STARTPTS`;
    if (speedRatio < 1) {
      videoFilter += `,setpts=PTS/${speedRatio.toFixed(6)}`;
    }
    if (options.output?.quality) {
      const resolution = getResolution(options);
      options.output.resolution = resolution;
      // Use high-quality scaling algorithm
      videoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}:flags=lanczos`;
    }
    videoFilter += `[v${index}]`;
    segmentFilters.push(videoFilter);
    videoSegments.push(`[v${index}]`);

    // === ORIGINAL AUDIO PROCESSING ===
    if (holdOriginalAudio) {
      // Cắt audio từ thời điểm startTime với duration chính xác
      let volumeEnable = '';
      if (!holdMusicOnly) {
        const fadeInDuration = 0.9; // thời gian fade in (có thể điều chỉnh)
        const fadeOutDuration = 0.9; // thời gian fade out (có thể điều chỉnh)
        // DÙNG VOLUME EXPRESSION NHƯ MEDIA-SDK - SMOOTH MATHEMATICAL TRANSITIONS
        const duckVolume = holdLastAudio ? 0 : voiceVolumeLevel;
        const normalVolume = holdLastAudio ? 0 : originalAudioVolume;

        // Tạo smooth volume expression như media-sdk (relative to segment start = 0)
        const startFade = 0;
        const endFade = fadeInDuration;
        const startRestore = vDur - fadeOutDuration;
        const endRestore = vDur;

        let volumeExpr;
        if (vDur > fadeInDuration + fadeOutDuration + 0.01) {
          // Đủ dài - có smooth transitions: normal → duck → normal
          volumeExpr =
            `if(between(t,${startFade},${endFade}),` +
            `${normalVolume}-(${normalVolume}-${duckVolume})*(t-${startFade})/${fadeInDuration},` +
            `if(between(t,${endFade},${startRestore}),${duckVolume},` +
            `if(between(t,${startRestore},${endRestore}),` +
            `${duckVolume}+(${normalVolume}-${duckVolume})*(t-${startRestore})/${fadeOutDuration},` +
            `${normalVolume})))`;
        } else {
          // Quá ngắn - volume cố định
          volumeExpr = `${duckVolume}`;
        }

        volumeEnable = `volume='${volumeExpr}':eval=frame,`;
      }
      let originalAudioFilter = `[0:a]${volumeEnable}atrim=start=${vStart.toFixed(6)}:duration=${vDur.toFixed(
        6,
      )},asetpts=PTS-STARTPTS`;

      // Improved tempo adjustment with smoother transitions
      if (speedRatio < 1) {
        let currentRatio = speedRatio;
        let tempoFilters = [];

        // Break down large tempo changes into smaller steps for better quality
        while (currentRatio < 0.5) {
          tempoFilters.push('atempo=0.5');
          currentRatio /= 0.5;
        }
        while (currentRatio > 2.0) {
          tempoFilters.push('atempo=2.0');
          currentRatio /= 2.0;
        }
        if (Math.abs(currentRatio - 1.0) > 0.001) {
          tempoFilters.push(`atempo=${currentRatio.toFixed(6)}`);
        }

        if (tempoFilters.length > 0) {
          originalAudioFilter += `,${tempoFilters.join(',')}`;
        }
      }

      // Tạo volume filter để giảm audio gốc tại các đoạn có voice
      let volumeFilter = '';

      // Duyệt qua tất cả segments để tìm những đoạn có voice

      if (!holdMusicOnly) {
      } else {
        // Không có voice segments, dùng volume bình thường
        volumeFilter = `,volume=${originalAudioVolume}`;
      }

      originalAudioFilter += volumeFilter;
      originalAudioFilter += `,adelay=${audioDelayTime * 1000}|${audioDelayTime * 1000}[oa${index}]`;

      segmentFilters.push(originalAudioFilter);
      originalAudioSegments.push(`[oa${index}]`);
    }

    // === IMPROVED VOICE AUDIO PROCESSING ===
    if (addVoiceAudio && audioInputMapping.has(index)) {
      const voiceAudioIndex = audioInputMapping.get(index); // Use correct mapped input index

      // HIGH-QUALITY VOICE PROCESSING
      let voiceAudioFilter = `[${voiceAudioIndex}:a]`;

      // Get voice enhancement level from options
      const voiceQuality = options?.audio?.voiceQuality || 'medium'; // 'basic', 'medium', 'high'
      const voiceFilters = [];

      if (voiceQuality === 'basic') {
        // Basic: just gentle volume boost
        voiceFilters.push('volume=1.1'); // 10% boost
      } else if (voiceQuality === 'medium') {
        // Medium: noise reduction + EQ + compression
        voiceFilters.push('afftdn=nr=8:nf=-25'); // Gentle noise reduction
        voiceFilters.push('equalizer=f=1000:width_type=h:width=500:g=1.5'); // Boost clarity
        voiceFilters.push('acompressor=threshold=-20dB:ratio=2.5:attack=10:release=80:makeup=1.5');
        voiceFilters.push('volume=1.15'); // 15% boost
      } else if (voiceQuality === 'high') {
        // High: full processing pipeline
        voiceFilters.push('afftdn=nr=12:nf=-20'); // More aggressive noise reduction
        voiceFilters.push('equalizer=f=800:width_type=h:width=400:g=1'); // Warmth
        voiceFilters.push('equalizer=f=1200:width_type=h:width=600:g=2'); // Clarity
        voiceFilters.push('equalizer=f=3000:width_type=h:width=1000:g=1.5'); // Presence
        voiceFilters.push('acompressor=threshold=-18dB:ratio=3:attack=5:release=50:makeup=2');
        voiceFilters.push('aecho=0.8:0.88:15:0.08'); // Very subtle reverb
        voiceFilters.push('volume=1.2'); // 20% boost
      }

      if (voiceFilters.length > 0) {
        voiceAudioFilter += voiceFilters.join(',');
      } else {
        voiceAudioFilter += 'anull'; // Pass-through if no enhancement
      }

      // console.log(`🎤 Voice quality: ${voiceQuality} (${voiceFilters.length} filters)`);
      // Duration adjustment with quality preservation
      const durationDiff = Math.abs(srt.duration - finalDur);
      if (srt.duration > finalDur && durationDiff > 0.01) {
        // Trim with fade-out to avoid clicks
        voiceAudioFilter += `,atrim=duration=${finalDur.toFixed(6)},afade=t=out:st=${Math.max(
          0,
          finalDur - 0.05,
        ).toFixed(6)}:d=0.05`;
      } else if (srt.duration < finalDur && durationDiff > 0.01) {
        const padDuration = finalDur - srt.duration;
        // Pad with silence
        voiceAudioFilter += `,apad=pad_dur=${padDuration.toFixed(6)}`;
      }

      // Add gentle fade-in/out to prevent clicks (very short)
      if (srt.duration > 0.1) {
        voiceAudioFilter += `,afade=t=in:d=0.02,afade=t=out:st=${Math.max(0, srt.duration - 0.02).toFixed(6)}:d=0.02`;
      }

      voiceAudioFilter += `,adelay=${audioDelayTime * 1000}|${audioDelayTime * 1000}[va${index}]`;
      segmentFilters.push(voiceAudioFilter);
      voiceAudioSegments.push(`[va${index}]`);
    } else if (addVoiceAudio) {
      console.warn(`⚠️ Skipping voice audio for SRT ${index} - no audio mapping`);
    }

    currentTime += finalDur;
    audioDelayTime += finalDur;
  });

  // ====== FIX: Only add remaining video in the LAST batch ======
  const isLastBatch = batchIndex === totalBatches - 1;

  // Calculate the actual end time of this batch
  let batchEndTime = 0;
  if (batch.length > 0) {
    const lastSrtInBatch = batch[batch.length - 1];
    batchEndTime = lastSrtInBatch.startTime + lastSrtInBatch.originalVideoSegmentDuration;
  }

  if (isLastBatch && batchEndTime < totalVideoDuration) {
    const remainingStart = batchEndTime; // Use batch end time instead of totalProcessedTime
    const remainingDuration = totalVideoDuration - batchEndTime;

    // ====== FIX: Only add remaining segment if duration is significant (> 0.1s) ======
    if (remainingDuration > 0.1) {
      console.log(
        `📺 Batch ${batchIndex + 1} (LAST): Adding remaining video: ${remainingStart.toFixed(
          2,
        )}s to ${totalVideoDuration.toFixed(2)}s (${remainingDuration.toFixed(2)}s)`,
      );
    } else {
      console.log(
        `📺 Batch ${batchIndex + 1} (LAST): Skipping remaining segment (too short: ${remainingDuration.toFixed(3)}s)`,
      );
    }
  }

  if (isLastBatch && batchEndTime < totalVideoDuration && totalVideoDuration - batchEndTime > 0.1) {
    const remainingStart = batchEndTime;
    const remainingDuration = totalVideoDuration - batchEndTime;

    // Add remaining video segment
    const remainingIndex = batch.length;
    let remainingVideoFilter = `[0:v]trim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(
      6,
    )},setpts=PTS-STARTPTS`;

    if (options.output?.quality) {
      const resolution = getResolution(options);
      remainingVideoFilter += `,scale=${resolution.width || 1920}:${resolution.height || 1080}`;
    }
    remainingVideoFilter += `[v${remainingIndex}]`;
    segmentFilters.push(remainingVideoFilter);
    videoSegments.push(`[v${remainingIndex}]`);

    // Add audio for remaining video if needed
    if (holdOriginalAudio) {
      let remainingAudioFilter = `[0:a]atrim=start=${remainingStart.toFixed(6)}:duration=${remainingDuration.toFixed(
        6,
      )},asetpts=PTS-STARTPTS`;
      remainingAudioFilter += `,volume=${originalAudioVolume}`;

      // ====== FIX: Use audioDelayTime for remaining segment synchronization ======
      remainingAudioFilter += `,adelay=${audioDelayTime * 1000}|${audioDelayTime * 1000}[oa${remainingIndex}]`;
      segmentFilters.push(remainingAudioFilter);
      originalAudioSegments.push(`[oa${remainingIndex}]`);
    }

    if (addVoiceAudio) {
      // Generate high-quality silence for remaining duration
      let silentAudioFilter = `aevalsrc=0:duration=${remainingDuration.toFixed(
        6,
      )}:sample_rate=44100:channel_layout=stereo`;
      // Use audioDelayTime for proper synchronization
      silentAudioFilter += `,adelay=${audioDelayTime * 1000}|${audioDelayTime * 1000}[va${remainingIndex}]`;
      segmentFilters.push(silentAudioFilter);
      voiceAudioSegments.push(`[va${remainingIndex}]`);
    }
    currentTime += remainingDuration;
    audioDelayTime += remainingDuration;
  }
  let adjustedSrtPath = `subtitles_${batchIndex}.ass`;
  if (options.textSubtitle?.enabled) {
    const textSubtitle = options.textSubtitle;
    // Validate custom font name for ASS subtitle
    const fontName = validateFontName(textSubtitle.fontFamily || 'Arial');

    const subtitleOptions = {
      fontSize: textSubtitle.fontSize || 48,
      fontName: fontName,
      // textColor: cssToASSColor(textSubtitle.color || '#000000'),
      textColor: textSubtitle.assColors?.text || '&H000000',
      backgroundColor: textSubtitle.assColors?.background || '&H000000',
      outlineColor: textSubtitle.assColors?.border || '&H000000',
      // backgroundColor: cssToASSColor(textSubtitle.backgroundColor || '#fff700', '00'),
      borderStyle: 4,
      bold: textSubtitle.bold || true,
      addPadding: true,
      alignment: 2,
      marginVertical: 50,
      resolution: options.output.resolution,
      assOptions: textSubtitle.assOptions,
    };
    const adjustedSrtArray = subAdjusted(batch, options, batchStartTime);
    // Thông báo cho UI về việc chia subtitle
    if (adjustedSrtArray.length > batch.length) {
      const splitCount = adjustedSrtArray.length - batch.length;
      event?.sender?.send('video-task', {
        data: `📝 Split ${splitCount} long subtitles (>${options.textSubtitle?.subInLine} words) into smaller chunks`,
        code: 0,
      });
    }

    const assContent = generateASSSubtitle(adjustedSrtArray, subtitleOptions);
    const filePath = path.join(outputDir, adjustedSrtPath);
    fs.writeFileSync(filePath, assContent, 'utf8');
    // console.log(`📝 Generated ASS file for batch ${batchIndex}: ${adjustedSrtPath} with ${adjustedSrtArray.length} segments`);
    const tempFontsDir = path.join(outputDir, 'fonts');
    // Copy fonts to temp directory and create fontconfig
    if (fs.existsSync(tempFontsDir)) {
      adjustedSrtPath += `:fontsdir=./fonts/`;
    }
  }
  // === CONCAT VIDEO ===
  const totalSegments = videoSegments.length;
  let assFile = '';
  if (options.textSubtitle?.enabled) assFile = `,ass='${adjustedSrtPath}'`;
  const videoConcat = `${videoSegments.join('')}concat=n=${totalSegments}:v=1:a=0${assFile}[vout]`;
  segmentFilters.push(videoConcat);

  // === IMPROVED AUDIO MIXING ===
  let audioMix = '';
  if (holdOriginalAudio && addVoiceAudio) {
    // Mix all audio segments (including initial and remaining if present)
    const allAudioTags = [];
    const weights = [];

    // Adjust mixing weights based on slientOriginalAudio option
    const originalWeight = slientOriginalAudio ? '0.3' : '0.8'; // Lower if silencing original
    const voiceWeight = '1.1'; // Keep voice strong

    // Handle initial segment if present
    if (originalAudioSegments.includes('[oa_initial]') && voiceAudioSegments.includes('[va_initial]')) {
      allAudioTags.push('[oa_initial][va_initial]');
      weights.push(`${originalWeight} ${voiceWeight}`);
    }

    // Handle regular segments
    for (let i = 0; i < batch.length; i++) {
      if (originalAudioSegments.includes(`[oa${i}]`) && voiceAudioSegments.includes(`[va${i}]`)) {
        allAudioTags.push(`[oa${i}][va${i}]`);
        weights.push(`${originalWeight} ${voiceWeight}`); // Balanced weights based on options
      }
    }

    // Handle remaining segment if present
    const remainingIndex = batch.length;
    if (
      originalAudioSegments.includes(`[oa${remainingIndex}]`) &&
      voiceAudioSegments.includes(`[va${remainingIndex}]`)
    ) {
      allAudioTags.push(`[oa${remainingIndex}][va${remainingIndex}]`);
      weights.push(`${originalWeight} ${voiceWeight}`);
    }

    if (allAudioTags.length > 0) {
      // Mix with balanced weights and add volume compensation
      // audioMix = `${allAudioTags.join('')}amix=inputs=${allAudioTags.length * 2}:duration=longest:weights=${weights.join(' ')}:normalize=0[aout]`;
      audioMix = `${allAudioTags.join('')}amix=inputs=${allAudioTags.length * 2}:duration=longest:normalize=0[aout]`;
      // Volume compensation to maintain good audio levels
    }
  } else if (holdOriginalAudio) {
    const originalAudioTags = originalAudioSegments.join('');
    audioMix = `${originalAudioTags}amix=inputs=${originalAudioSegments.length}:duration=longest:normalize=0[aout]`;
  } else if (addVoiceAudio) {
    // Use amix but with normalize=0 to prevent volume changes
    const voiceAudioTags = voiceAudioSegments.join('');
    audioMix = `${voiceAudioTags}amix=inputs=${voiceAudioSegments.length}:duration=longest:normalize=0[aout]`;
  }

  if (audioMix) segmentFilters.push(audioMix);

  // Rest of the code remains the same...
  const filterComplex = segmentFilters.join('; ');
  const batchOutput = path.join(outputDir, `batch_${batchIndex}.mp4`);

  const filterFile = path.join(outputDir, `filter_${batchIndex}.txt`);
  fs.writeFileSync(filterFile, filterComplex);

  // === IMPROVED FFMPEG COMMAND ===
  let ffmpegCmd = `${ffmpegManager.ffmpegPath} -v error ${inputs.join(
    ' ',
  )} -filter_complex_script "${filterFile}" -map "[vout]"`;
  if (audioMix) {
    // Higher quality audio encoding
    ffmpegCmd += ` -map "[aout]" -c:a aac -b:a 256k -ar 48000 -ac 2`;
  } else {
    ffmpegCmd += ` -an`;
  }
  // High-quality encoding settings
  const encoder = await getEncoder();

  // Build balanced quality/speed encoding parameters
  let encodingParams;
  if (encoder.includes('nvenc')) {
    // NVIDIA hardware encoding - balanced quality/speed
    encodingParams = `-c:v ${encoder} -preset medium -cq 18 -profile:v high -level 4.1 -pix_fmt yuv420p`;
  } else if (encoder.includes('qsv')) {
    // Intel QSV hardware encoding - balanced
    encodingParams = `-c:v ${encoder} -preset medium -global_quality 18 -profile:v high -level 4.1 -pix_fmt yuv420p`;
  } else if (encoder.includes('amf')) {
    // AMD hardware encoding - balanced
    encodingParams = `-c:v ${encoder} -quality balanced -rc cqp -qp_i 18 -qp_p 20 -qp_b 22 -profile:v high -level 4.1 -pix_fmt yuv420p`;
  } else {
    // Software encoding (libx264) - balanced quality/speed
    encodingParams = `-c:v ${encoder} -preset medium -crf 18 -profile:v high -level 4.1 -pix_fmt yuv420p -tune film`;
  }

  ffmpegCmd += ` ${encodingParams} -movflags +faststart -y "${batchOutput}"`;

  console.log(`🧩 Running FFmpeg for batch ${batchIndex + 1}/${totalBatches}, encoder: ${encoder}`);

  event?.sender?.send('video-task', {
    data: `🧩 Running FFmpeg for batch ${batchIndex + 1}/${totalBatches} (${currentTime.toFixed(2)}s)`,
    code: 0,
  });

  try {
    await execPromise(ffmpegCmd, {
      cwd: outputDir,
      maxBuffer: 50 * 1024 * 1024, // 50MB buffer để tránh overflow
    });
    // fs.unlinkSync(filterFile);
    return batchOutput;
  } catch (error) {
    console.error(`❌ Batch ${batchIndex + 1} processing failed:`, error.message);
    throw error;
  }
}

const processVideoSimplified = async (event, videoPath, srtArray, outputDir, finalOutput, options = {}) => {
  const workDirTemp = path.join(outputDir, '_temp');
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir, { recursive: true });
  const type = 'video-task';
  const totalVideoDuration = options?.videoInfo?.duration || 0;

  // 1. Calculate audio duration for each segment
  for (const srt of srtArray) {
    if (srt.audioUrl && fs.existsSync(srt.audioUrl.replace('file://', ''))) {
      srt.duration = await getAudioDuration(srt.audioUrl.replace('file://', ''));
      event?.sender?.send(type, {
        data: `🕐 Audio duration for segment ${srt.index}: ${srt.duration} seconds`,
        code: 0,
      });
    }
  }

  // 2. Calculate video segment duration and speed ratio (assumed handled by buildBatchAndSrt)
  const batches = buildBatchAndSrt(event, srtArray, BATCH_SIZE);
  const batchVideoPaths = [];

  // ====== FIX: Calculate total processed time correctly ======
  let totalProcessedTime = 0;
  if (srtArray.length > 0) {
    const lastSrt = srtArray[srtArray.length - 1];
    totalProcessedTime = lastSrt.startTime + lastSrt.originalVideoSegmentDuration;
  }

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];

    // ====== FIX: Calculate batchStartTime for subtitle timing ======
    // Chỉ batch đầu tiên cần batchStartTime, các batch khác luôn bắt đầu từ 0
    let batchStartTime = 0;
    if (batch.length > 0) {
      const firstSrt = batch[0];
      const lastSrt = batch[batch.length - 1];
      // Chỉ batch đầu tiên (batchIndex === 0) mới cần batchStartTime
      batchStartTime = batchIndex === 0 ? firstSrt.startTime : 0;
      console.log(
        `🔍 Batch ${batchIndex + 1}: ${firstSrt.startTime.toFixed(2)}s - ${(
          lastSrt.startTime + lastSrt.originalVideoSegmentDuration
        ).toFixed(2)}s (batchStartTime: ${batchStartTime.toFixed(2)}s)`,
      );
    }

    try {
      const batchOutput = await processBatchWithRetry(
        event,
        batch,
        batchIndex,
        videoPath,
        outputDir,
        options,
        batches.length,
        totalVideoDuration,
        totalProcessedTime,
        batchStartTime,
      );
      batchVideoPaths.push(batchOutput);
    } catch (error) {
      console.error(`❌ Failed to process batch ${batchIndex + 1}:`, error.message);
      throw error;
    }
  }

  // 4. Concatenate batches
  const concatListPath = path.join(outputDir, 'concat_list.txt');
  const concatListContent = batchVideoPaths.map((p) => `file '${p}'`).join('\n');
  fs.writeFileSync(concatListPath, concatListContent);
  const concatOutput = path.join(outputDir, 'concatenated.mp4');
  const encoder = await getEncoder();
  const { flipVideo, scaleFactor, volume } = options.output;
  const { width, height } = getResolution(options);
  const flipFilter = flipVideo ? 'hflip' : '';
  // const scaleFilter = `scale=ceil((iw*${scaleFactor})/2)*2:ceil((ih*${scaleFactor})/2)*2`;
  const zoom = scaleFactor || 1.2;
  const scaledW = Math.ceil(width * zoom);
  const scaledH = Math.ceil(height * zoom);
  const zoomFilter = `scale=${scaledW}:${scaledH}:flags=lanczos,crop=${width}:${height}`;
  // const videoFilters = [flipFilter, zoomFilter].filter(Boolean).join(", ");
  const videoFilters = '';
  const audioFilter = `volume=${volume || 15}`;

  // Ghép filter_complex nếu có
  let filterComplex = '';
  if (videoFilters && audioFilter) {
    filterComplex = `-filter_complex "[0:v]${videoFilters}[v];[0:a]${audioFilter}[a]" -map "[v]" -map "[a]" `;
  } else if (videoFilters) {
    filterComplex = `-filter_complex "[0:v]${videoFilters}[v]" -map "[v]" -map 0:a? `;
  } else if (audioFilter) {
    filterComplex = `-filter_complex "[0:a]${audioFilter}[a]" -map 0:v -map "[a]" `;
  }

  // Bắt đầu lệnh ffmpeg
  let concatCmd = `${ffmpegManager.ffmpegPath} -f concat -safe 0 -i "${concatListPath}" ${filterComplex}`;

  // NO FINAL NORMALIZATION - keep original audio quality
  // Remove any audio processing to preserve original voice quality

  // Balanced quality/speed final encoding
  if (encoder.includes('qsv')) {
    concatCmd += `-c:v ${encoder} -preset medium -global_quality 17 -profile:v high -level 4.1 -pix_fmt yuv420p -c:a aac -b:a 256k -ar 48000 -ac 2 -movflags +faststart -y "${concatOutput}"`;
  } else if (encoder.includes('nvenc')) {
    concatCmd += `-c:v ${encoder} -preset medium -cq 17 -profile:v high -level 4.1 -pix_fmt yuv420p -c:a aac -b:a 256k -ar 48000 -ac 2 -movflags +faststart -y "${concatOutput}"`;
  } else if (encoder.includes('amf')) {
    concatCmd += `-c:v ${encoder} -quality balanced -rc cqp -qp_i 17 -qp_p 19 -qp_b 21 -profile:v high -level 4.1 -pix_fmt yuv420p -c:a aac -b:a 256k -ar 48000 -ac 2 -movflags +faststart -y "${concatOutput}"`;
  } else {
    concatCmd += `-c:v ${encoder} -preset medium -crf 17 -profile:v high -level 4.1 -pix_fmt yuv420p -tune film -c:a aac -b:a 256k -ar 48000 -ac 2 -movflags +faststart -y "${concatOutput}"`;
  }

  console.log('📦 Running final concat with audio normalization...');
  event?.sender?.send(type, {
    data: '📦 Running final concat with audio normalization...',
    code: 0,
  });
  await execPromise(concatCmd, { cwd: outputDir });

  // backgroundMusic
  const backgroundMusic = options.audio?.backgroundMusic;
  if (backgroundMusic?.enabled && backgroundMusic?.file) {
    const musicPath = options.audio?.backgroundMusic?.file;
    const musicVolume = options.audio?.backgroundMusic?.volume;
    const outputWithMusic = path.join(outputDir, 'final_with_music.mp4');
    await addAudioToVideo(event, concatOutput, musicPath, outputWithMusic, {
      audioBitrate: '192k',
      volume: musicVolume,
    });
    fs.renameSync(outputWithMusic, concatOutput);
  }

  // export srt file
  await exportSrtFile(srtArray, finalOutput, outputDir, event);

  fs.renameSync(concatOutput, finalOutput);
  // 6. Cleanup
  // rm dir temp
  fs.rm(outputDir, { recursive: true, force: true }, (err) => {
    if (err) console.error('Lỗi khi xoá thư mục:', err);
    else console.log('Xoá thư mục thành công.');
  });

  console.log(`✅ Final video generated: ${finalOutput}`);
  event?.sender?.send(type, {
    data: `✅ Final video generated: ${finalOutput}`,
    code: 0,
    success: true,
  });

  return {
    videoPath: finalOutput,
    adjustedSrtArray: srtArray,
  };
};

function subAdjusted(srts, options, batchStartTime = 0) {
  const srtArray = JSON.parse(JSON.stringify(srts));
  const subInLine = options.textSubtitle.subInLine || 10;

  // console.log(`🔧 subAdjusted: Processing ${srtArray.length} segments with batchStartTime=${batchStartTime.toFixed(2)}s`);

  // ====== TÍNH NĂNG CHIA SUBTITLE DÀI ======
  // Nếu text subtitle có nhiều hơn subInLine từ (mặc định 10),
  // sẽ tự động chia thành các phần nhỏ hơn với thời gian được phân bổ đều

  // Hàm đếm từ (không tính dấu cách)
  function countWords(text) {
    if (!text) return 0;
    return text
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  }

  // Hàm chia text thành các phần nhỏ hơn với tối ưu hóa
  function splitTextIntoChunks(text, maxWords) {
    if (!text) return [text];

    const words = text.trim().split(/\s+/);
    if (words.length <= maxWords) return [text];

    const chunks = [];
    for (let i = 0; i < words.length; i += maxWords) {
      const chunk = words.slice(i, i + maxWords).join(' ');
      chunks.push(chunk);
    }

    // Đảm bảo không có chunk nào trống
    return chunks.filter((chunk) => chunk.trim().length > 0);
  }

  // Tạo bản copy của srtArray với timing đã điều chỉnh cho subtitle và chia text dài
  let adjustedSrtArray = [];

  // ====== FIX: Tính toán lại timing cho batch ======
  // Mỗi batch có timeline riêng bắt đầu từ 0, cần tính toán lại timing
  let batchCurrentTime = 0;

  // Chỉ thêm initial segment offset cho batch đầu tiên (batchStartTime = startTime của segment đầu tiên)
  const batchFirstSrt = srtArray[0];
  const isFirstBatch = batchFirstSrt && batchStartTime === batchFirstSrt.startTime && batchFirstSrt.startTime > 0.1;
  if (isFirstBatch) {
    batchCurrentTime += batchFirstSrt.startTime;
    // console.log(`  🎬 First batch - Adding initial segment offset: ${batchFirstSrt.startTime.toFixed(2)}s`);
  } else {
    // ====== FIX: Thêm delay nhỏ cho batch tiếp theo để tránh miss subtitle đầu tiên ======
    batchCurrentTime += 0.1; // Thêm 100ms delay
    // console.log(`  🎬 Subsequent batch - Starting from 0.1s (with small delay to avoid missing first subtitle)`);
  }

  srtArray.forEach((srt) => {
    // ====== FIX: Tính toán timing dựa trên timeline của batch ======
    // Mỗi batch có timeline riêng bắt đầu từ 0
    // Cần tính toán timing tương đối trong batch

    const baseAdjustedSrt = {
      ...srt,
      adjustedStartTime: batchCurrentTime,
      adjustedEndTime: batchCurrentTime + srt.finalDuration,
    };

    // Cập nhật thời gian cho segment tiếp theo trong batch
    batchCurrentTime += srt.finalDuration;

    const text = srt.translatedText || srt.text || '';
    const wordCount = countWords(text);

    if (wordCount > subInLine) {
      // Chia text thành các phần nhỏ hơn
      const textChunks = splitTextIntoChunks(text, subInLine);
      const totalDuration = baseAdjustedSrt.adjustedEndTime - baseAdjustedSrt.adjustedStartTime;
      const durationPerChunk = totalDuration / textChunks.length;

      // console.log(`📝 Splitting subtitle ${srt.index}: ${wordCount} words > ${subInLine}, creating ${textChunks.length} chunks`);

      textChunks.forEach((chunk, index) => {
        const chunkStartTime = baseAdjustedSrt.adjustedStartTime + index * durationPerChunk;
        const chunkEndTime = baseAdjustedSrt.adjustedStartTime + (index + 1) * durationPerChunk;

        adjustedSrtArray.push({
          ...baseAdjustedSrt,
          translatedText: chunk,
          adjustedStartTime: chunkStartTime,
          adjustedEndTime: chunkEndTime,
          originalIndex: srt.index,
          chunkIndex: index + 1,
          totalChunks: textChunks.length,
        });
      });
    } else {
      // Giữ nguyên nếu text không quá dài
      // Debug log for first segment of each batch
      // if (srt.index === 21) { // First segment of batch 1
      //   console.log(`🐛 DEBUG baseAdjustedSrt for segment ${srt.index}:`, {
      //     adjustedStartTime: baseAdjustedSrt.adjustedStartTime,
      //     adjustedEndTime: baseAdjustedSrt.adjustedEndTime,
      //     startTime: baseAdjustedSrt.startTime,
      //     endTime: baseAdjustedSrt.endTime
      //   });
      // }
      adjustedSrtArray.push(baseAdjustedSrt);
    }
  });
  return adjustedSrtArray;
}

/**
 * Format time in seconds to SRT time format (HH:MM:SS,mmm)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
function formatSrtTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Export SRT file with adjusted timing from the rendered video
 * @param {Array} srtArray - Array of subtitle objects
 * @param {string} finalOutput - Path to the final video output
 * @param {string} outputDir - Output directory
 * @param {Object} event - Electron event object for progress updates
 */
async function exportSrtFile(srtArray, finalOutput, outputDir, event) {
  try {
    const type = 'video-task';

    // Generate SRT content with adjusted timing
    let srtContent = '';
    let adjustedCurrentTime = 0;

    // Add initial segment if first SRT doesn't start at 0
    const firstSrt = srtArray[0];
    if (firstSrt && firstSrt.startTime > 0.1) {
      // No subtitle for initial segment - just advance time
      adjustedCurrentTime += firstSrt.startTime;
    }

    // Process each subtitle with adjusted timing
    srtArray.forEach((srt, index) => {
      const startTime = adjustedCurrentTime;
      const endTime = adjustedCurrentTime + srt.finalDuration;

      // Use translated text if available, otherwise use original text
      const text = srt.translatedText || srt.text || '';

      // Format SRT entry
      srtContent += `${index + 1}\n`;
      srtContent += `${formatSrtTime(startTime)} --> ${formatSrtTime(endTime)}\n`;
      srtContent += `${text}\n\n`;

      // Update time for next subtitle
      adjustedCurrentTime += srt.finalDuration;
    });

    // Generate SRT file path based on video output path
    const videoBaseName = path.basename(finalOutput, path.extname(finalOutput));
    const srtFilePath = path.join(path.dirname(finalOutput), `${videoBaseName}.srt`);

    // Write SRT file
    fs.writeFileSync(srtFilePath, srtContent, 'utf8');

    console.log(`📝 SRT file exported: ${srtFilePath}`);
    event?.sender?.send(type, {
      data: `📝 SRT file exported: ${path.basename(srtFilePath)}`,
      code: 0,
    });

    return srtFilePath;
  } catch (error) {
    console.error('❌ Error exporting SRT file:', error);
    event?.sender?.send('video-task', {
      data: `❌ Error exporting SRT file: ${error.message}`,
      code: 1,
    });
    throw error;
  }
}

module.exports = {
  processVideoSimplified,
  processBatchWithRetry,
  buildBatchAndSrt,
  getResolution,
  processSingleBatch,
  exportSrtFile,
};
