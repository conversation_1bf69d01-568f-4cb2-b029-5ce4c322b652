<template>
  <!-- Container với layout pattern chuẩn -->
  <div class="flex-1 flex-column bg-gray-800 text-white">
    <!-- Content area - có thể scroll -->
    <div class="flex-1 h-full p-1">
      <div class="relative flex-column h-full">
        <div>
          <!-- <label
          for="srt-table-file"
          class="block text-sm font-medium text-gray-300"
        >
          SRT File
        </label> -->
          <div class="mt-1 flex items-center space-x-2 gap-1">
            <input type="file" id="srt-table-file" accept=".srt" @change="handleFileUpload" class="hidden" />
            <a-button @click.prevent="openFileInput" :loading="isProcessing">
              New
            </a-button>

            <!-- SRT Lists Modal -->
            <SrtLists buttonText="Existing" @select="handleSelectSrt" @import-new="handleImportNew" />
            <a-button @click="resetFileSelected" :disabled="!srtItems.length"> Reset </a-button>

            <span v-if="srtFile" class="ml-2 text-sm text-gray-400">
              File: {{ srtFile.name.slice(0, 20) }}...
            </span>

            <div class="flex-1"></div>
            <!-- Batch Replace Button -->
            <a-button @click="openBatchReplaceModal" :disabled="srtItems.length === 0" class="flex items-center">
              <template #icon><SearchOutlined /></template>
              Thay thế
            </a-button>
            <!-- Terminology Dictionary Editor Button -->
            <TerminologyDictionaryEditor :buttonText="t('terminologyDictionary.edit')"
              @update="handleTerminologyUpdate" />

            <!-- Prompt Template Indicator & Button -->
            <PromptTemplateIndicator @open-manager="openPromptTemplateModal" />
            <a-button @click="openPromptTemplateModal" type="default" size="small" class="flex items-center justify-center">
              <template #icon><SettingOutlined /></template>
            </a-button>

            <!-- xây dựng từ điển thuật ngữ -->
            <a-button @click="ensureTerminologyDictionary" danger :disabled="srtItems.length === 0"
              :loading="termIsBuilding">
              Thuật ngữ
            </a-button>
            <!-- state.useTerm -->
             <a-checkbox v-model:checked="state.useTerm" :disabled="srtItems.length === 0">
              Use Term
            </a-checkbox>
            <!-- batch size -->
            <a-input-number v-model:value="ttsStore.batchSize" :min="1" :max="100" :step="1" :disabled="srtItems.length === 0" size="small"
              style="width: 40px; margin-left: 8px;" />
            <!-- Translation Dropdown Button -->
            <a-dropdown :disabled="srtItems.length === 0">
              <a-button type="default" :loading="translating">
                {{ translating ? 'Đang dịch...' : 'Dịch' }}
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu >
                  <a-menu-item
                    @click="handleTranslateWithSrtServiceTerm(false)"
                    :disabled="translating"
                  >
                    <template #icon><TranslationOutlined /></template>
                    Dịch toàn bộ
                    <div class="text-xs text-gray-500 mt-1">Dịch lại tất cả subtitle từ đầu</div>
                  </a-menu-item>
                  <a-menu-item
                    @click="handleTranslateWithSrtServiceTerm(true)"
                    :disabled="!hasPartialTranslation || translating"
                  >
                    <template #icon><PlayCircleOutlined /></template>
                    Dịch tiếp
                    <div class="text-xs text-gray-500 mt-1">
                      {{ hasPartialTranslation ? 'Tiếp tục dịch phần chưa dịch' : 'Không có subtitle nào cần dịch tiếp' }}
                    </div>
                  </a-menu-item>
                  <a-menu-item
                    @click="handleRetryAllFailed"
                    :disabled="!hasFailedTranslations || translating"
                  >
                    <template #icon><ReloadOutlined /></template>
                    Dịch lại lỗi
                    <div class="text-xs text-gray-500 mt-1">
                      {{ hasFailedTranslations ? `Thử lại ${failedCount} subtitle bị lỗi` : 'Không có subtitle nào bị lỗi' }}
                    </div>
                  </a-menu-item>
                  <a-menu-divider v-if="translating" />
                  <a-menu-item
                    v-if="translating"
                    @click="stopTranslation"
                    class="text-red-600 hover:text-red-700"
                  >
                    <template #icon><StopOutlined class="text-red-600" /></template>
                    <span class="text-red-600">Dừng dịch</span>
                    <div class="text-xs text-red-400 mt-1">Dừng quá trình dịch hiện tại</div>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="mt-2 flex items-center space-x-2 gap-1">
            <!-- Voice Configuration Button -->
            <!-- <a-button @click="showVoiceConfigDrawer">
              Cấu hình giọng
            </a-button> -->


            <div class="flex border border-gray-600 px-1 gap-1">

            <!-- Apply Voice Buttons -->
            <a-dropdown :disabled="srtItems.length === 0">
              <a-button>
                Chọn giọng
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="applyVoiceToAll(1)">
                    {{ t('voiceConfig.voice1') }} {{ isVoiceSelected(1) }}
                  </a-menu-item>
                  <a-menu-item @click="applyVoiceToAll(2)">
                    {{ t('voiceConfig.voice2') }} {{ isVoiceSelected(2) }}
                  </a-menu-item>
                  <a-menu-item @click="applyVoiceToAll(3)">
                    {{ t('voiceConfig.voice3') }} {{ isVoiceSelected(3) }}
                  </a-menu-item>
                  <a-menu-item @click="applyVoiceToAll(4)">
                    {{ t('voiceConfig.voice4') }} {{ isVoiceSelected(4) }}
                  </a-menu-item>
                  <a-menu-item @click="applyVoiceToAll(5)">
                    {{ t('voiceConfig.voice5') }} {{ isVoiceSelected(5) }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <!-- Generate Audio Buttons -->

              <a-dropdown :disabled="srtItems.length === 0 || generatingAudio">
                <a-button :loading="generatingAudio">
                  {{ t('voiceConfig.generateAudio') }}
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="generateAllAudio(true)">
                      {{ t('voiceConfig.generateForTranslated') }}
                    </a-menu-item>
                    <a-menu-item @click="generateAllAudio(false)">
                      {{ t('voiceConfig.generateForOriginal') }}
                    </a-menu-item>
                    <a-menu-item @click="generateAllAudioIsNotGenerated(true)">
                      {{ t('voiceConfig.generateForTranslatedNotGenerated') }}
                    </a-menu-item>
                    <a-menu-item @click="generateAllAudioIsNotGenerated(false)">
                      {{ t('voiceConfig.generateForOriginalNotGenerated') }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
              <!-- thread choice ttsStore.concurrency -->
              <a-dropdown :disabled="srtItems.length === 0 || generatingAudio">
                <a-button>
                  thread {{ ttsStore.concurrency }}
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="setConcurrency(1)">
                      1
                    </a-menu-item>
                    <a-menu-item @click="setConcurrency(2)">
                      2
                    </a-menu-item>
                    <a-menu-item @click="setConcurrency(3)">
                      3
                    </a-menu-item>
                    <a-menu-item @click="setConcurrency(4)">
                      4
                    </a-menu-item>
                    <a-menu-item @click="setConcurrency(5)">
                      5
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
              <a-checkbox @change="toggleCapcutVoiceEnabled" v-model:checked="isCapcutVoice"  class="flex items-center">
                <!-- Capcut -->
                <a-tooltip title="Generate audio using Capcut TTS, require Capcut cookie">
                  <img width="20" src="@/assets/capcut.svg" alt="Capcut" />
                </a-tooltip>
              </a-checkbox>
              <!-- <a-checkbox @change="toggleEdgeVoiceEnabled" v-model:checked="isEdgeVoice">
                Edge TTS
              </a-checkbox> -->
              <!-- Test Edge TTS button -->
              <!-- <a-button @click="testEdgeTTS" size="small" type="dashed" v-if="isEdgeVoice">
                Test Edge
              </a-button> -->
              <!-- stop generate audio -->
              <a-button @click="stopGeneratingAudio" v-if="generatingAudio" danger>Stop</a-button>
            </div>
            <!-- Join Audio Files Button -->
            <!-- <a-button
            @click="joinMp3FilesV2"
            :disabled="srtItems.length === 0 || joiningAudio"
            :loading="joiningAudio"
          >
            {{ t('voiceConfig.joinAudios') }}
          </a-button> -->
            <!-- <RenderVideoButton :key="key" /> -->

            <!-- apply is enabled all -->
            <a-checkbox :checked="allEnabled" @change="toggleAllEnabled" :indeterminate="indeterminate">
              Enabled {{ isEnabledCount }}
            </a-checkbox>


            <!-- srt export -->
            <a-button @click="exportSrt" :disabled="srtItems.length === 0">
              Export SRT
            </a-button>
            <!-- copy -->
             <a-button @click="copySrtToText" :disabled="srtItems.length === 0">
              Copy SRT
            </a-button>
             <a-button @click="toTranslate" :disabled="srtItems.length === 0">
              To Translate
            </a-button>
             <a-checkbox v-model:checked="state.useNovel" :disabled="srtItems.length === 0">
              Novel
            </a-checkbox>

            <!-- Novel Memory Reference Selector -->
            <a-select
              v-if="state.useNovel && availableNovelSrts.length > 0"
              v-model:value="selectedNovelReference"
              placeholder="Chọn SRT để tham chiếu memory"
              style="width: 200px; margin-left: 8px;"
              size="small"
              allow-clear
            >
              <a-select-option
                v-for="srt in availableNovelSrts"
                :key="srt.name"
                :value="srt.name"
              >
                {{ srt.name.split('_')[0] }} ({{ srt.percentage }}%)
              </a-select-option>
            </a-select>

            <!-- Memory Management Button -->
            <a-button
              v-if="state.useNovel && memoryStatus && memoryStatus.estimatedTokens > 6000"
              @click="compressCurrentMemory"
              size="small"
              type="primary"
              ghost
              style="margin-left: 8px;"
              :loading="compressingMemory"
            >
              Nén Memory
            </a-button>

          </div>
        </div>
        <!-- AI Service Status -->
        <div class="mt-1 p-0 bg-gray-700 rounded border border-gray-600">
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-300">AI Service:</span>
            <div class="flex items-center gap-2">
              <span class="text-white">{{ currentAiServiceInfo }}</span>
              <a-tag
                v-if="ttsStore.getActiveAiService()?.apiKey"
                color="green"
                size="small"
              >
                Ready
              </a-tag>
              <a-tag
                v-else
                color="red"
                size="small"
              >
                No API Key
              </a-tag>
            </div>
          </div>
        </div>

        <div v-if="validationError" class="mt-4 p-3 bg-red-900/50 text-red-300 rounded border border-red-700">
          {{ validationError }}
        </div>

        <div className="pt-0" v-if="translating">
          <div className="w-full" v-if="isPaused">
            <div className="flex items-center justify-between mb-1">
              <div className="text-sm font-medium text-amber-600">
                {{ t("translationSettings.translationPaused") }}
              </div>
              <!-- <div className="text-sm text-gray-500">{{ translationProgress }}%</div> -->
            </div>
            <a-progress :percent="translationProgress" :isPaused="isPaused" />
            <div className="mt-2 text-xs text-amber-600 px-2 py-1 bg-amber-50 border border-amber-100 rounded-md">
              {{ t("translationSettings.translationPaused") }}
            </div>
          </div>
          <div className="w-full" v-else>
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-blue-300">
                {{ t("translationSettings.translationInProgress") }}
              </div>
              <!-- <div className="text-sm text-gray-500">{{ translationProgress }}%</div> -->
            <a-progress class="w-[85%]" :percent="translationProgress" />
            </div>
            <div className="mt-1 text-xs text-gray-300 flex items-center justify-between">
              <span>
                <!-- {{modelTranslations.title[locale === 'en' ? 'en' : 'vi']}}: -->
                <span className="font-medium">
                  <!-- {{ currentAiServiceInfo }} -->
                  <span v-if="state.useNovel" className=" px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                    Novel Mode
                  </span>
                  <span
                    v-if="memoryStatus"
                    :className="`ml-2 px-2 py-1 rounded text-xs ${getMemoryStatusClass(memoryStatus.status)}`"
                    :title="`Characters: ${memoryStatus.charCount}, Words: ${memoryStatus.wordCount}, Estimated tokens: ${memoryStatus.estimatedTokens}`"
                  >
                    {{ memoryStatus.text }}
                  </span>
                  <span v-if="novelProgressStatus" className="ml-2 px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                    {{ novelProgressStatus }}
                  </span>
                </span>
              </span>
              <span className="text-gray-500">{{
                translationProgress > 0 && translationProgress < 100 ? `${Math.round((srtItems.length *
                  translationProgress) / 100)}/${srtItems.length}` : "" }}</span>
            </div>
          </div>
        </div>
        <SubtitleTable v-if="srtItems.length > 0" :subtitles="srtItems" @retry="handleRetrySubtitle"
          @updateTranslation="handleUpdateSubtitle" @updateOriginalText="handleUpdateOriginalText"
          :translating="translating" :batchSize="ttsStore.batchSize" :highlightedSubtitleId="state.currentPlayingSubtitleId"
          @suggestTranslation="handleSuggestBetterTranslation"
          :toggleVoiceForSubtitle="toggleVoiceForSubtitle" :generateAudioForSubtitle="generateAudioForSubtitle"
          :generatingAudio="generatingAudio" :currentGeneratingId="state.currentPlayingSubtitleId"
          @playVideo="handlePlayVideo" @delete="handleDelete" @insert="handleInsert" @split="handleSplit"
          @merge="handleMerge" @reorder="handleReorder" :mode="props.mode" :key="subKey" />
        <div v-else-if="!isProcessing && !srtFile" class="text-center py-8 text-gray-400">
          <p class="mb-4">No SRT file selected.</p>
          <div class="flex justify-center space-x-4 mb-2">
            <a-button @click="openFileInput">Import New SRT File</a-button>
            <SrtLists buttonText="Select Existing SRT" @select="handleSelectSrt" @import-new="handleImportNew" />
          </div>
          <DragDropUpload accept=".srt,application/x-subrip,text/srt" :max-size="100 * 1024 * 1024"
            @files-selected="handleFileUpload" />
        </div>
      </div>

      <!-- Voice Configuration Drawer -->
      <VoiceConfigDrawer :visible="voiceConfigDrawerVisible" @close="closeVoiceConfigDrawer"
        @apply="applyVoiceConfig" />

      <!-- Prompt Template Modal -->
      <PromptTemplateModal
        v-model:open="promptTemplateModalVisible"
        @template-selected="handleTemplateSelected"
        @close="closePromptTemplateModal"
      />

      <!-- Batch Replace Modal -->
      <BatchReplaceModal
        v-model:open="batchReplaceModalVisible"
        :subtitles="srtItems"
        @replace="handleBatchReplace"
        @close="closeBatchReplaceModal"
      />

      <!-- Audio Generation Progress -->
      <div v-if="generatingAudio || joiningAudio"
        class="fixed bottom-4 right-4 bg-gray-800 shadow-lg rounded-lg p-4 z-50 w-80 border border-gray-700">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-md font-medium text-white">{{ t('voiceConfig.processing') }}</h3>
          <span class="text-white">{{ audioProgress }}%</span>
        </div>
        <a-progress :percent="audioProgress" :status="audioProgress === 100 ? 'success' : 'active'" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick, triggerRef } from "vue";
import { message, Modal } from "ant-design-vue";
import { parseSRT } from "@/lib/utils";
import { getProviderForModel } from "@/lib/modelUtils";
import SubtitleTable from "./SubtitleTable.vue";
import SrtLists from "./SrtLists.vue";
import DragDropUpload from "./DragDropUpload.vue";
import { useTTSStore } from "@/stores/ttsStore";
import { useSRTStore } from "@/stores/srtStore";
import { useI18n } from "@/i18n/i18n";
import { hasApiKey, setApiKey } from "@/lib/apiKeyManager";
import LoadingIndicator from "./LoadingIndicator.vue";
import { AVAILABLE_MODELS } from "@/lib/modelUtils";
import { translateSrtService, configureTranslateService, configureFromStore, buildTerminologyDictionary, stopTranslation as stopAllTranslation } from "@/lib/allTranslateService";
import { novelTranslateService } from "@/lib/novelTranslateService";
import TerminologyDictionaryEditor from "./TerminologyDictionaryEditor.vue";
import VoiceConfigDrawer from "./VoiceConfigDrawer.vue";
import PromptTemplateModal from "./PromptTemplateModal.vue";
import PromptTemplateIndicator from "./PromptTemplateIndicator.vue";
import BatchReplaceModal from "./BatchReplaceModal.vue";
// Remove old import - we'll use allTranslateService instead
import { DownOutlined, FileTextOutlined, SettingOutlined, SearchOutlined, TranslationOutlined, PlayCircleOutlined, StopOutlined, ReloadOutlined } from '@ant-design/icons-vue';
// import VideoPlayer from "./VideoPlayer.vue";
import RenderVideoButton from "./RenderVideoButton.vue";
import { state } from '@/lib/state';
import { useSubtitleStore } from '@/stores/subtitle-store';
import {
  reorderSubtitleIds,
  adjustSubtitleTimes,
  mergeSubtitles,
  fixOverlappingSubtitles
} from '@/lib/subtitleUtils';

import { edgeTts } from '@/logic/edgeTts';
import edgeVoices from '@/assets/edge-voices.json';

const ttsStore = useTTSStore();
const srtStore = useSRTStore();
const subtitleStore = useSubtitleStore();
const { t } = useI18n();

const props = defineProps({
  mode: {
    type: String,
    default: 'normal'
  }
});

const translating = ref(false);
const srtFile = ref(null);
const isProcessing = ref(false);
// Sử dụng ref thay vì computed để có control tốt hơn về reactivity
// Reactive trigger để force re-render

const srtItems = computed({
  get: () => ttsStore.currentSrtList?.items || [],
  set: (newValue) => {
    if (ttsStore.currentSrtList) {
      // Cập nhật trực tiếp vào store
      ttsStore.currentSrtList.items = newValue;

      // Cập nhật trong srtLists array
      const index = ttsStore.srtLists.findIndex(
        (item) => item.name === ttsStore.currentSrtList.name
      );
      if (index !== -1) {
        ttsStore.srtLists[index].items = [...newValue];
      }
    }
  }
});

// add key for SubtitleTable if srtItems change
const subKey = ref(0);

// Function to update both local ref and store
const updateSrtItems = (newValue) => {
  // console.log(`🔍 updateSrtItems called with ${newValue.length} items`);

  // Update local ref first
  srtItems.value = [...newValue];

  // Then update store
  if (ttsStore.currentSrtList) {
    ttsStore.currentSrtList.items = [...newValue];

    // Cập nhật trong srtLists array
    const index = ttsStore.srtLists.findIndex(
      (item) => item.name === ttsStore.currentSrtList.name
    );
    if (index !== -1) {
      ttsStore.srtLists[index].items = [...newValue];
    }
  }

  // Force reactivity trigger
};
const selectedItems = ref([]);
const currentPage = ref(1);
const defaultSpeaker = computed(() => ttsStore.selectedSpeaker);
const validationError = ref(null);
const pauseStateRef = ref(false);
const translationError = ref(null);
const translationProgress = ref(0);
const isPaused = ref(false);
const selectedNovelReference = ref(null);
const compressingMemory = ref(false);
const existingTranslations = ref({});
const lastTranslatedIndex = ref(0);
const apiKeyProvided = ref(false);
const termIsBuilding = ref(false);
const playCurrentTime = ref(0);
const allEnabled = ref(true);
const indeterminate = ref(false);

// new key for reopen component




// Voice configuration
const voiceConfigDrawerVisible = ref(false);
const generatingAudio = ref(false);
const isStopping = ref(false);
const joiningAudio = ref(false);
const audioProgress = ref(0);
const videoPlayer = ref(null);

const isCapcutVoice = ref(false)
const isEdgeVoice = ref(false)

// Prompt Template Modal
const promptTemplateModalVisible = ref(false);

// Batch Replace Modal
const batchReplaceModalVisible = ref(false);

const isEnabledCount = computed(() => {
  return srtItems.value.filter(item => item.isEnabled).length;
});


// Selected voice for each subtitle
const selectedVoices = ref({});

// Computed property to check if there are partially translated items
const hasPartialTranslation = computed(() => {
  if (!srtItems.value || srtItems.value.length === 0) return false;

  const translatedCount = srtItems.value.filter(item =>
    item.status === "translated" && item.translatedText
  ).length;

  return translatedCount > 0 && translatedCount < srtItems.value.length;
});

// Computed property to check if there are failed translations
const hasFailedTranslations = computed(() => {
  if (!srtItems.value || srtItems.value.length === 0) return false;
  return srtItems.value.some(item => item.status === "error");
});

// Computed property to count failed translations
const failedCount = computed(() => {
  if (!srtItems.value || srtItems.value.length === 0) return 0;
  return srtItems.value.filter(item => item.status === "error").length;
});

// Computed property for current AI service info
const currentAiServiceInfo = computed(() => {
  const activeService = ttsStore.getActiveAiService();
  if (!activeService) {
    return `${ttsStore.selectedAiService} (Not configured)`;
  }

  return `${activeService.name} - ${ttsStore.selectedModel}`;
});

// Computed property for memory status
const memoryStatus = computed(() => {
  if (!state.useNovel) return null;

  const memory = ttsStore.currentSrtList?.memory || '';
  if (memory.length === 0) {
    return 'No memory';
  }

  const charCount = memory.length;
  const wordCount = memory.split(/\s+/).length;
  const estimatedTokens = Math.ceil(charCount / 3); // Rough token estimation

  // Determine status color based on size
  let status = 'normal';
  if (estimatedTokens > 8000) status = 'warning';
  if (estimatedTokens > 12000) status = 'danger';

  return {
    text: `Memory: ${wordCount} words (${estimatedTokens} tokens)`,
    status,
    charCount,
    wordCount,
    estimatedTokens
  };
});

// Computed property for novel progress status
const novelProgressStatus = computed(() => {
  if (!state.useNovel || !ttsStore.currentSrtList) return null;

  const progress = ttsStore.getNovelProgress(ttsStore.currentSrtList.name);
  if (progress.totalCount === 0) return null;

  const percentage = Math.round((progress.translatedCount / progress.totalCount) * 100);
  return `Progress: ${progress.translatedCount}/${progress.totalCount} (${percentage}%)`;
});

// Computed property for available novel progress SRTs
const availableNovelSrts = computed(() => {
  const srtNames = ttsStore.getNovelProgressSrtNames();
  return srtNames.map(name => {
    const progress = ttsStore.getNovelProgressBySrtName(name);
    return {
      name,
      progress,
      percentage: progress.totalCount > 0 ? Math.round((progress.translatedCount / progress.totalCount) * 100) : 0
    };
  });
});

// Function to get memory status CSS class
const getMemoryStatusClass = (status) => {
  switch (status) {
    case 'warning':
      return 'bg-yellow-100 text-yellow-700';
    case 'danger':
      return 'bg-red-100 text-red-700';
    default:
      return 'bg-blue-100 text-blue-700';
  }
};

// Function to manually compress memory
const compressCurrentMemory = async () => {
  if (!ttsStore.currentSrtList?.memory) {
    message.warning('Không có memory để nén');
    return;
  }

  try {
    compressingMemory.value = true;

    // Import NovelTranslator to use compression methods
    const { NovelTranslator } = await import('@/lib/NovelTranslator');
    const translator = new NovelTranslator();

    const originalMemory = ttsStore.currentSrtList.memory;
    const originalTokens = Math.ceil(originalMemory.length / 3);

    console.log(`Compressing memory: ${originalTokens} tokens`);

    const compressedMemory = translator.compressMemory(originalMemory);
    const newTokens = Math.ceil(compressedMemory.length / 3);

    // Update memory
    ttsStore.updateCurrentSrtMemory(compressedMemory);

    message.success(`Memory đã được nén từ ${originalTokens} xuống ${newTokens} tokens`);

  } catch (error) {
    console.error('Error compressing memory:', error);
    message.error('Lỗi khi nén memory: ' + error.message);
  } finally {
    compressingMemory.value = false;
  }
};


function handleUpdateSubtitle(id, text) {
  // Update subtitle logic using updateSrtItems function
  const currentItems = [...srtItems.value];
  const index = currentItems.findIndex((s) => s.id === id);
  if (index !== -1) {
    currentItems[index].translatedText = text;
    currentItems[index].status = "translated";
    updateSrtItems(currentItems);
  }
}

function handleUpdateOriginalText(id, text) {
  // Update original text logic using updateSrtItems function
  const currentItems = [...srtItems.value];
  const index = currentItems.findIndex((s) => s.id === id);
  if (index !== -1) {
    currentItems[index].text = text;
    updateSrtItems(currentItems);
  }
}

onMounted(async () => {
  window.ttsStore = ttsStore;

  // Load CapCut speakers if not already loaded
  if (!ttsStore.speakers || ttsStore.speakers.length === 0) {
    console.log('Loading CapCut/TikTok speakers...');
    await ttsStore.fetchCapCutSpeakers();
  }

  // Auto-configure translation service from store
  try {
    const success = configureFromStore(ttsStore);
    if (success) {
      // console.log("Translation service auto-configured from store");
      apiKeyProvided.value = true;
    } else {
      console.warn("Failed to auto-configure translation service - no active service or API key");
      apiKeyProvided.value = false;
    }
  } catch (error) {
    console.error("Error auto-configuring translation service:", error);
    apiKeyProvided.value = false;
  }

  // Legacy support - still set API keys for backward compatibility
  const activeService = ttsStore.getActiveAiService();
  if (activeService && activeService.apiKey) {
    if (ttsStore.selectedAiService === "gemini") {
      setApiKey(activeService.apiKey, ttsStore.selectedModel);
    } else if (ttsStore.selectedAiService === "openai") {
      setApiKey(activeService.apiKey, ttsStore.selectedModel);
    } else if (ttsStore.selectedAiService === "deepseek") {
      setApiKey(activeService.apiKey, ttsStore.selectedModel);
    }
  }
});

// Watcher không còn cần thiết vì srtItems giờ là computed property
// và tự động đồng bộ với store thông qua setter

watch(
  () => ttsStore.activeTab,
  async (newVal) => {
    if (newVal === "srt-table") {
      await handleMounded();
    }
  }
);

// Watch for AI service changes and auto-reconfigure
watch(
  () => [ttsStore.selectedAiService, ttsStore.selectedModel],
  () => {
    try {
      const success = configureFromStore(ttsStore);
      if (success) {
        console.log("Translation service reconfigured due to AI service change");
        apiKeyProvided.value = true;
      } else {
        console.warn("Failed to reconfigure translation service");
        apiKeyProvided.value = false;
      }
    } catch (error) {
      console.error("Error reconfiguring translation service:", error);
      apiKeyProvided.value = false;
    }
  },
  { deep: true }
);

// split
const handleSplit = (id, splitSubtitles) => {
  console.log('Split subtitle:', id, splitSubtitles);

  const currentItems = [...srtItems.value];
  const index = currentItems.findIndex(s => s.id === id);
  if (index === -1) return;

  // Replace original subtitle with split parts
  currentItems.splice(index, 1, ...splitSubtitles);

  // Reorder IDs and update
  updateSrtItems(reorderSubtitleIds(currentItems));

  message.success(`Split subtitle ${id} into 2 parts`);
};

// handleMerge
const handleMerge = (ids, mergedSubtitle) => {
  console.log('Merge subtitles:', ids, mergedSubtitle);

  const currentItems = [...srtItems.value];
  // Find indices of subtitles to merge
  const indices = ids.map(id => currentItems.findIndex(s => s.id === id)).sort((a, b) => b - a);

  if (indices.some(i => i === -1)) return;

  // Remove the subtitles (in reverse order to maintain indices)
  indices.forEach(index => {
    currentItems.splice(index, 1);
  });

  // Insert merged subtitle at the first position
  currentItems.splice(Math.min(...indices), 0, mergedSubtitle);

  // Reorder IDs and update
  updateSrtItems(reorderSubtitleIds(currentItems));

  message.success(`Merged subtitles ${ids.join(', ')}`);
};

// handleInsert
const handleInsert = (newSubtitle, targetId, position, adjustTiming = true) => {
  console.log('Insert subtitle:', newSubtitle, targetId, position, adjustTiming);

  let currentItems = [...srtItems.value];
  const targetIndex = currentItems.findIndex(s => s.id === targetId);
  if (targetIndex === -1) return;

  const insertIndex = position === 'before' ? targetIndex : targetIndex + 1;

  // Insert new subtitle
  currentItems.splice(insertIndex, 0, newSubtitle);

  // Reorder IDs
  currentItems = reorderSubtitleIds(currentItems);

  // Adjust timing if requested
  if (adjustTiming) {
    const insertDuration = newSubtitle.endTime - newSubtitle.startTime;
    currentItems = adjustSubtitleTimes(currentItems, insertIndex + 1, insertDuration);
  }

  // Fix any overlapping srtItems and update
  updateSrtItems(fixOverlappingSubtitles(currentItems));

  message.success(`Inserted new subtitle ${position} subtitle ${targetId}`);
};

// handleReorder
const handleReorder = (newOrder) => {
  console.log('Reorder subtitles:', newOrder);
  updateSrtItems(reorderSubtitleIds(newOrder));
  message.success('Reordered subtitles');
};

const handleDelete = (id) => {
  console.log('Delete subtitle:', id);
  const currentItems = [...srtItems.value];
  const index = currentItems.findIndex(s => s.id === id);
  if (index !== -1) {
    currentItems.splice(index, 1);
    // Reorder IDs and update
    updateSrtItems(reorderSubtitleIds(currentItems));
    message.success(`Deleted subtitle ${id}`);
  }
};

async function handleMounded() {
  if (srtStore.srtContent && srtStore.srtFile) {
    if (ttsStore.currentSrtList?.path === srtStore.srtFile?.path) return;
    await processSrtFile(srtStore.srtContent);
  }
}

async function processSrtFile(content) {
  try {
    isProcessing.value = true;
    subKey.value++;
    // Parse SRT content
    const parsedItems = parseSRT(content);

    // Add additional properties for voice selection and speed
    const processedItems = parsedItems.map((item) => ({
      ...item,
      translatedText: "",
      status: "pending",
      selectedSpeaker: defaultSpeaker.value,
      speechRate: 0, // Default speech rate (normal speed)
      audioUrl: "",
      duration: 0,
      isGenerated: false,
      isEnabled: true,
      isPlayable: false,
      isVoice: 1
    }));

    srtFile.value = srtStore.srtFile || srtFile.value;

    // Create a new SRT list entry
    const newSrtList = {
      name: srtFile.value.name + "_" + new Date().getTime(),
      items: processedItems,
      path: srtFile.value.path,
      layers: [],
      memory: '', // Initialize memory for Novel translation
    };
    if (srtFile.value.path) {
      const pathFile = srtFile.value.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
      const info = await window.electronAPI.getVideoInfo(pathFile);
      // create thumbnail
      const imagePath = await window.electronAPI.invoke('create-thumb', pathFile)
      newSrtList.info = info;
      newSrtList.thumb = imagePath;
    }
    // Add to the store
    ttsStore.srtLists.push(newSrtList);

    // Set as current SRT list - srtItems sẽ tự động cập nhật thông qua computed property
    ttsStore.currentSrtList = newSrtList;
    await electronAPI.setCurrentDir(srtFile.value.path);

    message.success(`Imported ${parsedItems.length} items from SRT file`);
    ttsStore.terminologyDictionary = {};
    state.key++;
  } catch (error) {
    console.error("Error processing SRT file:", error);
    message.error("Error processing SRT file: " + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// Function to handle file upload
async function handleFileUpload(e) {
  const file = e?.target?.files?.[0] || e?.[0];
  if (!file) return;

  srtFile.value = file;
  isProcessing.value = true;

  try {
    // Read the SRT file
    const reader = new FileReader();
    const content = await new Promise((resolve, reject) => {
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });

    // Parse SRT content
    await processSrtFile(content);

    message.success(`Imported ${srtItems.value.length} items from SRT file`);
  } catch (error) {
    console.error("Error processing SRT file:", error);
    message.error("Error processing SRT file: " + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// reset file selected
async function resetFileSelected() {
  srtFile.value = null;
  selectedItems.value = [];
  currentPage.value = 1;
  ttsStore.currentSrtList = null; // srtItems sẽ tự động thành [] thông qua computed property
  await electronAPI.setCurrentDir();
}

function openFileInput() {
  document.getElementById("srt-table-file").click();
}

async function handleSelectSrt(srtList) {
  if (!srtList || !srtList.items || srtList.items.length === 0) {
    message.error("Selected SRT file has no items");
    return;
  }
  subKey.value++;
  // Reset current state
  selectedItems.value = [];
  currentPage.value = 1;
  if (srtList.path) {
    const videoPath = srtList.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4');
    const info = await window.electronAPI.getVideoInfo(videoPath);
    srtList.info = info;
  }
  // Initialize memory field if not exists (for backward compatibility)
  if (!srtList.memory) {
    srtList.memory = '';
  }

  // Set as current SRT list - srtItems sẽ tự động cập nhật thông qua computed property
  ttsStore.currentSrtList = srtList;

  // Set the file name for display
  const fileName = srtList.name.split("_")[0];
  srtFile.value = { name: fileName };
  state.key++;


  message.success(`Loaded ${srtItems.value.length} items from selected SRT file`);
}

function handleImportNew() {
  openFileInput();
}

// function hasApiKey(model) {
//   return !!ttsStore.model && ttsStore.model === model;
// }

// handle translate
// Xác thực đầu vào trước khi dịch
const validateBeforeTranslate = () => {
  // Kiểm tra API key cho service hiện tại
  const activeService = ttsStore.getActiveAiService();
  if (!activeService || !activeService.enabled || !activeService.apiKey) {
    const serviceName = activeService ? activeService.name : ttsStore.selectedAiService;
    validationError.value = t("errors.apiKeyRequired") + ` (${serviceName})`;
    return false;
  }

  // Kiểm tra file SRT
  if (srtItems.value.length === 0) {
    validationError.value = t("errors.fileRequired");
    return false;
  }

  // Nếu tất cả đều hợp lệ, xóa thông báo lỗi
  validationError.value = null;
  return true;
};




// Handle retrying a subtitle or navigating to it
const handleRetrySubtitle = async (id) => {
  // Check if this is just a click to navigate to this subtitle
  if (id === state.currentPlayingSubtitleId) {
    return; // Already selected, no need to retry translation
  }

  // Set the current playing subtitle immediately for navigation purposes
  state.currentPlayingSubtitleId = id;
  subtitleStore.activeSubtitleId = id;

  // Only proceed with retry if the status is error
  const subtitleIndex = srtItems.value.findIndex(sub => sub.id === id);
  if (subtitleIndex === -1) return;

  const subtitle = srtItems.value[subtitleIndex];
  if (subtitle.status !== "error") {
    return; // Just navigation, not a retry
  }

  // If it's an actual retry (status is error), proceed with retry logic
  message.info("Đang thử lại dịch subtitle #" + id);

  try {
    // Configure the translate service from store
    const success = configureFromStore(ttsStore);
    if (!success) {
      throw new Error("Failed to configure translation service - no active service or API key");
    }

    // Prepare single subtitle for retry
    const singleSubForTranslation = [{
      index: subtitle.id,
      text: subtitle.text,
      start: subtitle.startTime,
      end: subtitle.endTime
    }];

    // Mark subtitle as translating
    const updatedSubtitles = [...srtItems.value];
    updatedSubtitles[subtitleIndex].status = "translating";
    updatedSubtitles[subtitleIndex].error = undefined;
    updateSrtItems(updatedSubtitles);

    // Create a simple callback for single subtitle retry
    const singleRetryCallback = async (translatedTexts, startIndex = null, isFailedBatch = false) => {
      if (!translatedTexts || translatedTexts.length === 0 || !translatedTexts[0] || translatedTexts[0].trim().length === 0) {
        console.error(`❌ Retry failed for subtitle ${id}`);
        const failedSubtitles = [...srtItems.value];
        const failedIndex = failedSubtitles.findIndex(sub => sub.id === id);
        if (failedIndex !== -1) {
          failedSubtitles[failedIndex].status = "error";
          failedSubtitles[failedIndex].error = "Retry translation failed";
        }
        updateSrtItems(failedSubtitles);
        message.error(`Không thể dịch lại subtitle #${id}`);
        return;
      }

      // Success - update with translated text
      const successSubtitles = [...srtItems.value];
      const successIndex = successSubtitles.findIndex(sub => sub.id === id);
      if (successIndex !== -1) {
        successSubtitles[successIndex].translatedText = translatedTexts[0];
        successSubtitles[successIndex].status = "translated";
        successSubtitles[successIndex].error = undefined;
      }
      updateSrtItems(successSubtitles);
      message.success(`Đã dịch lại thành công subtitle #${id}`);
    };

    // Retry translation
    if (state.useNovel) {
      await novelTranslateService.translateSrtService({
        subs: singleSubForTranslation,
        batchSize: 1,
        targetLanguage: "Vietnamese",
        callback: singleRetryCallback
      });
    } else {
      await translateSrtService({
        subs: singleSubForTranslation,
        batchSize: 1,
        terminologyDictionary: ttsStore.terminologyDictionary,
        targetLanguage: "Vietnamese",
        callback: singleRetryCallback
      });
    }

  } catch (error) {
    console.error("Error retrying subtitle:", error);
    message.error(`Lỗi khi thử lại dịch subtitle #${id}: ${error.message}`);

    // Mark as error
    const errorSubtitles = [...srtItems.value];
    errorSubtitles[subtitleIndex].status = "error";
    errorSubtitles[subtitleIndex].error = "Retry failed: " + error.message;
    updateSrtItems(errorSubtitles);
  }
};










// These functions have been removed as they're no longer needed

// Function to handle terminology dictionary updates
const handleTerminologyUpdate = (updatedDictionary) => {
  console.log("Terminology dictionary updated:", updatedDictionary);
  // The dictionary is already updated in the store by the TerminologyDictionaryEditor component
};

// Function to check if we need to build terminology dictionary
const ensureTerminologyDictionary = async () => {
  // If we already have a terminology dictionary with entries, use it
  if (Object.keys(ttsStore.terminologyDictionary || {}).length > 0) {
    return true;
  }
  termIsBuilding.value = true;
  // Otherwise, build a new one
  try {
    message.info(t("terminologyDictionary.buildingDictionary"));
    translationProgress.value = 5;
    // setProgressBar(5);

    // Get sample text from the first 150 subtitles
    const sampleTexts = srtItems.value.slice(0, 150).map(sub => sub.text);
    const sampleText = sampleTexts.join('\n\n');

    // Build terminology dictionary
    ttsStore.terminologyDictionary = await buildTerminologyDictionary(
      sampleText,
      'Chinese'
    );

    console.log("Built terminology dictionary:", ttsStore.terminologyDictionary);
    message.success(t("terminologyDictionary.dictionaryBuilt"));
    termIsBuilding.value = false;
    return true;
  } catch (error) {
    console.error("Error building terminology dictionary:", error);
    message.error("Error building terminology dictionary: " + error.message);
    termIsBuilding.value = false;
    return false;
  }
};

// Function to save translation progress
const saveTranslationProgress = () => {
  // Create a map of translated items
  const translations = {};
  let lastIndex = 0;

  srtItems.value.forEach((item, index) => {
    if (item.status === "translated" && item.translatedText) {
      translations[item.id] = item.translatedText;
      lastIndex = Math.max(lastIndex, index);
    }
  });

  // Save the last translated index
  lastTranslatedIndex.value = lastIndex;
  existingTranslations.value = translations;

  // Store sẽ tự động được cập nhật thông qua computed property setter
};

// Hàm xử lý khi người dùng nhấn nút dịch với SRT Service
const handleTranslateWithSrtServiceTerm = async (continueMode = false) => {
  // Xác thực đầu vào và dừng nếu có lỗi
  if (!validateBeforeTranslate()) {
    return;
  }

  // Ensure we have a terminology dictionary
  if (!await ensureTerminologyDictionary()) {
    return;
  }

  // Configure the translate service from store
  try {
    const success = configureFromStore(ttsStore);
    if (!success) {
      throw new Error("Failed to configure translation service - no active service or API key");
    }
    console.log("Translation service configured for translation");
  } catch (error) {
    console.error("Error configuring translation service:", error);
    message.error("Translation service configuration failed: " + error.message);
    translating.value = false;
    return;
  }

  translating.value = true;
  translationProgress.value = 0;
  isPaused.value = false;
  pauseStateRef.value = false;
  translationError.value = null;

  try {
    let subsForTranslation;
    let initialProgress = 0;

    if (continueMode) {
      // Continue mode: only translate untranslated items
      console.log("Continue translation mode");

      // Filter out already translated items
      const untranslatedItems = srtItems.value.filter(item =>
        item.status !== "translated" && (!item.translatedText || item.translatedText.trim() === "")
      );

      if (untranslatedItems.length === 0) {
        message.info("Tất cả subtitle đã được dịch");
        translating.value = false;
        return;
      }

      // Calculate initial progress based on already translated items
      const translatedCount = srtItems.value.length - untranslatedItems.length;
      initialProgress = Math.floor((translatedCount / srtItems.value.length) * 100);
      translationProgress.value = initialProgress;
      await setProgressBar(initialProgress);

      // Set status to translating for untranslated items only
      const updatedSubtitles = srtItems.value.map((sub) => {
        if (sub.status !== "translated" && (!sub.translatedText || sub.translatedText.trim() === "")) {
          return {
            ...sub,
            status: "translating",
            error: undefined,
          };
        }
        return sub;
      });
      updateSrtItems(updatedSubtitles);

      // Prepare data for translation service (only untranslated items)
      subsForTranslation = untranslatedItems.map(item => ({
        index: item.id,
        text: item.text,
        start: item.startTime,
        end: item.endTime
      }));

      console.log(`🔍 Continue mode debug:`);
      console.log(`- Total items: ${srtItems.value.length}`);
      console.log(`- Translated items: ${translatedCount}`);
      console.log(`- Untranslated items: ${untranslatedItems.length}`);
      console.log(`- First untranslated item ID: ${untranslatedItems[0]?.id}`);
      console.log(`- Last untranslated item ID: ${untranslatedItems[untranslatedItems.length - 1]?.id}`);
      console.log(`- subsForTranslation length: ${subsForTranslation.length}`);

      message.info(`Tiếp tục dịch ${untranslatedItems.length} subtitle chưa dịch`);
    } else {
      // Full translation mode: translate all items from scratch
      console.log("Full translation mode");

      // Đặt lại trạng thái cho tất cả phụ đề về "translating" khi dịch lại từ đầu
      const resetSubtitles = srtItems.value.map((sub) => ({
        ...sub,
        status: "translating",
        translatedText: "", // Xóa bản dịch cũ
        error: undefined,
      }));
      updateSrtItems(resetSubtitles);

      // Prepare data for translation service (all items)
      subsForTranslation = srtItems.value.map(item => ({
        index: item.id,
        text: item.text,
        start: item.startTime,
        end: item.endTime
      }));

      message.info(`Bắt đầu dịch toàn bộ ${srtItems.value.length} subtitle`);
    }

    // Cập nhật tiến độ ban đầu
    translationProgress.value = Math.max(5, initialProgress);
    await setProgressBar(translationProgress.value);
    // Hàm callback để cập nhật kết quả dịch từng batch
    let processedCount = 0; // Biến đếm số lượng đã xử lý
    const updateTranslatedBatch = async (translatedTexts, startIndex = null, isFailedBatch = false) => {
      try {
        console.log(`🔍 updateTranslatedBatch called:`);
        console.log(`- translatedTexts.length: ${translatedTexts.length}`);
        console.log(`- startIndex: ${startIndex}`);
        console.log(`- isFailedBatch: ${isFailedBatch}`);
        console.log(`- continueMode: ${continueMode}`);
        console.log(`- processedCount: ${processedCount}`);
        console.log(`- subsForTranslation.length: ${subsForTranslation.length}`);
        console.log(`- state.useNovel: ${state.useNovel}`);

        // Validate input before processing
        if (!translatedTexts || translatedTexts.length === 0) {
          console.error(`❌ Empty or invalid translatedTexts received, skipping batch update`);
          return;
        }

        // Validate that we have valid translations
        const validTranslations = translatedTexts.filter(text => text && typeof text === 'string' && text.trim().length > 0);
        if (validTranslations.length !== translatedTexts.length) {
          console.warn(`⚠️ Some translations are invalid: ${translatedTexts.length} total, ${validTranslations.length} valid`);
          console.warn(`Invalid translations:`, translatedTexts.filter(text => !text || typeof text !== 'string' || text.trim().length === 0));
        }

        // For Novel translation, validate that we have the expected number of translations
        if (state.useNovel && startIndex !== null) {
          const expectedEndIndex = startIndex + translatedTexts.length;
          if (expectedEndIndex > subsForTranslation.length) {
            console.error(`❌ Translation batch would exceed bounds: startIndex=${startIndex}, length=${translatedTexts.length}, max=${subsForTranslation.length}`);
            console.error(`❌ Skipping this batch to prevent SRT misalignment`);
            return;
          }
        }

        const updatedSubtitles = [...srtItems.value];

        // Simplified logic: Always use ID mapping for consistency
        // This ensures correct order regardless of translation service
        let successfulUpdates = 0;
        let failedUpdates = 0;
        translatedTexts.forEach((translatedText, index) => {
          // Handle failed translations (empty strings) differently
          const isEmptyTranslation = !translatedText || typeof translatedText !== 'string' || translatedText.trim().length === 0;

          if (isEmptyTranslation && !isFailedBatch) {
            console.warn(`⚠️ Skipping invalid translation at index ${index}:`, translatedText);
            return;
          }

          let subsIndex;

          if (state.useNovel) {
            // Novel translation: use startIndex provided by NovelTranslator
            subsIndex = startIndex + index;
            console.log(`🔍 Novel mode: subsIndex = ${startIndex} + ${index} = ${subsIndex}`);
          } else {
            // Standard translation: use processedCount or startIndex
            subsIndex = startIndex !== null ? startIndex + index : processedCount + index;
            console.log(`� Standard mode: subsIndex = ${subsIndex} (startIndex: ${startIndex}, processedCount: ${processedCount})`);
          }

          // Validate subsIndex bounds
          if (subsIndex >= subsForTranslation.length) {
            console.error(`❌ subsIndex ${subsIndex} out of bounds (max: ${subsForTranslation.length - 1})`);
            return;
          }

          // Get subtitle ID from subsForTranslation
          const subtitleId = subsForTranslation[subsIndex]?.index;
          if (!subtitleId) {
            console.error(`❌ No subtitle ID found at subsIndex ${subsIndex}`);
            return;
          }

          // Find subtitle in updatedSubtitles by ID
          const subtitleIndex = updatedSubtitles.findIndex(sub => sub.id === subtitleId);
          if (subtitleIndex !== -1) {
            if (isEmptyTranslation) {
              // Mark as failed for retry - don't overwrite with empty translation
              console.log(`🔍 Marking item ID ${subtitleId} as failed for retry`);
              updatedSubtitles[subtitleIndex].status = "error";
              updatedSubtitles[subtitleIndex].error = "Translation failed - will retry";
              failedUpdates++;
            } else {
              // Valid translation - update normally
              console.log(`🔍 Updating item ID ${subtitleId} at array index ${subtitleIndex}: ${translatedText.substring(0, 30)}...`);
              updatedSubtitles[subtitleIndex].translatedText = translatedText;
              updatedSubtitles[subtitleIndex].status = "translated";
              updatedSubtitles[subtitleIndex].error = undefined; // Clear any previous error
              successfulUpdates++;
            }
          } else {
            console.error(`❌ Subtitle ID ${subtitleId} not found in updatedSubtitles`);
          }
        });

        // Log batch update results
        console.log(`📊 Batch update completed: ${successfulUpdates}/${translatedTexts.length} successful updates, ${failedUpdates} failed`);

        // If this was a failed batch or has failed items, log for retry tracking
        if (isFailedBatch || failedUpdates > 0) {
          console.warn(`⚠️ Batch has ${failedUpdates} failed translations that need retry`);
        }

        // Update processedCount for tracking
        if (state.useNovel) {
          // For Novel translation, update processedCount based on actual processed items
          processedCount = startIndex + translatedTexts.length;
        } else {
          // For standard translation, update based on service behavior
          if (startIndex !== null) {
            processedCount = startIndex + translatedTexts.length;
          } else {
            processedCount += translatedTexts.length;
          }
        }

      console.log(`✅ Callback completed. Processed ${processedCount} items.`);
      console.log(`🔍 Final updatedSubtitles sample:`, updatedSubtitles.slice(0, 3).map(s => ({
        id: s.id,
        text: s.text?.substring(0, 30) + '...',
        translatedText: s.translatedText?.substring(0, 30) + '...',
        status: s.status
      })));

      console.log(`🔍 BEFORE srtItems.value assignment:`, srtItems.value.slice(0, 3).map(s => ({
        id: s.id,
        translatedText: s.translatedText?.substring(0, 30) + '...',
        status: s.status
      })));

      // Force complete reactivity by using updateSrtItems function
      updateSrtItems(updatedSubtitles);

      console.log(`🔍 AFTER srtItems.value assignment:`, srtItems.value.slice(0, 3).map(s => ({
        id: s.id,
        translatedText: s.translatedText?.substring(0, 30) + '...',
        status: s.status
      })));

      // Force reactivity update
      state.key++;
      console.log(`🔍 Forced reactivity update. New state.key: ${state.key}`);

      // Force Vue to re-render by using nextTick and triggerRef
      await nextTick();

      // Force trigger reactivity on the computed property
      if (ttsStore.currentSrtList) {
        triggerRef(ttsStore.currentSrtList);
      }

      console.log(`🔍 After nextTick and triggerRef - DOM should be updated`);

      // Additional debug: Check if DOM elements exist and have correct content
      setTimeout(() => {
        const tableRows = document.querySelectorAll('.ant-table-tbody tr');
        console.log(`🔍 DOM Check: Found ${tableRows.length} subtitle rows in DOM`);
        if (tableRows.length > 0) {
          const firstRow = tableRows[0];
          // Look for translated text in the table cells
          const cells = firstRow.querySelectorAll('td');
          if (cells.length > 2) {
            const translatedCell = cells[2]; // Assuming translated text is in 3rd column
            console.log(`🔍 DOM Check: First row translated text in DOM: "${translatedCell.textContent?.substring(0, 50)}..."`);
          }
        }
      }, 100);

      } catch (error) {
        console.error(`❌ Error in updateTranslatedBatch:`, error);
        console.error(`❌ Error details:`, {
          translatedTextsLength: translatedTexts?.length,
          startIndex,
          processedCount,
          subsForTranslationLength: subsForTranslation?.length,
          continueMode
        });
        // Don't throw the error, just log it to prevent stopping the translation
      }
    };

    // Check if Novel translation is enabled
    if (state.useNovel) {
      console.log("Using Novel translation service");
      message.info("Sử dụng dịch Novel với memory context");

      // Prepare initial memory from reference SRT if selected
      let initialMemory = ttsStore.currentSrtList?.memory || '';
      if (selectedNovelReference.value && selectedNovelReference.value !== ttsStore.currentSrtList?.name) {
        const referenceProgress = ttsStore.getNovelProgressBySrtName(selectedNovelReference.value);
        if (referenceProgress.memory) {
          initialMemory = referenceProgress.memory;
          console.log(`Using memory from reference SRT: ${selectedNovelReference.value}`);
          console.log(`Memory length: ${initialMemory.length} characters`);

          // Update current SRT memory with reference memory
          ttsStore.updateCurrentSrtMemory(initialMemory);
          message.info(`Sử dụng memory từ ${selectedNovelReference.value.split('_')[0]}`);
        }
      }

      // Use Novel translation service
      await novelTranslateService.translateSrtService({
        subs: subsForTranslation,
        batchSize: ttsStore.batchSize,
        targetLanguage: "Vietnamese",
        callback: updateTranslatedBatch,
        onProgress: (current, total) => {
          // current và total giờ là số items đã xử lý và tổng số items
          if (continueMode) {
            // In continue mode, calculate progress considering already translated items
            const remainingProgress = 100 - initialProgress; // Remaining percentage to complete
            const currentProgressInRemaining = Math.floor((current / total) * remainingProgress);
            translationProgress.value = initialProgress + currentProgressInRemaining;
            setProgressBar(translationProgress.value);
            console.log(`🔍 Continue progress: initial=${initialProgress}%, current=${current}/${total}, remaining=${remainingProgress}%, final=${translationProgress.value}%`);
          } else {
            // In full mode, use normal progress calculation
            translationProgress.value = Math.floor((current / total) * 100);
            setProgressBar(translationProgress.value);
          }

          // Save progress periodically
          if (current % 5 === 0 || current === total) {
            saveTranslationProgress();
          }
        },
        continueMode,
        initialMemory // Pass initial memory to service
      });
    } else {
      console.log("Using standard translation service");

      // Gọi dịch vụ dịch SRT với tiến độ
      let currentBatch = 1; // Biến đếm batch hiện tại
      await translateSrtService({
        subs: subsForTranslation,
        batchSize: ttsStore.batchSize,
        terminologyDictionary: ttsStore.terminologyDictionary,
        targetLanguage: "Vietnamese", // Hard-coded to Vietnamese as per requirements
        callback: updateTranslatedBatch, // Truyền hàm callback vào translateSrtService
        onProgress: (current, total) => {
          // current và total giờ là số items đã xử lý và tổng số items
          if (continueMode) {
            // In continue mode, calculate progress considering already translated items
            const progressPercentage = Math.floor(current / total * (100 - initialProgress));
            translationProgress.value = initialProgress + progressPercentage;
            setProgressBar(translationProgress.value);
          } else {
            // In full mode, use normal progress calculation
            translationProgress.value = Math.floor((current / total) * 100);
            setProgressBar(translationProgress.value);
          }

          // Save progress periodically
          if (current % 5 === 0 || current === total) {
            saveTranslationProgress();
          }
        }
      });
    }

    // Check for failed translations and retry if needed
    const failedSubtitles = srtItems.value.filter(sub => sub.status === "error" && sub.error === "Translation failed - will retry");

    if (failedSubtitles.length > 0) {
      console.log(`🔄 Found ${failedSubtitles.length} failed translations, attempting retry...`);
      message.info(`Đang thử lại dịch ${failedSubtitles.length} subtitle bị lỗi...`);

      try {
        // Prepare failed subtitles for retry
        const failedSubsForTranslation = failedSubtitles.map(item => ({
          index: item.id,
          text: item.text,
          start: item.startTime,
          end: item.endTime
        }));

        // Mark failed subtitles as translating again
        const retryUpdatedSubtitles = [...srtItems.value];
        failedSubtitles.forEach(failedSub => {
          const index = retryUpdatedSubtitles.findIndex(sub => sub.id === failedSub.id);
          if (index !== -1) {
            retryUpdatedSubtitles[index].status = "translating";
            retryUpdatedSubtitles[index].error = undefined;
          }
        });
        updateSrtItems(retryUpdatedSubtitles);

        // Retry translation for failed items
        if (state.useNovel) {
          await novelTranslateService.translateSrtService({
            subs: failedSubsForTranslation,
            batchSize: Math.min(ttsStore.batchSize, failedSubsForTranslation.length),
            targetLanguage: "Vietnamese",
            callback: updateTranslatedBatch,
            onProgress: (current, total) => {
              console.log(`🔄 Retry progress: ${current}/${total}`);
            }
          });
        } else {
          await translateSrtService({
            subs: failedSubsForTranslation,
            batchSize: Math.min(ttsStore.batchSize, failedSubsForTranslation.length),
            terminologyDictionary: ttsStore.terminologyDictionary,
            targetLanguage: "Vietnamese",
            callback: updateTranslatedBatch
          });
        }

        // Check final results
        const stillFailedCount = srtItems.value.filter(sub => sub.status === "error").length;
        if (stillFailedCount > 0) {
          message.warning(`Đã thử lại nhưng vẫn còn ${stillFailedCount} subtitle không dịch được`);
        } else {
          message.success("Đã dịch lại thành công tất cả subtitle bị lỗi!");
        }
      } catch (retryError) {
        console.error("Error during retry:", retryError);
        message.error(`Lỗi khi thử lại dịch: ${retryError.message}`);
      }
    }

    translationProgress.value = 100;
    await setProgressBar(100);

    // Save final translation progress
    saveTranslationProgress();

    const finalFailedCount = srtItems.value.filter(sub => sub.status === "error").length;
    if (finalFailedCount > 0) {
      message.warning(`Dịch hoàn tất với ${finalFailedCount} subtitle bị lỗi. Bạn có thể thử dịch lại hoặc chỉnh sửa thủ công.`);
    } else {
      message.success("Dịch hoàn tất!");
    }
  } catch (error) {
    console.error("Lỗi khi dịch với SRT Service:", error);
    translationError.value = `Có lỗi xảy ra trong quá trình dịch: ${error.message || "Unknown error"}`;

    // Đánh dấu các phụ đề chưa dịch là lỗi
    const updatedSubtitles = [...srtItems.value];
    updatedSubtitles.forEach(sub => {
      if (sub.status === "translating") {
        sub.status = "error";
        sub.error = "Dịch không thành công";
      }
    });
    updateSrtItems(updatedSubtitles);

    // Save partial progress
    saveTranslationProgress();
  } finally {
    translating.value = false;
    await setProgressBar(0);
  }
};

// Function to continue translation from where it left off
const handleContinueTranslation = async () => {
  // Use the main translation function with continue mode
  await handleTranslateWithSrtServiceTerm(true);
};

// Function to retry all failed subtitles
const handleRetryAllFailed = async () => {
  const failedSubtitles = srtItems.value.filter(sub => sub.status === "error");

  if (failedSubtitles.length === 0) {
    message.info("Không có subtitle nào bị lỗi để thử lại");
    return;
  }

  message.info(`Đang thử lại dịch ${failedSubtitles.length} subtitle bị lỗi...`);

  try {
    // Configure the translate service from store
    const success = configureFromStore(ttsStore);
    if (!success) {
      throw new Error("Failed to configure translation service - no active service or API key");
    }

    // Prepare failed subtitles for retry
    const failedSubsForTranslation = failedSubtitles.map(item => ({
      index: item.id,
      text: item.text,
      start: item.startTime,
      end: item.endTime
    }));

    // Mark failed subtitles as translating again
    const retryUpdatedSubtitles = [...srtItems.value];
    failedSubtitles.forEach(failedSub => {
      const index = retryUpdatedSubtitles.findIndex(sub => sub.id === failedSub.id);
      if (index !== -1) {
        retryUpdatedSubtitles[index].status = "translating";
        retryUpdatedSubtitles[index].error = undefined;
      }
    });
    updateSrtItems(retryUpdatedSubtitles);

    // Retry translation for failed items
    if (state.useNovel) {
      await novelTranslateService.translateSrtService({
        subs: failedSubsForTranslation,
        batchSize: Math.min(ttsStore.batchSize, failedSubsForTranslation.length),
        targetLanguage: "Vietnamese",
        callback: updateTranslatedBatch,
        onProgress: (current, total) => {
          console.log(`🔄 Retry all failed progress: ${current}/${total}`);
        }
      });
    } else {
      await translateSrtService({
        subs: failedSubsForTranslation,
        batchSize: Math.min(ttsStore.batchSize, failedSubsForTranslation.length),
        terminologyDictionary: ttsStore.terminologyDictionary,
        targetLanguage: "Vietnamese",
        callback: updateTranslatedBatch
      });
    }

    // Check final results
    const stillFailedCount = srtItems.value.filter(sub => sub.status === "error").length;
    if (stillFailedCount > 0) {
      message.warning(`Đã thử lại nhưng vẫn còn ${stillFailedCount} subtitle không dịch được`);
    } else {
      message.success("Đã dịch lại thành công tất cả subtitle bị lỗi!");
    }
  } catch (retryError) {
    console.error("Error during retry all failed:", retryError);
    message.error(`Lỗi khi thử lại dịch: ${retryError.message}`);
  }
};

const handleSuggestBetterTranslation = async (id, originalText, currentTranslation) => {
  // Kiểm tra API key cho service hiện tại
  const activeService = ttsStore.getActiveAiService();
  if (!activeService || !activeService.enabled || !activeService.apiKey) {
    const serviceName = activeService ? activeService.name : ttsStore.selectedAiService;
    message.error(t('errors.apiKeyRequired') + ` (${serviceName})`);
    return [];
  }

  let targetLanguage = "Vietnamese";
  try {
    // Đảm bảo translation service được configure
    const success = configureFromStore(ttsStore);
    if (!success) {
      throw new Error('Failed to configure translation service');
    }

    // Xác định ngôn ngữ nguồn dựa trên ngôn ngữ đích
    const sourceLanguage = targetLanguage === "Vietnamese" ? "Chinese" : "Vietnamese";

    // Tạo prompt riêng cho gợi ý bản dịch tốt hơn
    const suggestPrompt = `Hãy đưa ra 3 phiên bản dịch HOÀN TOÀN KHÁC NHAU cho đoạn văn bản sau, mỗi phiên bản với phong cách và cách diễn đạt riêng biệt.

- Văn bản gốc (${sourceLanguage}): "${originalText}"
- Bản dịch hiện tại (${targetLanguage}): "${currentTranslation}"

Yêu cầu cụ thể cho mỗi phiên bản:

1. PHIÊN BẢN THÔNG DỤNG: Ngôn ngữ tự nhiên, dễ hiểu cho số đông người xem. Sử dụng từ ngữ phổ thông, đơn giản mà vẫn diễn đạt đầy đủ ý nghĩa.

2. PHIÊN BẢN HỌC THUẬT: Sát nghĩa với văn bản gốc, sử dụng thuật ngữ chính xác và ngôn ngữ trang trọng. Diễn đạt chặt chẽ về mặt ngữ nghĩa và cú pháp.

3. PHIÊN BẢN SÁNG TẠO: Tự do hơn về mặt diễn đạt, có thể dùng thành ngữ, cách nói địa phương hoặc biểu đạt hiện đại. Truyền tải không chỉ nội dung mà cả cảm xúc và tinh thần của văn bản gốc.

Đảm bảo ba phiên bản phải ĐỦ KHÁC BIỆT để người dùng có những lựa chọn đa dạng. Trả về chính xác 3 phiên bản, mỗi phiên bản trên một dòng, không có đánh số, không có giải thích.`;

    console.log(`🔧 Suggesting better translations using service: ${ttsStore.selectedAiService}`);

    // Sử dụng translateSrtService với custom prompt
    const mockSubtitles = [{
      id: 1,
      text: originalText,
      translatedText: '',
      status: 'pending'
    }];

    // Tạo custom prompt cho suggestion
    const originalPrompt = ttsStore.customPrompt;
    ttsStore.customPrompt = suggestPrompt;

    try {
      const result = await translateSrtService({
        subs: mockSubtitles,
        batchSize: 1,
        terminologyDictionary: {},
        targetLanguage: targetLanguage
      });

      // Restore original prompt
      ttsStore.customPrompt = originalPrompt;

      // Debug logging
      console.log('🔍 Translation result structure:', result);
      console.log('🔍 Result type:', typeof result);
      console.log('🔍 Result length:', result?.length);
      console.log('🔍 First item:', result?.[0]);

      // Handle different response structures
      let responseText = null;

      if (result && result.length > 0) {
        // Try different possible response structures
        if (result[0].translatedText) {
          responseText = result[0].translatedText;
        } else if (result[0].text) {
          responseText = result[0].text;
        } else if (typeof result[0] === 'string') {
          responseText = result[0];
        } else if (result[0].content) {
          responseText = result[0].content;
        }
      } else if (typeof result === 'string') {
        responseText = result;
      } else if (result && result.translatedText) {
        responseText = result.translatedText;
      } else if (result && result.text) {
        responseText = result.text;
      }

      console.log('🔍 Extracted response text:', responseText);

      if (responseText) {
        // Parse response để lấy 3 suggestions
        const lines = responseText.split('\n').filter(line => line.trim());

        let suggestions = [];

        // Lấy tối đa 3 dòng đầu tiên làm suggestions
        if (lines.length >= 3) {
          suggestions = lines.slice(0, 3);
        } else if (lines.length > 0) {
          suggestions = [...lines];
          // Thêm current translation để đủ 3 options
          while (suggestions.length < 3) {
            suggestions.push(currentTranslation);
          }
        } else {
          suggestions = [currentTranslation, currentTranslation, currentTranslation];
        }

        console.log(`📝 Generated ${suggestions.length} translation suggestions:`, suggestions);
        return suggestions;
      } else {
        console.warn('⚠️ No valid response text found, using fallback');
        return [currentTranslation, currentTranslation, currentTranslation];
      }
    } catch (translationError) {
      // Restore original prompt in case of error
      ttsStore.customPrompt = originalPrompt;
      throw translationError;
    }

  } catch (error) {
    console.error("Error suggesting better translations:", error);
    message.error('Failed to generate translation suggestions: ' + error.message);
    return [currentTranslation];
  }
};

// Voice configuration functions
const showVoiceConfigDrawer = () => {
  voiceConfigDrawerVisible.value = true;
};

const closeVoiceConfigDrawer = () => {
  voiceConfigDrawerVisible.value = false;
};

const applyVoiceConfig = (config) => {
  ttsStore.selectedVoiceConfig = { ...config };
  message.success(t('voiceConfig.configApplied'));
};

const applyVoiceToAll = (voiceNumber) => {
  // Initialize voices object if it doesn't exist
  if (!selectedVoices.value) {
    selectedVoices.value = {};
  }

  // Check if this voice is already enabled for most subtitles
  const enabledCount = srtItems.value.filter(s =>
    selectedVoices.value[s.id]?.[`voice${voiceNumber}`] === 1
  ).length;

  const shouldEnable = enabledCount < srtItems.value.length / 2;

  // Apply the specified voice to all subtitles
  const currentItems = [...srtItems.value];
  currentItems.forEach(item => {
    if (!selectedVoices.value[item.id]) {
      selectedVoices.value[item.id] = {
        voice1: 0,
        voice2: 0,
        voice3: 0,
        voice4: 0,
        voice5: 0
      };
    }

    if (shouldEnable) {
      // If we're enabling this voice, disable all other voices
      selectedVoices.value[item.id].voice1 = 0;
      selectedVoices.value[item.id].voice2 = 0;
      selectedVoices.value[item.id].voice3 = 0;
      selectedVoices.value[item.id].voice4 = 0;
      selectedVoices.value[item.id].voice5 = 0;

      // Enable only the selected voice
      selectedVoices.value[item.id][`voice${voiceNumber}`] = 1;
      item.isVoice = voiceNumber;
    } else {
      // If we're disabling, just disable the selected voice
      selectedVoices.value[item.id][`voice${voiceNumber}`] = 0;
      item.isVoice = 0;
    }
  });

  // Update srtItems to trigger computed property setter
  srtItems.value = currentItems;

  // Force reactivity update
  selectedVoices.value = { ...selectedVoices.value };

  message.success(shouldEnable ?
    `${t('voiceConfig.voice' + voiceNumber)} ${t('voiceConfig.enabledForAll')}` :
    `${t('voiceConfig.voice' + voiceNumber)} ${t('voiceConfig.disabledForAll')}`
  );
};

// Helper function to get voice name from voice ID (supports multi-engine)
function getVoiceNameById(voiceId) {
  if (!voiceId) return '';

  // console.log(`🔍 getVoiceNameById - Looking for voice ID:`, voiceId);

  // Try to find in CapCut/TikTok speakers first
  const capCutVoice = ttsStore.speakers?.find(s => s.id === voiceId);
  if (capCutVoice) {
    // console.log(`🔍 Found CapCut voice:`, capCutVoice.name);
    return `${capCutVoice.name} (CapCut)`;
  }

  // Check OpenAI voices
  if (voiceId.startsWith('openai_')) {
    const parts = voiceId.split('_');
    const originalId = parts[1];
    const langCode = parts[2];
    const openaiVoice = ttsStore.getOpenAIVoicesByLanguage(langCode)?.find(v => v.id === originalId);
    if (openaiVoice) return `${openaiVoice.name} (OpenAI)`;
  }

  // Check Mimimax voices
  if (voiceId.startsWith('mimimax_')) {
    const originalId = voiceId.replace('mimimax_', '');
    const mimimaxVoice = ttsStore.getMimimaxVoices()?.find(v => v.id === originalId);
    if (mimimaxVoice) return `${mimimaxVoice.name} (Mimimax)`;
  }

  // Check VBee voices
  if (voiceId.startsWith('vbee_')) {
    const originalId = voiceId.replace('vbee_', '');
    const vbeeVoice = ttsStore.getVbeeVoices()?.find(v => v.id === originalId);
    if (vbeeVoice) return `${vbeeVoice.name} (VBee)`;
  }

  // Check Edge TTS voices
  if (voiceId.startsWith('edge_')) {
    const originalId = voiceId.replace('edge_', '');
    const edgeVoice = edgeVoices.find(v => v.ShortName === originalId);
    if (edgeVoice) return `${edgeVoice.FriendlyName} (Edge)`;
  }

  // Check if it's a direct Edge TTS voice ID (by ShortName)
  const edgeVoice = edgeVoices.find(v => v.ShortName === voiceId);
  if (edgeVoice) {
    // console.log(`🔍 Found Edge TTS voice:`, edgeVoice.FriendlyName);
    return `${edgeVoice.FriendlyName} (Edge)`;
  }

  // Check if it's a direct VBee voice ID (without prefix)
  const vbeeVoices = ttsStore.getVbeeVoices();
  // console.log(`🔍 Available VBee voices:`, vbeeVoices?.length || 0);
  const directVbeeVoice = vbeeVoices?.find(v => v.id === voiceId);
  if (directVbeeVoice) {
    // console.log(`🔍 Found direct VBee voice:`, directVbeeVoice.name);
    return `${directVbeeVoice.name} (VBee)`;
  }

  // console.log(`🔍 Voice not found, returning ID:`, voiceId);
  return voiceId; // fallback to voice ID if not found
}

function isVoiceSelected(voiceNumber) {
  const selectedVoiceId = ttsStore.selectedVoiceConfig[`voice${voiceNumber}`].speaker;
  // console.log(`🔍 isVoiceSelected - Voice ${voiceNumber} ID:`, selectedVoiceId);
  const voiceName = getVoiceNameById(selectedVoiceId);
  // console.log(`🔍 isVoiceSelected - Voice ${voiceNumber} Name:`, voiceName);
  return voiceName;
}




const toggleVoiceForSubtitle = (subtitleId, voiceNumber) => {
  // Initialize if not exists
  if (!selectedVoices.value[subtitleId]) {
    selectedVoices.value[subtitleId] = {
      voice1: 0,
      voice2: 0,
      voice3: 0,
      voice4: 0,
      voice5: 0
    };
  }

  // Get current value
  const currentValue = selectedVoices.value[subtitleId][`voice${voiceNumber}`];

  if (currentValue === 0) {
    // If we're enabling this voice, disable all other voices first
    selectedVoices.value[subtitleId].voice1 = 0;
    selectedVoices.value[subtitleId].voice2 = 0;
    selectedVoices.value[subtitleId].voice3 = 0;
    selectedVoices.value[subtitleId].voice4 = 0;
    selectedVoices.value[subtitleId].voice5 = 0;

    // Then enable the selected voice
    selectedVoices.value[subtitleId][`voice${voiceNumber}`] = 1;

    // Update srtItems to trigger computed property setter
    const currentItems = [...srtItems.value];
    const item = currentItems.find(item => item.id === subtitleId);
    if (item) {
      item.isVoice = voiceNumber;
      srtItems.value = currentItems;
    }
  } else {
    // If we're disabling, just disable the selected voice
    selectedVoices.value[subtitleId][`voice${voiceNumber}`] = 0;

    // Update srtItems to trigger computed property setter
    const currentItems = [...srtItems.value];
    const item = currentItems.find(item => item.id === subtitleId);
    if (item) {
      item.isVoice = 0;
      srtItems.value = currentItems;
    }
  }

  // Update the selectedVoices ref to trigger reactivity
  selectedVoices.value = { ...selectedVoices.value };
};


// Audio generation functions
const generateAudioForSubtitle = async (subtitle, useTranslatedText = true, isBatch = false) => {

  // if (generatingAudio.value) return;

  const text = useTranslatedText ? subtitle.translatedText : subtitle.text;
  if (!text) {
    if (!isBatch) message.error(useTranslatedText ? 'No translated text available' : 'No text available');
    return;
  }

  // Check if any voice is selected for this subtitle
  if (!subtitle.isVoice || subtitle.isVoice === 0) {
    if (!isBatch) message.error(t('voiceConfig.selectVoice'));
    return;
  }

  try {
    if (!isBatch) {
      generatingAudio.value = true;
    }
    state.currentPlayingSubtitleId = state.syncTable ? subtitle.id : null

    // Generate audio for the selected voice
    const voiceNumber = subtitle.isVoice;
    const voiceConfig = ttsStore.selectedVoiceConfig[`voice${voiceNumber}`];
    await generateSingleVoiceAudio(subtitle, text, voiceConfig, voiceNumber);

    if (!isBatch) message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio:', error);
    if (!isBatch) message.error('Error generating audio: ' + error.message);
  } finally {
    if (!isBatch) {
      generatingAudio.value = false;
      state.currentPlayingSubtitleId = null;
    }
  }
};
function getRandomBoolean() {
  return Math.random() < 0.5;
}
function slugify(text) {
  return text
    .normalize('NFD') // Tách dấu khỏi ký tự
    .replace(/[\u0300-\u036f]/g, '') // Xóa các dấu
    .replace(/đ/g, 'd') // Chuyển đ -> d
    .replace(/Đ/g, 'd')
    .toLowerCase()
    .replace(/\s+/g, '-') // Thay khoảng trắng bằng -
    .replace(/[^\w\-]+/g, '') // Xóa ký tự không phải chữ/số/gạch ngang
    .replace(/\-\-+/g, '-') // Gộp nhiều dấu - liên tiếp
    .replace(/^-+|-+$/g, ''); // Xóa - ở đầu và cuối
}
const generateSingleVoiceAudio = async (subtitle, text, voiceConfig, voiceNumber) => {
  if (!voiceConfig.enabled) return;

  // Helper functions for multi-engine support
  const detectEngineFromVoiceId = (voiceId) => {
    if (voiceId.startsWith('openai_')) return 'openai';
    if (voiceId.startsWith('mimimax_')) return 'mimimax';
    if (voiceId.startsWith('vbee_')) return 'vbee';
    if (voiceId.startsWith('edge_')) return 'edge';

    // Check if it's a direct VBee voice ID (without prefix)
    const isVbeeVoice = ttsStore.getVbeeVoices()?.some(v => v.id === voiceId);
    if (isVbeeVoice) return 'vbee';

    // Check if it's an Edge TTS voice (by ShortName pattern)
    const isEdgeVoice = edgeVoices.some(v => v.ShortName === voiceId);
    if (isEdgeVoice) return 'edge';

    return 'capcut'; // default for CapCut/TikTok voices
  };

  const extractOriginalVoiceId = (voiceId) => {
    if (voiceId.startsWith('openai_')) {
      const parts = voiceId.split('_');
      return parts[1]; // return the original voice ID
    }
    if (voiceId.startsWith('mimimax_')) {
      return voiceId.replace('mimimax_', '');
    }
    if (voiceId.startsWith('vbee_')) {
      return voiceId.replace('vbee_', '');
    }

    // For direct VBee voice IDs or CapCut/TikTok voices, return as-is
    return voiceId;
  };

  // Detect engine from voice ID
  let detectedEngine = detectEngineFromVoiceId(voiceConfig.speaker);
  const originalVoiceId = extractOriginalVoiceId(voiceConfig.speaker);

  if(!isCapcutVoice.value && detectedEngine === 'capcut') {
    detectedEngine = 'tiktok';
  }

  console.log(`🎵 Voice ${voiceNumber}: Using ${detectedEngine} engine with voice ${originalVoiceId}`);

  const audio_config = {
    speech_rate: voiceConfig.speed,
    volume_gain_db: voiceConfig.volume,
    pitch: voiceConfig.pitch,
    rate: voiceConfig.rate,
    trim: voiceConfig.trim
  };

  try {
    let requestConfig = {
      text: text,
      speaker: originalVoiceId, // Use original voice ID
      workspaceId: ttsStore.workspaceId,
      cookie: ttsStore.cookie,
      typeEngine: detectedEngine, // Use detected engine instead of global setting
      language: ttsStore.selectedVoiceConfig.language,
      audio_config
    };

    // Add engine-specific config based on detected engine
    if (detectedEngine === 'openai') {
      requestConfig.openaiConfig = {
        apiKey: ttsStore.openaiTTS.apiKey,
        baseURL: ttsStore.openaiTTS.baseURL,
        speed: voiceConfig.speed || ttsStore.openaiTTS.speed,
        format: ttsStore.openaiTTS.format
      };
    }

    if (detectedEngine === 'mimimax') {
      requestConfig.mimimaxConfig = {
        apiKey: ttsStore.mimimaxTTS.apiKey,
        groupId: ttsStore.mimimaxTTS.groupId,
        selectedVoice: originalVoiceId,
        voiceSettings: {
          speed: voiceConfig.speed || ttsStore.mimimaxTTS.voiceSettings.speed,
          vol: ttsStore.mimimaxTTS.voiceSettings.vol,
          pitch: voiceConfig.pitch || ttsStore.mimimaxTTS.voiceSettings.pitch
        },
        audioSettings: ttsStore.mimimaxTTS.audioSettings
      };
    }

    if (detectedEngine === 'vbee') {
      requestConfig.vbeeConfig = {
        apiKey: ttsStore.vbee.apiKey,
        baseURL: ttsStore.vbee.baseURL,
        selectedVoice: originalVoiceId,
        voiceSettings: {
          speed: voiceConfig.speed || ttsStore.vbee.voiceSettings.speed,
          volume: ttsStore.vbee.voiceSettings.volume,
          pitch: voiceConfig.pitch || ttsStore.vbee.voiceSettings.pitch
        },
        audioSettings: ttsStore.vbee.audioSettings
      };
    }

    // Handle Edge TTS directly without going through electronAPI.generateTTS
    if (detectedEngine === 'edge') {
      try {
        // Generate unique output path for this audio
        const timestamp = Date.now();
        const currentDir = (await window.electronAPI.getCurrentDir()).currentDir
        const outputPath = await window.electronAPI.invoke('path:join',currentDir,`edge_tts_${slugify(text).slice(0, 24)}_${timestamp}.mp3`);

        // Call Edge TTS generation
        await edgeTts.generateAudioByEdgeTTS({
          text: text,
          voice: originalVoiceId,
          outputPath: outputPath,
          pitch: voiceConfig.pitch !== undefined ? voiceConfig.pitch : 0,
          rate: voiceConfig.speed !== undefined ? voiceConfig.speed : 0,
        });

        // Get audio duration using ffprobe
        const duration = await window.electronAPI.invoke('audio:getDuration', outputPath);

        // Create response object similar to other engines
        const response = {
          success: true,
          audioUrl: outputPath,
          duration: duration || 0
        };

        // Update the subtitle with the audio URL
        const updatedSubtitles = [...srtItems.value];
        const index = updatedSubtitles.findIndex(s => s.id === subtitle.id);

        if (index !== -1) {
          updatedSubtitles[index] = {
            ...updatedSubtitles[index],
            audioUrl: response.audioUrl,
            [`audioUrl${voiceNumber}`]: response.audioUrl,
            [`audioDuration${voiceNumber}`]: response.duration,
            [`isGenerated${voiceNumber}`]: true
          };

          srtItems.value = updatedSubtitles;

          // Add to generated audios in the store
          const newAudio = {
            id: Date.now() + voiceNumber,
            text: text,
            speaker: originalVoiceId,
            url: response.audioUrl,
            duration: response.duration,
            timestamp: new Date().toISOString(),
            voice: getVoiceNameById(voiceConfig.speaker),
            subtitleId: subtitle.id,
            voiceNumber: voiceNumber,
            engine: detectedEngine
          };

          ttsStore.generatedAudios.push(newAudio);
        }

        return; // Exit early for Edge TTS
      } catch (error) {
        console.error(`❌ Error generating Edge TTS audio:`, error);
        throw new Error(`Failed to generate audio using Edge TTS: ${error.message}`);
      }
    }

    const response = await window.electronAPI.generateTTS(JSON.parse(JSON.stringify(requestConfig)));

    if (response.success) {
      // Update the subtitle with the audio URL
      const updatedSubtitles = [...srtItems.value];
      const index = updatedSubtitles.findIndex(s => s.id === subtitle.id);

      if (index !== -1) {
        updatedSubtitles[index] = {
          ...updatedSubtitles[index],
          audioUrl: response.audioUrl,
          [`audioUrl${voiceNumber}`]: response.audioUrl,
          [`audioDuration${voiceNumber}`]: response.duration,
          [`isGenerated${voiceNumber}`]: true
        };


        srtItems.value = updatedSubtitles;

        // Add to generated audios in the store
        const newAudio = {
          id: Date.now() + voiceNumber,
          text: text,
          speaker: originalVoiceId, // Use original voice ID
          url: response.audioUrl,
          duration: response.duration,
          timestamp: new Date().toISOString(),
          voice: getVoiceNameById(voiceConfig.speaker),
          subtitleId: subtitle.id,
          voiceNumber: voiceNumber,
          engine: detectedEngine // Store which engine was used
        };

        ttsStore.generatedAudios.push(newAudio);
      }
    } else {
      throw new Error(`${detectedEngine.toUpperCase()} TTS failed: ${response.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`❌ Error generating audio for voice ${voiceNumber} using ${detectedEngine} engine:`, error);
    throw new Error(`Failed to generate audio using ${detectedEngine.toUpperCase()} engine: ${error.message}`);
  }
};

const generateAllAudio = async (useTranslatedText = true) => {
  if (generatingAudio.value) return;

  if (srtItems.value.length === 0) {
    message.error('No subtitles available');
    return;
  }

  try {
    generatingAudio.value = true;
    audioProgress.value = 0;

    // Filter subtitles that have voices selected
    const subtitlesToProcess = srtItems.value.filter(subtitle =>
      subtitle.isVoice && subtitle.isVoice > 0
    );

    if (subtitlesToProcess.length === 0) {
      message.error(t('voiceConfig.selectVoice'));
      generatingAudio.value = false;
      return;
    }
    const concurrency = ttsStore.concurrency;
    console.log('subtitlesToProcess', subtitlesToProcess);
    console.log(`Processing with concurrency: ${concurrency}`);

    let processedCount = 0;

    // Process subtitles in batches with specified concurrency
    for (let i = 0; i < subtitlesToProcess.length; i += concurrency) {
      // Check if stopping was requested
      if (isStopping.value) {
        isStopping.value = false;
        break;
      }

      const batch = subtitlesToProcess.slice(i, i + concurrency);

      // Process batch in parallel
      const batchPromises = batch.map(async (subtitle) => {
        if (!subtitle.isEnabled) return;
        if (isStopping.value) return;

        state.currentPlayingSubtitleId =state.syncTable ?  subtitle.id : null
        await generateAudioForSubtitle(subtitle, useTranslatedText, true);
        processedCount++;

        // Update progress
        audioProgress.value = Math.floor((processedCount / subtitlesToProcess.length) * 100);
        await setProgressBar(audioProgress.value);
      });

      // Wait for current batch to complete before moving to next batch
      await Promise.all(batchPromises);

      console.log(`Completed batch ${Math.floor(i / concurrency) + 1}/${Math.ceil(subtitlesToProcess.length / concurrency)}`);
    }

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio for all subtitles:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    generatingAudio.value = false;
    audioProgress.value = 100;
    state.currentPlayingSubtitleId = null;
    await setProgressBar(100);
    setTimeout(async () => {
      await setProgressBar(0);
    }, 1000);
  }
};

async function setProgressBar(value) {
  await electronAPI.invoke('taskbar:progress', value);
}


async function generateAllAudioIsNotGenerated(useTranslatedText = true) {
  if (generatingAudio.value) return;

  if (srtItems.value.length === 0) {
    message.error('No subtitles available');
    return;
  }

  try {
    generatingAudio.value = true;
    audioProgress.value = 0;

    // Filter subtitles that have voices selected and not yet generated
    const subtitlesToProcess = srtItems.value.filter(subtitle =>
      subtitle.isVoice && subtitle.isVoice > 0 && !subtitle[`isGenerated${subtitle.isVoice}`]
    );

    if (subtitlesToProcess.length === 0) {
      message.error(t('voiceConfig.nothingToGenerate'));
      generatingAudio.value = false;
      return;
    }
    const concurrency = ttsStore.concurrency;
    console.log('subtitlesToProcess (not generated)', subtitlesToProcess);
    console.log(`Processing with concurrency: ${concurrency}`);

    let processedCount = 0;

    // Process subtitles in batches with specified concurrency
    for (let i = 0; i < subtitlesToProcess.length; i += concurrency) {
      // Check if stopping was requested
      if (isStopping.value) {
        isStopping.value = false;
        break;
      }

      const batch = subtitlesToProcess.slice(i, i + concurrency);

      // Process batch in parallel
      const batchPromises = batch.map(async (subtitle) => {
        if (!subtitle.isEnabled) return;
        if (isStopping.value) return;

        state.currentPlayingSubtitleId = state.syncTable ?  subtitle.id : null
        await generateAudioForSubtitle(subtitle, useTranslatedText, true);
        processedCount++;

        // Update progress
        audioProgress.value = Math.floor((processedCount / subtitlesToProcess.length) * 100);
        await setProgressBar(audioProgress.value);
      });

      // Wait for current batch to complete before moving to next batch
      await Promise.all(batchPromises);

      console.log(`Completed batch ${Math.floor(i / concurrency) + 1}/${Math.ceil(subtitlesToProcess.length / concurrency)}`);
    }

    message.success(t('voiceConfig.audioGenerated'));
  } catch (error) {
    console.error('Error generating audio for all subtitles:', error);
    message.error('Error generating audio: ' + error.message);
  } finally {
    generatingAudio.value = false;
    audioProgress.value = 100;
    state.currentPlayingSubtitleId = null;
    await setProgressBar(100);
    setTimeout(async () => {
      await setProgressBar(0);
    }, 1000);
  }
}

function stopGeneratingAudio() {
  isStopping.value = true;
  generatingAudio.value = false;
}





function handlePlayVideo(record) {
  if(!state.syncTable) return
  state.currentPlayingSubtitleId = record.id;
  if (state.videoPlayer && state.videoPlayer.$refs?.video) {
    state.videoPlayer.$refs.video.currentTime = record.startTime
    // play from start time
    state.videoPlayer.$refs.video.play();
    record.isPlayable = true;
    // pause is end time
    setTimeout(() => {
      state.videoPlayer.$refs.video.pause();
      record.isPlayable = false;
    }, (record.endTime - record.startTime) * 1200);
    return;
  }

  if (state.videoElement) {
    state.videoElement.currentTime = record.startTime;
    state.videoElement.play();
    record.isPlayable = true;
    // pause is end time
    setTimeout(() => {
      state.videoElement.pause();
      record.isPlayable = false;
    }, (record.endTime - record.startTime) * 1200);
  }
}

function toggleCapcutVoiceEnabled() {
  if(isCapcutVoice.value){
    isCapcutVoice.value = true;
  } else {
    isCapcutVoice.value = false;
  }
}

function toggleEdgeVoiceEnabled() {
  console.log('toggleEdgeVoiceEnabled', isEdgeVoice.value);
  // Edge TTS doesn't need to change the global typeEngine since it's handled per voice
  // This is just for UI indication
}

async function testEdgeTTS() {
  try {
    console.log('Testing Edge TTS...');
    message.info('Testing Edge TTS...');

    // Generate unique output path for test
    const timestamp = Date.now();
    const outputPath = await window.electronAPI.invoke('path:join', [
      await window.electronAPI.invoke('path:temp'),
      `edge_tts_test_${timestamp}.mp3`
    ]);

    // Test with a simple Vietnamese voice
    await edgeTts.generateAudioByEdgeTTS({
      text: "Xin chào, đây là test Edge TTS",
      voice: "vi-VN-HoaiMyNeural", // Vietnamese female voice
      outputPath: outputPath,
      pitch: 0,
      rate: 0,
    });

    // Get audio duration
    const duration = await window.electronAPI.invoke('audio:getDuration', outputPath);

    console.log('Edge TTS test successful!', { outputPath, duration });
    message.success(`Edge TTS test successful! Duration: ${duration?.toFixed(2)}s`);

    // Optionally play the audio
    const audio = new Audio(`file://${outputPath}`);
    audio.play().catch(err => console.log('Audio play error:', err));

  } catch (error) {
    console.error('Edge TTS test failed:', error);
    message.error('Edge TTS test failed: ' + error.message);
  }
}


function toggleAllEnabled() {
  allEnabled.value = !allEnabled.value;
  indeterminate.value = false;

  // Update srtItems to trigger computed property setter
  const currentItems = [...srtItems.value];
  currentItems.forEach(item => {
    item.isEnabled = allEnabled.value;
  });
  srtItems.value = currentItems;
}

function checkIndeterminate() {
  const enabledCount = srtItems.value.filter(item => item.isEnabled).length;
  indeterminate.value = enabledCount > 0 && enabledCount < srtItems.value.length;
}
function formatTimestamp(timeInSeconds) {
  const hours = Math.floor(timeInSeconds / 3600);
  const minutes = Math.floor((timeInSeconds % 3600) / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  const milliseconds = Math.floor((timeInSeconds - Math.floor(timeInSeconds)) * 1000);
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
}


async function exportSrt() {
  const content = srtItems.value.map(item => {
    return `${item.index}\n${formatTimestamp(item.startTime)} --> ${formatTimestamp(item.endTime)}\n${item.translatedText || item.text}\n\n`;
  }).join('');

  const filePath = ttsStore.currentSrtList.path.replace('.srt', '_exported.srt');
  await electronAPI.saveFile(filePath, content);
  message.success('Exported SRT file');
}

async function copySrtToText() {
  //  get text only
  const content = srtItems.value.map(item => {
    return `${item.translatedText || item.text}\n`;
  }).join('');
  await navigator.clipboard.writeText(content);
  message.success('Copied SRT to clipboard');
}

async function toTranslate() {
  const confirm = await new Promise((resolve) => {
    Modal.confirm({
      title: 'Xác nhận',
      content: `Copy văn bản gốc sang/thay thế văn bản dịch`,
      okText: 'Có',
      cancelText: 'Không',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    });
  });

  if (confirm) {
    const currentItems = [...srtItems.value].map(item => ({
      ...item,
      translatedText: item.text
    }));
    srtItems.value = currentItems;
  }
}




function setConcurrency(concurrency) {
  ttsStore.concurrency = concurrency;
}

// Prompt Template Modal Methods
function openPromptTemplateModal() {
  promptTemplateModalVisible.value = true;
}

function closePromptTemplateModal() {
  promptTemplateModalVisible.value = false;
}

function handleTemplateSelected(template) {
  console.log('Template selected:', template);
  message.success(`Đã chọn template: ${template.name}`);
  // Optionally close modal after selection
  // closePromptTemplateModal();
}

// Batch Replace Modal Methods
function openBatchReplaceModal() {
  batchReplaceModalVisible.value = true;
}

function closeBatchReplaceModal() {
  batchReplaceModalVisible.value = false;
}

function handleBatchReplace(replacements) {
  console.log('Batch replace:', replacements);

  try {
    // Update srtItems with new translated text
    const currentItems = [...srtItems.value];
    let updatedCount = 0;

    replacements.forEach(replacement => {
      const index = currentItems.findIndex(item => item.id === replacement.id);
      if (index !== -1) {
        currentItems[index].translatedText = replacement.newText;
        currentItems[index].status = "translated";
        currentItems[index].isReplaced = true;
        updatedCount++;
      }
    });

    // Update the reactive array
    srtItems.value = currentItems;

    message.success(`Đã thay thế thành công ${updatedCount} subtitle`);
    closeBatchReplaceModal();
  } catch (error) {
    console.error('Error in batch replace:', error);
    message.error('Lỗi khi thay thế: ' + error.message);
  }
}

// Function to stop translation
const stopTranslation = () => {
  try {
    console.log('Stopping translation...');

    // Stop Novel translation if it's running
    if (novelTranslateService.isNovelTranslating()) {
      const stopped = novelTranslateService.stopTranslation();
      if (stopped) {
        message.info('Đã dừng Novel translation');
      }
    }

    // Stop regular translation if it's running
    if (translating.value) {
      const stopped = stopAllTranslation();
      if (stopped) {
        message.info('Đã dừng translation');
      }
    }

    // Reset translation state
    translating.value = false;
    translationProgress.value = 0;
    translationError.value = null;
    setProgressBar(0);

  } catch (error) {
    console.error('Error stopping translation:', error);
    message.error('Lỗi khi dừng translation: ' + error.message);
  }
};


</script>

<style scoped>
/* Custom scrollbar styling để match với dark theme */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6B7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
