<template>
    <div :class="{ 'video-bar': true, 'disabled': disabled }" @pointerdown="pointerDown" @pointermove="pointerMoveTooltip">
        <div class="video-bar-current" :style="{width: percent * 100 + '%'}">
            <div class="video-bar-tooltip">{{currentTime}}</div>
            <div class="video-bar-handle"></div>
        </div>
        <div class="video-bar-placeholder"></div>
    </div>

</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { frameToTime } from '@/lib/utils'

const props = defineProps<{
  percent?: number
  totalFrame?: number
  fps?: number
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'change', value: number): void
  (e: 'bar-drag-start'): void
}>()

const percentCurrent = ref(0)
const tooltipLeft = ref(0)
const pointerId = ref<number | undefined>()

const currentTime = computed(() => {
  if (props.totalFrame !== undefined && props.fps !== undefined) {
    return frameToTime(percentCurrent.value * props.totalFrame, props.fps)
  }
  return ''
})

function updatePos(event: PointerEvent) {
  const videoBarDOM = document.querySelector('.video-bar')
  if (!videoBarDOM) return

  const barRect = videoBarDOM.getBoundingClientRect()
  let percent = 0

  if (event.pageX <= barRect.left) {
    percent = 0
  } else if (event.pageX >= barRect.right) {
    percent = 1
  } else {
    percent = (event.pageX - barRect.left) / (barRect.width)
  }

  emit('change', percent)
}

function pointerDown(event: PointerEvent) {
  if (props.disabled || pointerId.value !== undefined) return

  event.preventDefault()
  pointerId.value = event.pointerId

  document.addEventListener('pointermove', pointerMove)
  document.addEventListener('pointerup', pointerUp)

  const handle = document.querySelector('.video-bar-handle')
  const tooltip = document.querySelector('.video-bar-tooltip')
  handle?.classList.add('video-bar-handle-active')
  tooltip?.classList.add('video-bar-tooltip-active')

  updatePos(event)
}

function pointerMove(event: PointerEvent) {
  if (pointerId.value !== event.pointerId) return

  event.preventDefault()
  emit('bar-drag-start')
  updatePos(event)
  pointerMoveTooltip(event)
}

function pointerUp(event: PointerEvent) {
  if (pointerId.value !== event.pointerId) return

  event.preventDefault()
  pointerId.value = undefined

  const handle = document.querySelector('.video-bar-handle')
  const tooltip = document.querySelector('.video-bar-tooltip')
  handle?.classList.remove('video-bar-handle-active')
  tooltip?.classList.remove('video-bar-tooltip-active')

  document.removeEventListener('pointermove', pointerMove)
  document.removeEventListener('pointerup', pointerUp)
}

function pointerMoveTooltip(event: PointerEvent) {
  const bar = document.querySelector('.video-bar')
  const tooltip = document.querySelector('.video-bar-tooltip') as HTMLElement | null
  if (!bar || !tooltip) return

  const barRect = bar.getBoundingClientRect()
  const x = event.pageX - barRect.left
  const width = barRect.width

  if (x <= 0) {
    tooltipLeft.value = 40
    percentCurrent.value = 0
  } else if (x >= width) {
    tooltipLeft.value = width - 40
    percentCurrent.value = 1
  } else {
    tooltipLeft.value = Math.min(Math.max(x, 40), width - 40)
    percentCurrent.value = x / width
  }
}

onBeforeUnmount(() => {
  document.removeEventListener('pointermove', pointerMove)
  document.removeEventListener('pointerup', pointerUp)
})
</script>



<style scoped>
.video-bar {
    border-radius: 10px;
    width: 100%;
    height: 4px;
    cursor: pointer;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
}

.video-bar.disabled {
    cursor: not-allowed;
}

.video-bar-current {
    height: 4px;
    background: #18a1b4;
    position: relative;
}

.video-bar-handle {
    content: "";
    width: 0;
    height: 0;
    background: white;
    position: absolute;
    right: 0;
    border-radius: 50%;
    transform: translate(50%, -25%);
    opacity: 0;
    transition: 0.3s all;
}

.video-bar-handle-active {
    opacity: 1;
    width: 10px;
    height: 10px;
}

.video-bar:hover .video-bar-handle {
    opacity: 1;
    width: 10px;
    height: 10px;
}

.video-bar.disabled:hover .video-bar-handle {
    opacity: 0;
    width: 0;
    height: 0;
}

.video-bar-tooltip {
    font-size: 12px;
    font-weight: 500;
    line-height: 12px;
    position: absolute;
    top: 0;
    transform: translate(-50%, -24px);
    color: white;
    text-shadow: 0 1px 8px #000 f;
    opacity: 0;
    transition: opacity 0.3s;
}

.video-bar:hover .video-bar-tooltip {
    opacity: 1;
}

.video-bar.disabled:hover .video-bar-tooltip {
    opacity: 0;
}

.video-bar-tooltip-active {
    opacity: 1;
}

.video-bar-placeholder {
    position: absolute;
    width: 100%;
    height: 16px;
    transform: translateY(-50%);
    left: 0;
}

</style>
