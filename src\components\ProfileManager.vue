<template>
  <div class="profile-manager">
    <a-card title="Profile Manager" size="small">
      <!-- Quick Actions -->
      <div class="quick-actions mb-4">
        <a-space>
          <a-button type="primary" @click="showCreateModal = true" size="small">
            <plus-outlined />
            Save Current as Profile
          </a-button>
          <a-button @click="showImportModal = true" size="small">
            <import-outlined />
            Import
          </a-button>
          <a-select
            v-model:value="selectedCategory"
            placeholder="Filter by category"
            allow-clear
            size="small"
            style="width: 150px"
          >
            <a-select-option value="">All Categories</a-select-option>
            <a-select-option v-for="category in profileStore.getAllCategories" :key="category" :value="category">
              {{ category }}
            </a-select-option>
          </a-select>
        </a-space>
      </div>

      <!-- Profiles List -->
      <div class="profiles-list">
        <a-row :gutter="[16, 16]">
          <a-col :span="24" v-for="profile in filteredProfiles" :key="profile.id">
            <a-card size="small" :class="{ 'current-profile': profileStore.currentProfile?.id === profile.id }">
              <template #title>
                <div class="flex justify-between items-center">
                  <span class="profile-name">{{ profile.name }}</span>
                  <a-tag :color="getCategoryColor(profile.category)" size="small">
                    {{ profile.category }}
                  </a-tag>
                </div>
              </template>
              
              <template #extra>
                <a-dropdown>
                  <a-button type="text" size="small">
                    <more-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="applyProfile(profile.id)">
                        <check-outlined />
                        Apply Profile
                      </a-menu-item>
                      <a-menu-item @click="editProfile(profile)">
                        <edit-outlined />
                        Edit
                      </a-menu-item>
                      <a-menu-item @click="duplicateProfile(profile.id)">
                        <copy-outlined />
                        Duplicate
                      </a-menu-item>
                      <a-menu-item @click="exportProfile(profile.id)">
                        <export-outlined />
                        Export
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="deleteProfile(profile.id)" danger>
                        <delete-outlined />
                        Delete
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>

              <div class="profile-content">
                <p class="profile-description">{{ profile.description || 'No description' }}</p>
                <div class="profile-meta">
                  <small class="text-gray-500">
                    Created: {{ formatDate(profile.createdAt) }}
                    <br>
                    Updated: {{ formatDate(profile.updatedAt) }}
                  </small>
                </div>
                
                <!-- Profile Preview -->
                <div class="profile-preview mt-2">
                  <a-space size="small" wrap>
                    <a-tag v-if="profile.settings.textAnimation?.showText" color="blue" size="small">
                      Text Animation
                    </a-tag>
                    <a-tag v-if="profile.settings.mediaOverlay?.showLogo" color="orange" size="small">
                      Logo
                    </a-tag>
                    <a-tag v-if="profile.settings.mediaOverlay?.showImage" color="green" size="small">
                      Image
                    </a-tag>
                    <a-tag v-if="profile.settings.mediaOverlay?.showFixedText" color="purple" size="small">
                      Fixed Text
                    </a-tag>
                    <a-tag v-if="profile.settings.subtitle?.showSubtitle" color="cyan" size="small">
                      Subtitle
                    </a-tag>
                    <a-tag v-if="profile.settings.audio?.addBackgroundMusic" color="red" size="small">
                      Background Music
                    </a-tag>
                  </a-space>
                </div>

                <!-- Quick Apply Button -->
                <div class="mt-3">
                  <a-button 
                    type="primary" 
                    size="small" 
                    block 
                    @click="applyProfile(profile.id)"
                    :loading="isApplying === profile.id"
                  >
                    Apply This Profile
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <div v-if="filteredProfiles.length === 0" class="empty-state text-center py-8">
          <a-empty description="No profiles found">
            <a-button type="primary" @click="showCreateModal = true">
              Create Your First Profile
            </a-button>
          </a-empty>
        </div>
      </div>
    </a-card>

    <!-- Create/Edit Profile Modal -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingProfile ? 'Edit Profile' : 'Save Current Settings as Profile'"
      @ok="handleCreateProfile"
      @cancel="handleCancelCreate"
      :confirm-loading="isCreating"
    >
      <a-form :model="profileForm" layout="vertical">
        <a-form-item label="Profile Name" required>
          <a-input v-model:value="profileForm.name" placeholder="Enter profile name" />
        </a-form-item>
        
        <a-form-item label="Description">
          <a-textarea 
            v-model:value="profileForm.description" 
            placeholder="Optional description"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="Category">
          <a-select 
            v-model:value="profileForm.category" 
            placeholder="Select or enter category"
            mode="combobox"
          >
            <a-select-option v-for="category in profileStore.getAllCategories" :key="category" :value="category">
              {{ category }}
            </a-select-option>
            <a-select-option value="General">General</a-select-option>
            <a-select-option value="Gaming">Gaming</a-select-option>
            <a-select-option value="Education">Education</a-select-option>
            <a-select-option value="Entertainment">Entertainment</a-select-option>
            <a-select-option value="Business">Business</a-select-option>
          </a-select>
        </a-form-item>

        <!-- Settings Preview -->
        <a-form-item label="Current Settings Preview">
          <div class="settings-preview p-3 bg-gray-50 rounded">
            <a-space size="small" wrap>
              <a-tag v-if="currentSettings.textAnimation?.showText" color="blue" size="small">
                Text Animation
              </a-tag>
              <a-tag v-if="currentSettings.mediaOverlay?.showLogo" color="orange" size="small">
                Logo
              </a-tag>
              <a-tag v-if="currentSettings.mediaOverlay?.showImage" color="green" size="small">
                Image
              </a-tag>
              <a-tag v-if="currentSettings.mediaOverlay?.showFixedText" color="purple" size="small">
                Fixed Text
              </a-tag>
              <a-tag v-if="currentSettings.subtitle?.showSubtitle" color="cyan" size="small">
                Subtitle
              </a-tag>
              <a-tag v-if="currentSettings.audio?.addBackgroundMusic" color="red" size="small">
                Background Music
              </a-tag>
            </a-space>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Import Profile Modal -->
    <a-modal
      v-model:open="showImportModal"
      title="Import Profile"
      @ok="handleImportProfile"
      @cancel="handleCancelImport"
      :confirm-loading="isImporting"
    >
      <a-form layout="vertical">
        <a-form-item label="Import Method">
          <a-radio-group v-model:value="importMethod">
            <a-radio value="file">From File</a-radio>
            <a-radio value="json">From JSON</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="importMethod === 'file'" label="Select Profile File">
          <a-upload
            :before-upload="handleFileUpload"
            accept=".json"
            :show-upload-list="false"
          >
            <a-button>
              <upload-outlined />
              Choose File
            </a-button>
          </a-upload>
          <div v-if="importFileName" class="mt-2">
            <a-tag color="green">{{ importFileName }}</a-tag>
          </div>
        </a-form-item>

        <a-form-item v-if="importMethod === 'json'" label="Paste JSON Data">
          <a-textarea
            v-model:value="importJsonData"
            placeholder="Paste profile JSON data here"
            :rows="6"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  MoreOutlined,
  CheckOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import { useProfileStore } from '@/stores/profile-store';

const profileStore = useProfileStore();

// Reactive state
const selectedCategory = ref('');
const showCreateModal = ref(false);
const showImportModal = ref(false);
const editingProfile = ref(null);
const isCreating = ref(false);
const isImporting = ref(false);
const isApplying = ref(null);
const importMethod = ref('file');
const importFileName = ref('');
const importJsonData = ref('');

// Form data
const profileForm = reactive({
  name: '',
  description: '',
  category: 'General'
});

// Computed
const filteredProfiles = computed(() => {
  if (!selectedCategory.value) {
    return profileStore.profiles;
  }
  return profileStore.getProfilesByCategory(selectedCategory.value);
});

const currentSettings = computed(() => {
  return profileStore.getCurrentSettings();
});

// Methods
function getCategoryColor(category) {
  const colors = {
    'General': 'default',
    'Gaming': 'red',
    'Education': 'blue',
    'Entertainment': 'purple',
    'Business': 'green'
  };
  return colors[category] || 'default';
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString();
}

async function applyProfile(profileId) {
  isApplying.value = profileId;
  try {
    const success = profileStore.applyProfile(profileId);
    if (success) {
      message.success('Profile applied successfully!');
    } else {
      message.error(profileStore.error || 'Failed to apply profile');
    }
  } catch (error) {
    message.error('Error applying profile: ' + error.message);
  } finally {
    isApplying.value = null;
  }
}

function editProfile(profile) {
  editingProfile.value = profile;
  profileForm.name = profile.name;
  profileForm.description = profile.description;
  profileForm.category = profile.category;
  showCreateModal.value = true;
}

async function handleCreateProfile() {
  if (!profileForm.name.trim()) {
    message.error('Please enter a profile name');
    return;
  }

  isCreating.value = true;
  try {
    if (editingProfile.value) {
      // Update existing profile
      const success = profileStore.updateProfile(editingProfile.value.id, {
        name: profileForm.name,
        description: profileForm.description,
        category: profileForm.category
      });
      if (success) {
        message.success('Profile updated successfully!');
      } else {
        message.error(profileStore.error || 'Failed to update profile');
      }
    } else {
      // Create new profile
      const newProfile = profileStore.createProfile(
        profileForm.name,
        profileForm.description,
        profileForm.category
      );
      if (newProfile) {
        message.success('Profile saved successfully!');
      } else {
        message.error('Failed to save profile');
      }
    }
    handleCancelCreate();
  } catch (error) {
    message.error('Error saving profile: ' + error.message);
  } finally {
    isCreating.value = false;
  }
}

function handleCancelCreate() {
  showCreateModal.value = false;
  editingProfile.value = null;
  profileForm.name = '';
  profileForm.description = '';
  profileForm.category = 'General';
}

async function duplicateProfile(profileId) {
  try {
    const duplicated = profileStore.duplicateProfile(profileId);
    if (duplicated) {
      message.success('Profile duplicated successfully!');
    } else {
      message.error(profileStore.error || 'Failed to duplicate profile');
    }
  } catch (error) {
    message.error('Error duplicating profile: ' + error.message);
  }
}

async function exportProfile(profileId) {
  try {
    const profileData = profileStore.exportProfile(profileId);
    if (profileData) {
      const dataStr = JSON.stringify(profileData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${profileData.name.replace(/[^a-z0-9]/gi, '_')}_profile.json`;
      link.click();
      URL.revokeObjectURL(url);
      message.success('Profile exported successfully!');
    } else {
      message.error(profileStore.error || 'Failed to export profile');
    }
  } catch (error) {
    message.error('Error exporting profile: ' + error.message);
  }
}

async function deleteProfile(profileId) {
  try {
    const success = profileStore.deleteProfile(profileId);
    if (success) {
      message.success('Profile deleted successfully!');
    } else {
      message.error(profileStore.error || 'Failed to delete profile');
    }
  } catch (error) {
    message.error('Error deleting profile: ' + error.message);
  }
}

function handleFileUpload(file) {
  const reader = new FileReader();
  reader.onload = (e) => {
    importJsonData.value = e.target.result;
    importFileName.value = file.name;
  };
  reader.readAsText(file);
  return false; // Prevent auto upload
}

async function handleImportProfile() {
  if (!importJsonData.value.trim()) {
    message.error('Please provide profile data');
    return;
  }

  isImporting.value = true;
  try {
    const profileData = JSON.parse(importJsonData.value);
    const imported = profileStore.importProfile(profileData);
    if (imported) {
      message.success('Profile imported successfully!');
      handleCancelImport();
    } else {
      message.error(profileStore.error || 'Failed to import profile');
    }
  } catch (error) {
    message.error('Invalid JSON data: ' + error.message);
  } finally {
    isImporting.value = false;
  }
}

function handleCancelImport() {
  showImportModal.value = false;
  importJsonData.value = '';
  importFileName.value = '';
  importMethod.value = 'file';
}

// Initialize
onMounted(() => {
  profileStore.init();
});
</script>

<style scoped>
.current-profile {
  border: 2px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.profile-name {
  font-weight: 600;
}

.profile-description {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.profile-meta {
  margin-bottom: 8px;
}

.profile-preview {
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.settings-preview {
  min-height: 40px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.text-gray-500 {
  color: #8c8c8c;
}
</style>
