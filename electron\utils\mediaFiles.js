const fs = require('fs');
const path = require('path');
const { ffmpeg } = require('../ffmpeg-config');
const { VideoProperties } = require('./VideoProperties');

// H<PERSON>m lấy tất cả file media từ thư mục
async function getMediaFilesFromFolder(folderPath) {
  try {
    // Các extension file media được hỗ trợ
    const mediaExtensions = [
      // Video extensions
      '.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv', '.m4v', 
      '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.mts', '.m2ts', '.vob',
      '.asf', '.rm', '.rmvb', '.divx', '.xvid', '.f4v', '.swf',
      
      // Audio extensions
      '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.opus',
      '.amr', '.ac3', '.dts', '.ape', '.mka', '.ra', '.au', '.aiff',
      '.caf', '.mp2', '.m4b', '.m4p', '.mpc', '.tta', '.wv'
    ];

    // Kiểm tra thư mục có tồn tại không
    if (!fs.existsSync(folderPath)) {
      return {
        success: false,
        error: 'Folder does not exist',
        files: []
      };
    }

    // Kiểm tra có phải là thư mục không
    const stats = fs.statSync(folderPath);
    if (!stats.isDirectory()) {
      return {
        success: false,
        error: 'Path is not a directory',
        files: []
      };
    }

    const mediaFiles = [];

    // Hàm đệ quy để quét thư mục con (tùy chọn)
    function scanDirectory(currentPath, recursive = false) {
      try {
        const items = fs.readdirSync(currentPath);
        
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          
          try {
            const itemStats = fs.statSync(itemPath);
            
            if (itemStats.isFile()) {
              // Kiểm tra extension file
              const ext = path.extname(item).toLowerCase();
              if (mediaExtensions.includes(ext)) {
                mediaFiles.push({
                  name: item,
                  path: itemPath,
                  size: itemStats.size,
                  extension: ext,
                  lastModified: itemStats.mtime
                });
              }
            } else if (itemStats.isDirectory() && recursive) {
              // Quét thư mục con nếu recursive = true
              scanDirectory(itemPath, recursive);
            }
          } catch (err) {
            // Bỏ qua file/folder không thể đọc
            console.warn(`Cannot access: ${itemPath}`, err.message);
          }
        }
      } catch (err) {
        console.error(`Error scanning directory: ${currentPath}`, err.message);
      }
    }

    // Quét thư mục (chỉ cấp 1, không đệ quy)
    scanDirectory(folderPath, false);

    // Sắp xếp file theo tên
    mediaFiles.sort((a, b) => a.name.localeCompare(b.name));

    return {
      success: true,
      files: mediaFiles.map(file => file.path), // Chỉ trả về path
      fileDetails: mediaFiles, // Trả về chi tiết file nếu cần
      totalFiles: mediaFiles.length
    };

  } catch (error) {
    console.error('Error in getMediaFilesFromFolder:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
      files: []
    };
  }
}

// Hàm lấy file media từ thư mục có hỗ trợ đệ quy
async function getMediaFilesFromFolderRecursive(folderPath, maxDepth = 3) {
  try {
    const mediaExtensions = [
      '.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv', '.m4v', 
      '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.mts', '.m2ts', '.vob',
      '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.opus'
    ];

    if (!fs.existsSync(folderPath)) {
      return {
        success: false,
        error: 'Folder does not exist',
        files: []
      };
    }

    const mediaFiles = [];

    function scanDirectoryRecursive(currentPath, currentDepth = 0) {
      if (currentDepth > maxDepth) return;

      try {
        const items = fs.readdirSync(currentPath);
        
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          
          try {
            const itemStats = fs.statSync(itemPath);
            
            if (itemStats.isFile()) {
              const ext = path.extname(item).toLowerCase();
              if (mediaExtensions.includes(ext)) {
                mediaFiles.push({
                  name: item,
                  path: itemPath,
                  size: itemStats.size,
                  extension: ext,
                  lastModified: itemStats.mtime,
                  relativePath: path.relative(folderPath, itemPath)
                });
              }
            } else if (itemStats.isDirectory()) {
              scanDirectoryRecursive(itemPath, currentDepth + 1);
            }
          } catch (err) {
            console.warn(`Cannot access: ${itemPath}`, err.message);
          }
        }
      } catch (err) {
        console.error(`Error scanning directory: ${currentPath}`, err.message);
      }
    }

    scanDirectoryRecursive(folderPath);
    mediaFiles.sort((a, b) => a.relativePath.localeCompare(b.relativePath));

    return {
      success: true,
      files: mediaFiles.map(file => file.path),
      fileDetails: mediaFiles,
      totalFiles: mediaFiles.length
    };

  } catch (error) {
    console.error('Error in getMediaFilesFromFolderRecursive:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
      files: []
    };
  }
}

// Hàm lọc file media từ danh sách file
function filterMediaFiles(filePaths) {
  const mediaExtensions = [
    '.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv', '.m4v', 
    '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.mts', '.m2ts', '.vob',
    '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.opus'
  ];

  return filePaths.filter(filePath => {
    const ext = path.extname(filePath).toLowerCase();
    return mediaExtensions.includes(ext);
  });
}

// Hàm lấy thông tin chi tiết file media
async function getMediaFileInfo(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return {
        success: false,
        error: 'File does not exist'
      };
    }

    const stats = fs.statSync(filePath);
    const ext = path.extname(filePath).toLowerCase();
    const name = path.basename(filePath);

    return {
      success: true,
      info: {
        name,
        path: filePath,
        size: stats.size,
        extension: ext,
        lastModified: stats.mtime,
        created: stats.birthtime,
        isVideo: ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv'].includes(ext),
        isAudio: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'].includes(ext)
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

async function openVideo(path) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(path, (err, metadata) => {
      if (err) return reject(err)

      const videoStream = metadata.streams.find((s) => s.codec_type === 'video')
      if (!videoStream) return reject(new Error('No video stream found'))

      const width = videoStream.width || 0
      const height = videoStream.height || 0
      const duration = metadata.format.duration || 0
      const avgFrameRate = videoStream.avg_frame_rate || '1/1'
      const timeBase = videoStream.time_base || '1/1'

      const [num, den] = avgFrameRate.split('/').map(Number)
      const fps = [num, den]

      const [tbNum, tbDen] = timeBase.split('/').map(Number)
      const unitFrame = tbNum / tbDen
      const lastFrame = Math.floor(duration / unitFrame)

      const props = new VideoProperties(
        duration,
        fps,
        [tbNum, tbDen],
        width,
        height
      )

      resolve(props)
    })
  })
}










// Export các hàm để sử dụng trong main process
module.exports = {
  getMediaFilesFromFolder,
  getMediaFilesFromFolderRecursive,
  filterMediaFiles,
  getMediaFileInfo,
  openVideo
};

