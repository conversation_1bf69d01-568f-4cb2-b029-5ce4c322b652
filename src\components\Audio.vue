<template>
  <div>
    <audio ref="audio" :src="item.audioUrl" @ended="isPlaying = false" v-if="item?.audioUrl"></audio>
    <audio ref="audio" :src="src" @ended="isPlaying = false" v-if="src"></audio>
    <button @click="toggleAudio" class="flex items-center justify-center">
      <!-- {{ isPlaying ? '⏸️' : '▶️' }} -->
      <PlayCircleIcon size="22" v-if="!isPlaying" />
      <PauseCircleIcon size="22" v-if="isPlaying" />
    </button>
  </div>
</template>

<script>

import { PlayCircleIcon, PauseCircleIcon } from 'lucide-vue-next';


export default {
  components: {
    PlayCircleIcon,
    PauseCircleIcon
  },
  props: {
    item: {
      type: Object,
      required: false
    },
    src: {
      type: String,
      required: false
    }
  },
  data() {
    return {
      isPlaying: false
    };
  },
  methods: {
    toggleAudio() {
      const audio = this.$refs.audio;
      if (!audio) return;

      if (this.isPlaying) {
        audio.pause();
        this.isPlaying = false;
      } else {
        audio.play();
        this.isPlaying = true;
      }
    }
  }
};
</script>

<style scoped>
button {
  /* padding: 6px 12px; */
  font-size: 16px;
  cursor: pointer;
}
</style>
