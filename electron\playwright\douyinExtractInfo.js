
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function extractDouyinVideoInfo(videoUrl) {
  const browser = await chromium.launch({
    headless: false,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-blink-features=AutomationControlled'
    ]
  });

  try {
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1280, height: 720 },
      extraHTTPHeaders: {
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
      }
    });

    const page = await context.newPage();

    // Add stealth scripts to avoid detection
    await page.addInitScript(() => {
      // Remove webdriver property
      delete navigator.__proto__.webdriver;

      // Mock plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5]
      });

      // Mock languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['zh-CN', 'zh', 'en']
      });

      // Mock permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );
    });

    console.log('Navigating to:', videoUrl);

    // Use domcontentloaded and stop loading immediately
    await page.goto(videoUrl, { waitUntil: 'domcontentloaded' });

    // Stop any further loading immediately
    await page.evaluate(() => {
      if (window.stop) {
        window.stop();
      }
      try {
        window.history.pushState(null, '', window.location.href);
      } catch (e) {
        // Ignore errors
      }
    });

    console.log('DOM loaded, stopped loading, proceeding with extraction...');
    // Extract video ID from current URL in browser context (handles redirects)
    const extractionResult = await page.evaluate(() => {
      // Extract video ID from current URL after any redirects
      const match = location.href.match(/\/video\/(\d+)/);
      const videoId = match ? match[1] : null;

      return {
        videoId,
        currentUrl: location.href,
        originalUrl: document.referrer || 'unknown'
      };
    });

    if (!extractionResult.videoId) {
      throw new Error('Could not extract video ID from current URL: ' + extractionResult.currentUrl);
    }

    console.log('Extracted video ID from current URL:', extractionResult.videoId);
    console.log('Current URL after redirects:', extractionResult.currentUrl);

    // Get basic page info (optional - for additional metadata)
    const pageInfo = await page.evaluate(() => {
      // Find video element if available
      const videoElement = document.querySelector('video');

      // Try to get description from page
      const descSelectors = [
        '[data-e2e="video-desc"]',
        '.video-desc',
        '.video-description',
        'h1',
        '.title'
      ];

      let fileName = null;
      for (const selector of descSelectors) {
        const element = document.querySelector(selector);
        if (element && element.innerText.trim()) {
          fileName = element.innerText.trim();
          break;
        }
      }

      return {
        fileName,
        videoElement: videoElement ? {
          duration: videoElement.duration,
          width: videoElement.videoWidth,
          height: videoElement.videoHeight,
          src: videoElement.src || videoElement.currentSrc
        } : null
      };
    });

    const videoInfo = {
      videoId: extractionResult.videoId,
      videoLink: pageInfo.videoElement?.src,
      fileName: pageInfo.fileName,
      videoElement: pageInfo.videoElement,
      currentUrl: extractionResult.currentUrl,
      originalUrl: extractionResult.originalUrl
    };

    console.log('Basic video info extracted:', {
      videoId: videoInfo.videoId,
      hasVideoLink: !!videoInfo.videoLink,
      hasFileName: !!videoInfo.fileName,
      hasVideoElement: !!videoInfo.videoElement,
      currentUrl: videoInfo.currentUrl
    });

    // Video ID should already be extracted from URL, but double-check
    if (!videoInfo.videoId) {
      throw new Error('Video ID extraction failed');
    }

    // Fetch detailed video information from API
    console.log('Fetching detailed info from Douyin API...');
    if (videoInfo.videoId) {
      try {
        const detailResponse = await page.evaluate(async (aweme_id) => {
          const response = await fetch(`https://www.douyin.com/aweme/v1/web/aweme/detail?aid=6383&version_code=190500&aweme_id=${aweme_id}`, {
            method: 'GET',
            headers: {
              'Referer': 'https://www.douyin.com',
              'User-Agent': navigator.userAgent
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          return await response.json();
        }, videoInfo.videoId);

        console.log('API response received');

        // Extract relevant information
        const awemeDetail = detailResponse?.aweme_detail;
        if (awemeDetail) {
          const extractedInfo = {
            success: true,
            videoId: videoInfo.videoId,
            title: awemeDetail.desc || videoInfo.fileName || 'Douyin Video',
            description: awemeDetail.desc,
            author: {
              nickname: awemeDetail.author?.nickname,
              unique_id: awemeDetail.author?.unique_id,
              avatar: awemeDetail.author?.avatar_thumb?.url_list?.[0]
            },
            video: {
              duration: awemeDetail.duration || videoInfo.videoElement.duration,
              width: awemeDetail.video?.width || videoInfo.videoElement.width,
              height: awemeDetail.video?.height || videoInfo.videoElement.height,
              ratio: awemeDetail.video?.ratio,
              cover: awemeDetail.video?.cover?.url_list?.[0],
              dynamic_cover: awemeDetail.video?.dynamic_cover?.url_list?.[0]
            },
            formats: [],
            statistics: {
              digg_count: awemeDetail.statistics?.digg_count,
              comment_count: awemeDetail.statistics?.comment_count,
              share_count: awemeDetail.statistics?.share_count,
              play_count: awemeDetail.statistics?.play_count
            },
            create_time: awemeDetail.create_time,
            music: awemeDetail.music ? {
              title: awemeDetail.music.title,
              author: awemeDetail.music.author,
              duration: awemeDetail.music.duration
            } : null,
            extracted_at: new Date().toISOString()
          };

          // Extract video URLs
          const playAddr = awemeDetail.video?.play_addr;
          if (playAddr?.url_list) {
            playAddr.url_list.forEach((url, index) => {
              extractedInfo.formats.push({
                url: url,
                quality: index === 0 ? 'high' : 'medium',
                extension: 'mp4',
                type: 'video'
              });
            });
          }

          // Add fallback video URL if available
          if (videoInfo.videoLink && !extractedInfo.formats.some(f => f.url === videoInfo.videoLink)) {
            extractedInfo.formats.unshift({
              url: videoInfo.videoLink,
              quality: 'original',
              extension: 'mp4',
              type: 'video'
            });
          }

          return extractedInfo;
        } else {
          throw new Error('No aweme_detail found in API response');
        }

      } catch (apiError) {
        console.error('API fetch failed:', apiError);

        // Fallback to basic info
        return {
          success: false,
          error: apiError.message,
          fallback: {
            videoId: videoInfo.videoId,
            title: videoInfo.fileName || 'Douyin Video',
            formats: videoInfo.videoLink ? [{
              url: videoInfo.videoLink,
              quality: 'original',
              extension: 'mp4',
              type: 'video'
            }] : [],
            video: videoInfo.videoElement,
            extracted_at: new Date().toISOString()
          }
        };
      }
    } else {
      // Last resort: try to extract video ID from URL
      const urlMatch = videoUrl.match(/\/video\/(\d+)/);
      if (urlMatch) {
        return {
          success: false,
          error: 'Could not extract video info from page, but found video ID in URL',
          fallback: {
            videoId: urlMatch[1],
            title: 'Douyin Video',
            url: videoUrl,
            extracted_at: new Date().toISOString(),
            note: 'Minimal info extracted from URL only'
          }
        };
      } else {
        throw new Error('Could not extract video ID from page or URL');
      }
    }

  } catch (error) {
    console.error('Error extracting video info:', error);

    // Try to extract video ID from URL as absolute last resort
    const urlMatch = videoUrl.match(/\/video\/(\d+)/);
    if (urlMatch) {
      return {
        success: false,
        error: error.message,
        fallback: {
          videoId: urlMatch[1],
          title: 'Douyin Video',
          url: videoUrl,
          extracted_at: new Date().toISOString(),
          note: 'Emergency fallback - extracted from URL only due to page load failure'
        },
        extracted_at: new Date().toISOString()
      };
    }

    return {
      success: false,
      error: error.message,
      extracted_at: new Date().toISOString()
    };
  } finally {
    await browser.close();
  }
}

// Export function for use in other modules
module.exports = { extractDouyinVideoInfo };

// If run directly, extract info and save to JSON file
if (require.main === module) {
  (async () => {
    const videoUrl = process.argv[2] || 'https://www.douyin.com/video/7509343869454306618';
    console.log('Extracting info for:', videoUrl);

    const result = await extractDouyinVideoInfo(videoUrl);

    // Save to JSON file
    const outputPath = path.join(__dirname, 'douyininfo.json');
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf8');

    console.log('Result saved to:', outputPath);
    console.log('Extraction result:', result.success ? 'SUCCESS' : 'FAILED');

    if (result.success) {
      console.log('Title:', result.title);
      console.log('Author:', result.author?.nickname);
      console.log('Formats:', result.formats.length);
    } else {
      console.log('Error:', result.error);
      if (result.fallback) {
        console.log('Fallback data available');
      }
    }
  })();
}
