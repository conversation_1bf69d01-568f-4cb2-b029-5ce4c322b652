import { useTTSStore } from '@/stores/ttsStore';
import { message } from 'ant-design-vue';
import { NovelSummarizer } from './NovelSummarizer';

/**
 * Novel Summary Service
 * Uses NovelSummarizer for novel-style summarization with memory context
 */
export class NovelSummaryService {
  constructor() {
    this.isSummarizing = false;
    this.currentBatch = 0;
    this.totalBatches = 0;
  }

  /**
   * Summarize chapters using Novel summarization approach
   * @param {Object} options - Summarization options
   * @param {Array} options.chapters - Array of chapter objects to summarize
   * @param {number} options.batchSize - Size of each summarization batch (default: 10)
   * @param {Function} options.callback - Callback function for batch updates
   * @param {Function} options.onProgress - Progress callback function
   * @param {string} options.bookMemory - Initial memory from book (optional)
   * @returns {Promise<Array>} - Array of summarized texts
   */
  async summarizeChapters({
    chapters,
    batchSize = 10,
    callback = null,
    onProgress = null,
    bookMemory = null
  }) {
    const ttsStore = useTTSStore();
    
    if (!chapters || chapters.length === 0) {
      throw new Error('No chapters provided for summarization');
    }

    // Validate AI service configuration
    const activeService = ttsStore.getActiveAiService();
    if (!activeService || !activeService.enabled || !activeService.apiKey) {
      throw new Error('AI service not configured properly');
    }

    this.isSummarizing = true;

    try {
      // Get existing memory from book or use provided memory
      let memory = bookMemory || '';
      
      console.log(`Starting Novel summarization with ${chapters.length} chapters using ${activeService.name}`);
      if (memory) {
        console.log(`Using book memory: ${memory.length} characters`);
      }

      // Calculate batches
      this.totalBatches = Math.ceil(chapters.length / batchSize);

      // Create NovelSummarizer instance
      const providerName = this.getProviderName(activeService);
      const modelName = activeService.model //this.getModelForService(activeService);
      
      console.log(`Creating NovelSummarizer with:`, {
        provider: providerName,
        model: modelName,
        serviceName: activeService.name,
        hasApiKey: !!activeService.apiKey,
        baseURL: activeService.baseURL
      });
      
      const summarizer = new NovelSummarizer({
        provider: providerName,
        model: modelName,
        apiKey: activeService.apiKey,
        baseURL: activeService.baseURL,
        temperature: 0.3,
        windowSize: Math.min(batchSize, 10),
        overlap: 0 // No overlap for summaries
      });

      // Set initial memory
      if (memory) {
        summarizer.memory = memory;
      }

      const summarizedTexts = [];
      let processedItems = 0;

      // Extract chapter texts for summarization
      const chapterTexts = chapters.map(chapter => chapter.original_text || chapter.text || '');

      // Summarize chapters with progress callback
      const windowSummarizedTexts = await summarizer.summarizeChunks(chapterTexts, (current, total, message, batchMemory, windowResults) => {
        console.log(`Summarization progress: ${current}/${total} - ${message}`);

        // Update memory from summarizer
        if (batchMemory) {
          memory = batchMemory;
        }

        // Update progress
        this.currentBatch = current;

        if (onProgress) {
          // Convert window progress to item progress
          const itemsPerWindow = Math.ceil(chapters.length / total);
          const currentItems = Math.min((current - 1) * itemsPerWindow + itemsPerWindow, chapters.length);
          onProgress(currentItems, chapters.length);
        }

        // If we have window results, call callback immediately for real-time update
        if (windowResults && callback) {
          // Calculate correct start index based on processed items so far
          const startIndex = processedItems;
          
          console.log(`Calling real-time summary callback for window ${current}, startIndex: ${startIndex}, items: ${windowResults.length}`);
          
          // Create summary objects with chapter info
          const summaryBatch = windowResults.map((summaryText, index) => {
            const chapterIndex = startIndex + index;
            const originalChapter = chapters[chapterIndex];
            
            return {
              chapter_id: originalChapter?.chapter_id || chapterIndex + 1,
              chapter_title: originalChapter?.chapter_title || `Chapter ${chapterIndex + 1}`,
              summary_text: summaryText,
              original_text: originalChapter?.original_text || originalChapter?.text || '',
              memory: memory
            };
          });
          
          callback(summaryBatch, startIndex);
          
          // Update processed items counter
          processedItems += windowResults.length;
        }
      });

      // Ensure all results are captured in final array
      summarizedTexts.push(...windowSummarizedTexts);
      
      console.log(`Total processed via callbacks: ${processedItems}, Total summarized: ${windowSummarizedTexts.length}`);

      console.log(`Novel summarization completed successfully`);
      console.log(`Total summarized: ${summarizedTexts.length} chapters`);
      console.log(`Final memory length: ${memory?.length || 0} characters`);
      
      return {
        summaries: summarizedTexts,
        memory: memory
      };

    } catch (error) {
      console.error('Novel summarization failed:', error);
      throw error;
    } finally {
      this.isSummarizing = false;
    }
  }

  /**
   * Get provider name for NovelSummarizer
   * @param {Object} activeService - Active AI service configuration
   * @returns {string} - Provider name
   */
  getProviderName(activeService) {
    const serviceName = activeService.name.toLowerCase();
    
    console.log(`Getting provider name for service: "${serviceName}"`);
    
    switch (serviceName) {
      case 'openai':
        return 'openai';
      case 'deepseek':
        return 'deepseek';
      case 'nebula':
        return 'nebula';
      case 'anthropic claude':
      case 'claude':
        return 'openrouter'; // Claude via OpenRouter
      case 'google gemini':
      case 'gemini':
        console.log('Matched Gemini service');
        return 'gemini';
      case 'openrouter':
        return 'openrouter';
      default:
        console.warn(`Unknown service name: "${serviceName}", using openai fallback`);
        return 'openai'; // Default fallback
    }
  }


  /**
   * Check if novel summarization is currently in progress
   * @returns {boolean}
   */
  isNovelSummarizing() {
    return this.isSummarizing;
  }

  /**
   * Get current summarization progress
   * @returns {Object} - Progress information
   */
  getSummarizationProgress() {
    return {
      currentBatch: this.currentBatch,
      totalBatches: this.totalBatches,
      isSummarizing: this.isSummarizing
    };
  }
}

// Create singleton instance
export const novelSummaryService = new NovelSummaryService();

// Export default
export default novelSummaryService;
