const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { execWithLog, sanitizeDangerousCharacters } = require('./utils');

/**
 * Get video dimensions using ffprobe
 * @param {string} videoPath - Path to video file
 * @returns {Promise<{width: number, height: number}>}
 */
async function getVideoDimensions(videoPath) {
  return new Promise((resolve, reject) => {
    const ffprobe = spawn('ffprobe', [
      '-v', 'quiet',
      '-print_format', 'json',
      '-show_streams',
      '-select_streams', 'v:0',
      videoPath
    ]);

    let output = '';
    ffprobe.stdout.on('data', (data) => {
      output += data.toString();
    });

    ffprobe.on('close', (code) => {
      if (code === 0) {
        try {
          const info = JSON.parse(output);
          const videoStream = info.streams[0];
          resolve({
            width: videoStream.width || 1920,
            height: videoStream.height || 1080
          });
        } catch (err) {
          console.warn('Failed to parse video info, using default dimensions');
          resolve({ width: 1920, height: 1080 });
        }
      } else {
        console.warn('ffprobe failed, using default dimensions');
        resolve({ width: 1920, height: 1080 });
      }
    });

    ffprobe.on('error', (err) => {
      console.warn('ffprobe error, using default dimensions:', err);
      resolve({ width: 1920, height: 1080 });
    });
  });
}

/**
 * Process video with VideoCR CLI to extract text and generate SRT file
 * @param {Object} event - IPC event object
 * @param {Object} options - VideoCR options
 * @param {string} options.videoPath - Path to the video file
 * @param {string} options.videocrPath - Path to videocr-cli-sa.exe
 * @param {string} options.lang - Language for OCR (default: 'ch')
 * @param {string} options.crop - Crop coordinates in format "x1,y1,x2,y2" (optional)
 * @param {number} options.simThreshold - Similarity threshold (default: 80)
 * @param {number} options.confThreshold - Confidence threshold (default: 75)
 * @param {number} options.brightnessThreshold - Brightness threshold (default: 210)
 * @param {number} options.similarImageThreshold - Similar image threshold (default: 1000)
 * @param {number} options.framesToSkip - Frames to skip (default: 1)
 * @param {boolean} options.useGpu - Enable GPU acceleration (default: false)
 * @param {number} options.maxMergeGap - Max merge gap in seconds (default: 0.09)
 * @returns {Promise<Object>} - Result object
 */
async function processVideocrCli(event, {
  videoPath,
  videocrPath,
  lang = 'ch',
  crop = null,
  simThreshold = 80,
  confThreshold = 75,
  brightnessThreshold = 210,
  similarImageThreshold = 1000,
  framesToSkip = 1,
  useGpu = false,
  maxMergeGap = 0.09
}) {
  if (!videoPath || !fs.existsSync(videoPath)) {
    return { 
      success: false, 
      error: "Input video file does not exist" 
    };
  }

  if (!videocrPath || !fs.existsSync(videocrPath)) {
    return { 
      success: false, 
      error: "VideoCR CLI executable not found. Please set the correct path." 
    };
  }

  try {
    videoPath = sanitizeDangerousCharacters(videoPath)
    // Determine output path
    const { dir, name } = path.parse(videoPath);
    const outputPath = path.join(dir, `${name}-ocr.srt`);

    // Prepare command arguments for videocr-cli-sa.exe
    const args = [
      '--video_path', videoPath,
      '--output', outputPath,
      '--lang', lang,
      '--sim_threshold', simThreshold.toString(),
      '--conf_threshold', confThreshold.toString(),
      '--use_fullframe', crop ? 'false' : 'true',
      '--similar_image_threshold', similarImageThreshold.toString(),
      '--frames_to_skip', framesToSkip.toString(),
      '--max_merge_gap', maxMergeGap.toString(),
      '--use_gpu', useGpu ? 'true' : 'false'
    ];
      
    if(brightnessThreshold) args.push('--brightness_threshold', brightnessThreshold.toString())
    // Add crop parameters if provided
    if (crop) {
      const coords = crop.split(',').map(parseFloat);
      if (coords.length === 4) {
        // Get video dimensions to convert normalized coordinates to pixels
        const dimensions = await getVideoDimensions(videoPath);

        // Check if coordinates are normalized (0-1) or pixel values
        const isNormalized = coords.every(coord => coord >= 0 && coord <= 1);

        let cropX, cropY, cropWidth, cropHeight;

        if (isNormalized) {
          // Convert normalized coordinates (0-1) to pixel coordinates
          // coords = [x, y, width, height] in normalized format
          cropX = Math.round(coords[0] * dimensions.width);
          cropY = Math.round(coords[1] * dimensions.height);
          cropWidth = Math.round(coords[2] * dimensions.width);
          cropHeight = Math.round(coords[3] * dimensions.height);
        } else {
          // Already in pixel format
          cropX = Math.round(coords[0]);
          cropY = Math.round(coords[1]);
          cropWidth = Math.round(coords[2]);
          cropHeight = Math.round(coords[3]);
        }

        // Ensure crop values are within bounds
        const finalCropX = Math.max(0, Math.min(cropX, dimensions.width - 1));
        const finalCropY = Math.max(0, Math.min(cropY, dimensions.height - 1));
        const finalCropWidth = Math.max(1, Math.min(cropWidth, dimensions.width - finalCropX));
        const finalCropHeight = Math.max(1, Math.min(cropHeight, dimensions.height - finalCropY));

        args.push(
          '--crop_x', finalCropX.toString(),
          '--crop_y', finalCropY.toString(),
          '--crop_width', finalCropWidth.toString(),
          '--crop_height', finalCropHeight.toString()
        );

        console.log('Video dimensions:', dimensions);
        console.log('Input crop coords:', coords, isNormalized ? '(normalized)' : '(pixels)');
        console.log('Converted crop coords:', { cropX, cropY, cropWidth, cropHeight });
        console.log('Final crop parameters:', {
          cropX: finalCropX,
          cropY: finalCropY,
          cropWidth: finalCropWidth,
          cropHeight: finalCropHeight
        });
      }
    }

    console.log('VideoCR CLI processing started:', {
      videocrPath,
      videoPath,
      outputPath,
      args
    });

    // Execute videocr-cli-sa.exe directly
    console.log('Executing command:', videocrPath);
    console.log('With args:', args);

    const result = await execWithLog(event, videocrPath, args, null);

    console.log('VideoCR CLI execution result:', result);

    // Wait a bit and check if process is still running
    setTimeout(async () => {
      try {
        const activeProcesses = await require('./utils').getActiveProcesses();
        const isStillRunning = activeProcesses.some(p => p.processId === result.processId);
        console.log(`Process ${result.processId} still running: ${isStillRunning}`);
      } catch (err) {
        console.log('Could not check process status:', err.message);
      }
    }, 5000);

    // Check if output file was created and has content
    setTimeout(() => {
      if (fs.existsSync(outputPath)) {
        const stats = fs.statSync(outputPath);
        console.log(`Output file created: ${outputPath}, size: ${stats.size} bytes`);
        if (stats.size === 0) {
          console.warn('Warning: Output SRT file is empty (0 bytes)');
        }
      } else {
        console.error('Error: Output SRT file was not created');
      }
    }, 2000); // Check after 2 seconds

    return {
      success: true,
      processId: result.processId,
      outputPath: outputPath,
      status: 'started'
    };
  } catch (err) {
    console.error("Error processing video with VideoCR CLI:", err);
    return {
      success: false,
      error: err.message || "Unknown error occurred"
    };
  }
}



/**
 * Test VideoCR CLI with minimal parameters
 */
async function testVideocrCli(event, { videoPath, videocrPath }) {
  try {
    const { dir, name } = path.parse(videoPath);
    const outputPath = path.join(dir, `${name}-test.srt`);

    // Minimal command to test
    const args = [
      '--video_path', videoPath,
      '--output', outputPath,
      '--lang', 'ch'
    ];

    console.log('Testing VideoCR CLI with minimal args:', { videocrPath, args });

    const result = await execWithLog(event, videocrPath, args, null);

    return {
      success: true,
      processId: result.processId,
      outputPath: outputPath,
      status: 'started'
    };
  } catch (err) {
    console.error("Error testing VideoCR CLI:", err);
    return {
      success: false,
      error: err.message || "Unknown error occurred"
    };
  }
}

module.exports = {
  processVideocrCli,
  testVideocrCli
};
