import { state } from './state'

/**
 * Global Audio Manager
 * Manages synchronized playback of all subtitle audio with video
 * Enhanced with Web Audio API and speed balancing
 */
export class GlobalAudioManager {
  constructor() {
    this.audioElements = new Map() // subtitle ID -> audio element
    this.audioPool = [] // Pool of reusable audio elements
    this.activeAudioElements = new Map() // Currently active audio elements
    this.maxPoolSize = 20 // Maximum number of audio elements to keep in memory
    this.isPlaying = false
    this.videoElement = null
    this.originalVideoVolume = 1
    this.subtitleItems = []
    this.audioElementsEnabled = false // Track if audio elements have been enabled
    this.scheduledTimeouts = new Set() // Track scheduled timeouts for cleanup
    this.preloadedAudio = new Set() // Track which audio files have been preloaded

    // Web Audio API support
    this.audioContext = null
    this.audioMap = new Map() // subtitle ID -> [audioBuffer, sourceNode]
    this.isWebAudioSupported = false

    // Speed balancing
    this.speedBalancingEnabled = false
    this.audioCombineList = []
    this.lastPlaybackRate = 1
    this.defaultVideoSpeed = 1
    this.currentIndex = 0
    this.joinTime = 0.5 // Default join time in seconds

    this.initializeWebAudio()
  }

  /**
   * Initialize Web Audio API
   */
  initializeWebAudio() {
    try {
      if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        this.audioContext = new (AudioContext || webkitAudioContext)()
        this.isWebAudioSupported = true
        // console.log('Web Audio API initialized successfully')
      } else {
        console.warn('Web Audio API not supported')
        this.isWebAudioSupported = false
      }
    } catch (error) {
      console.error('Failed to initialize Web Audio API:', error)
      this.isWebAudioSupported = false
    }
  }

  /**
   * Load and decode audio from URL using Web Audio API
   */
  async loadAudio(url) {
    if (!this.isWebAudioSupported || !this.audioContext) {
      throw new Error('Web Audio API not available')
    }

    try {
      const response = await fetch(url + "?t=" + new Date().getTime())
      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
      return audioBuffer
    } catch (error) {
      console.error('Error loading audio:', error)
      throw error
    }
  }

  /**
   * Store audio buffer for a subtitle ID
   */
  async storeAudio(id, url) {
    if (!this.isWebAudioSupported) {
      console.warn('Web Audio API not supported, falling back to HTML5 audio')
      return
    }

    try {
      const audioBuffer = await this.loadAudio(url)
      this.audioMap.set(id, [audioBuffer, null])
      // console.log(`Audio stored for subtitle ${id}`)
    } catch (error) {
      console.error(`Error storing audio for subtitle ${id}:`, error)
    }
  }

  /**
   * Play sound by ID using Web Audio API
   */
  playSoundById(id, time = 0, rate = 1) {
    if (!this.isWebAudioSupported || !this.audioContext) {
      console.warn('Web Audio API not available')
      return
    }

    const audioData = this.audioMap.get(id)
    if (!audioData || !audioData[0]) {
      console.error(`No audio found for subtitle ${id}`)
      return
    }

    try {
      // Stop any existing source for this ID
      this.stopSoundById(id)

      const sourceNode = this.audioContext.createBufferSource()
      sourceNode.buffer = audioData[0]
      sourceNode.playbackRate.value = parseFloat(rate)
      sourceNode.connect(this.audioContext.destination)

      if (time > 0) {
        sourceNode.start(0, time)
      } else {
        sourceNode.start()
      }

      // Store the source node for later control
      this.audioMap.set(id, [audioData[0], sourceNode])
    } catch (error) {
      console.error(`Error playing audio for subtitle ${id}:`, error)
    }
  }

  /**
   * Stop sound by ID
   */
  stopSoundById(id) {
    const audioData = this.audioMap.get(id)
    if (audioData && audioData[1]) {
      try {
        audioData[1].stop()
        this.audioMap.set(id, [audioData[0], null])
      } catch (error) {
        // Source might already be stopped
      }
    }
  }

  /**
   * Check if sound is playing
   */
  isSoundPlaying(id) {
    const audioData = this.audioMap.get(id)
    if (!audioData || !audioData[1]) {
      return false
    }

    const sourceNode = audioData[1]
    // Check if the source node is still active
    return sourceNode && (sourceNode.playbackState === "PLAYING" || sourceNode.playbackState === "SCHEDULED")
  }

  /**
   * Initialize the manager with subtitle items
   */
  async initialize(subtitleItems, videoElement) {
    this.subtitleItems = subtitleItems
    this.videoElement = videoElement
    this.originalVideoVolume = videoElement?.volume || 1

    // Initialize audio pool instead of creating all elements
    this.initializeAudioPool()

    // Auto-enable audio context on initialization
    await this.enableAudioContext()

    // Preload audio for Web Audio API if supported
    if (this.isWebAudioSupported) {
      await this.preloadSubtitleAudio()
    }

    // Initialize audio combine list for speed balancing
    this.buildAudioCombineList()
  }

  /**
   * Preload audio for all subtitle items using Web Audio API
   */
  async preloadSubtitleAudio() {
    if (!this.isWebAudioSupported) return

    const preloadPromises = this.subtitleItems
      .filter(item => this.getAudioUrl(item))
      .map(async (item) => {
        try {
          await this.storeAudio(item.id, this.getAudioUrl(item))
        } catch (error) {
          console.warn(`Failed to preload audio for subtitle ${item.id}:`, error)
        }
      })

    await Promise.allSettled(preloadPromises)
    // console.log('Audio preloading completed')
  }

  /**
   * Build audio combine list for speed balancing
   */
  buildAudioCombineList() {
    this.audioCombineList = []

    if (!this.subtitleItems || this.subtitleItems.length === 0) {
      return
    }

    // Filter enabled subtitles with audio and sort by start time
    const enabledSubtitles = this.subtitleItems
      .filter(item => {
        // Check if subtitle is enabled
        if (item.isEnabled === false) return false

        // Check if has audio URL
        const audioUrl = this.getAudioUrl(item)
        if (!audioUrl) return false

        // Check if has generated audio based on voice type
        if (item.isVoice === 1 && !item.isGenerated1) return false
        if (item.isVoice === 2 && !item.isGenerated2) return false
        if (item.isVoice === 3 && !item.isGenerated3) return false

        return true
      })
      .sort((a, b) => a.startTime - b.startTime)

    const videoDuration = this.videoElement?.duration || 0

    for (let i = 0; i < enabledSubtitles.length; i++) {
      const item = enabledSubtitles[i]
      const startTime = item.startTime
      const endTime = item.endTime
      const segmentDuration = endTime - startTime

      // Get audio duration from subtitle item or Web Audio API
      let audioDuration = segmentDuration

      // Try to get duration from subtitle item based on voice type
      if (item.isVoice === 1 && item.audioDuration1) {
        audioDuration = item.audioDuration1 / 1000 // Convert from ms to seconds
      } else if (item.isVoice === 2 && item.audioDuration2) {
        audioDuration = item.audioDuration2 / 1000
      } else if (item.isVoice === 3 && item.audioDuration3) {
        audioDuration = item.audioDuration3 / 1000
      } else if (item.duration) {
        audioDuration = item.duration
      }

      // Override with Web Audio API duration if available
      const audioData = this.audioMap.get(item.id)
      if (audioData && audioData[0]) {
        audioDuration = audioData[0].duration
      }

      let playbackRate = this.defaultVideoSpeed
      let segmentEndTime = endTime

      if (this.speedBalancingEnabled) {
        // Calculate next segment start time
        const nextItem = enabledSubtitles[i + 1]
        const nextStartTime = nextItem ? nextItem.startTime : Math.min(startTime + audioDuration, videoDuration)

        // Check if we need speed adjustment
        const availableTime = nextStartTime - startTime
        if (audioDuration > availableTime - this.joinTime) {
          // Need to slow down video to fit audio
          playbackRate = Math.max((availableTime / this.defaultVideoSpeed) / audioDuration * this.defaultVideoSpeed, 0.1)
          segmentEndTime = nextStartTime
        } else {
          // Normal speed
          playbackRate = this.defaultVideoSpeed
          segmentEndTime = Math.min(nextStartTime, startTime + audioDuration)
        }
      }

      this.audioCombineList.push({
        id: item.id,
        startTime: startTime,
        endTime: segmentEndTime,
        playbackRate: playbackRate,
        isPlaying: false,
        audioRate: item.speechRate || 1
      })
    }

    // console.log('Audio combine list built:', this.audioCombineList.length, 'segments')
  }

  /**
   * Initialize audio pool with limited number of audio elements
   */
  initializeAudioPool() {
    // Clear existing pool
    this.audioPool.forEach(audio => {
      audio.src = ''
      audio.removeEventListener('loadedmetadata', audio._loadedHandler)
      audio.removeEventListener('error', audio._errorHandler)
    })
    this.audioPool = []
    this.activeAudioElements.clear()
    this.audioElements.clear()

    // Create initial pool of audio elements
    for (let i = 0; i < this.maxPoolSize; i++) {
      const audio = this.createPooledAudioElement()
      this.audioPool.push(audio)
    }

    // console.log(`Audio pool initialized with ${this.maxPoolSize} elements`)

    // Update global state
    state.globalAudioElements = this.activeAudioElements
  }

  /**
   * Create a pooled audio element with reusable event handlers
   */
  createPooledAudioElement() {
    const audio = new Audio()
    audio.preload = 'metadata'
    audio.volume = 0.8 // Subtitle audio volume

    // Create reusable event handlers
    audio._loadedHandler = () => {
      // console.log(`Pooled audio loaded, duration:`, audio.duration)
    }

    audio._errorHandler = (error) => {
      console.error(`Error loading pooled audio:`, error)
    }

    audio.addEventListener('loadedmetadata', audio._loadedHandler)
    audio.addEventListener('error', audio._errorHandler)

    return audio
  }

  /**
   * Get or create audio element for a subtitle item
   */
  getAudioElementForSubtitle(item) {
    const audioUrl = this.getAudioUrl(item)
    if (!audioUrl) return null

    // Check if already active
    if (this.activeAudioElements.has(item.id)) {
      return this.activeAudioElements.get(item.id)
    }

    // Get audio element from pool
    let audio = this.audioPool.pop()

    // If pool is empty, reuse least recently used active element
    if (!audio && this.activeAudioElements.size >= this.maxPoolSize) {
      const oldestId = this.activeAudioElements.keys().next().value
      audio = this.activeAudioElements.get(oldestId)
      this.activeAudioElements.delete(oldestId)
      this.audioElements.delete(oldestId)
    }

    // If still no audio, create new one (shouldn't happen often)
    if (!audio) {
      audio = this.createPooledAudioElement()
    }

    // Configure audio for this subtitle
    audio.src = audioUrl
    audio.currentTime = 0

    // Add to active elements
    this.activeAudioElements.set(item.id, audio)
    this.audioElements.set(item.id, audio)

    return audio
  }

  /**
   * Return audio element to pool
   */
  returnAudioToPool(subtitleId) {
    const audio = this.activeAudioElements.get(subtitleId)
    if (!audio) return

    // Stop and reset audio
    audio.pause()
    audio.currentTime = 0
    audio.src = ''

    // Remove from active elements
    this.activeAudioElements.delete(subtitleId)
    this.audioElements.delete(subtitleId)

    // Return to pool if there's space
    if (this.audioPool.length < this.maxPoolSize) {
      this.audioPool.push(audio)
    }
  }

  /**
   * Get audio URL for a subtitle item
   */
  getAudioUrl(item) {
    // Check based on isVoice property
    if (item.isVoice === 1 && item.audioUrl1) {
      return item.audioUrl1
    } else if (item.isVoice === 2 && item.audioUrl2) {
      return item.audioUrl2
    } else if (item.isVoice === 3 && item.audioUrl3) {
      return item.audioUrl3
    }

    // Fallback to any available audio URL
    return item.audioUrl || item.audioUrl1 || item.audioUrl2 || item.audioUrl3 || null
  }

  /**
   * Enable audio context with user interaction
   */
  async enableAudioContext() {
    try {
      // Create and play a silent audio to enable audio context
      const silentAudio = new Audio('data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQAAAAA=')
      silentAudio.volume = 0
      await silentAudio.play()
      silentAudio.pause()
      return true
    } catch (error) {
      console.warn('Failed to enable audio context:', error)
      return false
    }
  }



  /**
   * Enable audio playback by testing pool elements
   */
  async enableAllAudioElements() {
    if (this.audioPool.length === 0) {
      console.warn('No audio elements in pool to enable')
      return false
    }

    try {
      // Test one audio element from the pool
      const testAudio = this.audioPool[0]
      const originalVolume = testAudio.volume

      // Set a test audio source
      testAudio.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQAAAAA='
      testAudio.volume = 0.01
      testAudio.currentTime = 0

      await testAudio.play()

      // Stop immediately and restore
      setTimeout(() => {
        testAudio.pause()
        testAudio.currentTime = 0
        testAudio.volume = originalVolume
        testAudio.src = ''
      }, 50)

      this.audioElementsEnabled = true
      state.audioEnabled = true
      console.log('Audio pool enabled successfully')
      return true

    } catch (error) {
      console.warn('Failed to enable audio pool:', error)
      return false
    }
  }

  /**
   * Auto scroll sound - speed balancing logic during playback
   */
  autoScrollSound(index = 0) {
    if (!this.videoElement || !this.isPlaying) return 0

    const currentTime = this.videoElement.currentTime
    let blockReturn = 0
    let indexReturn = 0

    // Search in a range around the current index for performance
    const searchStart = Math.max(0, index - 20)
    const searchEnd = Math.min(this.audioCombineList.length, index + 20)

    if (index !== 0) {
      for (let i = searchStart; i < searchEnd; i++) {
        const segment = this.audioCombineList[i]

        if (this.videoElement.paused) {
          // Stop all audio when video is paused
          try {
            segment.isPlaying = false
            this.stopSoundById(segment.id)
          } catch (error) {
            console.log(error)
          }
        } else {
          // Check if current time is within this segment
          if (currentTime >= segment.startTime && currentTime <= segment.endTime) {
            // Apply speed balancing if enabled
            if (this.speedBalancingEnabled) {
              const currentPlaybackRate = segment.playbackRate
              if (currentPlaybackRate !== this.lastPlaybackRate) {
                this.videoElement.playbackRate = currentPlaybackRate
                this.lastPlaybackRate = currentPlaybackRate
              }
            } else {
              // Use default speed
              const currentPlaybackRate = this.defaultVideoSpeed
              if (currentPlaybackRate !== this.lastPlaybackRate) {
                this.videoElement.playbackRate = currentPlaybackRate
                this.lastPlaybackRate = currentPlaybackRate
              }
            }

            // Start audio if not already playing
            if (!segment.isPlaying) {
              const audioCurrentTime = currentTime - segment.startTime
              this.playSoundById(segment.id, audioCurrentTime, segment.audioRate)
              segment.isPlaying = true
            }

            blockReturn = 1
            indexReturn = i
          } else {
            // Stop audio for segments not in current time
            try {
              segment.isPlaying = false
              this.stopSoundById(segment.id)
            } catch (error) {
              console.log(error)
            }
          }
        }
      }
    }

    // If no segment found in range, search all segments
    if (blockReturn !== 1) {
      for (let i = 0; i < this.audioCombineList.length; i++) {
        const segment = this.audioCombineList[i]

        if (this.videoElement.paused) {
          try {
            segment.isPlaying = false
            this.stopSoundById(segment.id)
          } catch (error) {
            console.log(error)
          }
        } else {
          if (currentTime >= segment.startTime && currentTime <= segment.endTime) {
            // Apply speed balancing
            if (this.speedBalancingEnabled) {
              const currentPlaybackRate = segment.playbackRate
              if (currentPlaybackRate !== this.lastPlaybackRate) {
                this.videoElement.playbackRate = currentPlaybackRate
                this.lastPlaybackRate = currentPlaybackRate
              }
            } else {
              const currentPlaybackRate = this.defaultVideoSpeed
              if (currentPlaybackRate !== this.lastPlaybackRate) {
                this.videoElement.playbackRate = currentPlaybackRate
                this.lastPlaybackRate = currentPlaybackRate
              }
            }

            try {
              if (!segment.isPlaying) {
                const audioCurrentTime = currentTime - segment.startTime
                this.playSoundById(segment.id, audioCurrentTime, segment.audioRate)
                segment.isPlaying = true
              }
            } catch (error) {
              console.log(error)
            }

            indexReturn = i
          } else {
            try {
              segment.isPlaying = false
              this.stopSoundById(segment.id)
            } catch (error) {
              console.log(error)
            }
          }
        }
      }
    }

    return indexReturn
  }

  /**
   * Enable/disable speed balancing
   */
  setSpeedBalancing(enabled) {
    this.speedBalancingEnabled = enabled
    this.buildAudioCombineList() // Rebuild list with new settings
    // console.log('Speed balancing:', enabled ? 'enabled' : 'disabled')
  }

  /**
   * Set join time for speed balancing calculations
   */
  setJoinTime(time) {
    this.joinTime = time
    this.buildAudioCombineList() // Rebuild list with new settings
  }

  /**
   * Start global synchronized playback
   */
  async startGlobalPlayback(startTime = 0) {
    try {
      if (!this.videoElement) {
        console.warn('No video element available for global playback')
        return false
      }

      // Clear any existing timeouts first to prevent duplicates
      this.scheduledTimeouts.forEach(timeoutId => {
        clearTimeout(timeoutId)
      })
      this.scheduledTimeouts.clear()

      // Stop any currently playing audio
      this.audioElements.forEach((audio) => {
        audio.pause()
        audio.currentTime = 0
      })

      // Stop Web Audio API sounds
      this.audioCombineList.forEach(segment => {
        segment.isPlaying = false
        this.stopSoundById(segment.id)
      })

      // Audio elements should already be enabled during initialization
      if (!this.audioElementsEnabled) {
        await this.enableAllAudioElements()
      }

      // Set global playback state
      this.isPlaying = true
      state.isGlobalPlayback = true
      state.globalPlaybackStartTime = startTime

      // Adjust video volume for balance
      this.videoElement.volume = this.originalVideoVolume * 0.4 // Reduce to 40%

      // Seek video to start time
      this.videoElement.currentTime = startTime

      // Start video playback
      await this.videoElement.play()

      // Use speed balancing if Web Audio API is supported and enabled
      if (this.isWebAudioSupported && this.speedBalancingEnabled) {
        // Start speed balancing loop
        this.startSpeedBalancingLoop()
      } else {
        // Fallback to original scheduling method
        this.scheduleAudioPlayback(startTime)
      }

      return true

    } catch (error) {
      console.error('Error starting global playback:', error)
      this.stopGlobalPlayback()
      return false
    }
  }

  /**
   * Start speed balancing loop for real-time synchronization
   */
  startSpeedBalancingLoop() {
    if (!this.isPlaying) return

    // Find current segment index
    this.currentIndex = this.autoScrollSound(this.currentIndex)

    // Continue loop
    if (this.isPlaying) {
      const loopTimeout = setTimeout(() => {
        this.startSpeedBalancingLoop()
      }, 100) // Check every 100ms

      this.scheduledTimeouts.add(loopTimeout)
    }
  }

  /**
   * Schedule audio playback for all subtitles based on their timing
   */
  scheduleAudioPlayback(startTime) {
    let immediatePlayCount = 0
    let scheduledPlayCount = 0

    // Get subtitles that need to play within a reasonable window
    const playWindow = 30 // seconds - only load audio for subtitles within 30 seconds
    const relevantSubtitles = this.subtitleItems.filter(item => {
      const audioUrl = this.getAudioUrl(item)
      return audioUrl && item.startTime <= startTime + playWindow
    })

    relevantSubtitles.forEach(item => {
      const itemStartTime = item.startTime
      const itemEndTime = item.endTime

      // Check if this subtitle should be playing at the start time
      if (startTime >= itemStartTime && startTime < itemEndTime) {
        // Get audio element for immediate playback
        const audio = this.getAudioElementForSubtitle(item)
        if (!audio) return

        // Start playing immediately with offset
        const offset = startTime - itemStartTime
        audio.currentTime = offset

        const playPromise = audio.play()
        if (playPromise !== undefined) {
          playPromise.then(() => {
            // console.log(`Audio started for subtitle ${item.id}`)
          }).catch(error => {
            console.error(`Error playing audio for subtitle ${item.id}:`, error.message)
          })
        }
        immediatePlayCount++

        // Schedule to return audio to pool when subtitle ends
        const endDelay = (itemEndTime - startTime) * 1000
        const endTimeoutId = setTimeout(() => {
          this.returnAudioToPool(item.id)
        }, endDelay)
        this.scheduledTimeouts.add(endTimeoutId)

      } else if (itemStartTime > startTime) {
        // Schedule to play later
        const delay = (itemStartTime - startTime) * 1000

        const timeoutId = setTimeout(() => {
          if (this.isPlaying && state.isGlobalPlayback) {
            const audio = this.getAudioElementForSubtitle(item)
            if (!audio) return

            audio.currentTime = 0

            const playPromise = audio.play()
            if (playPromise !== undefined) {
              playPromise.then(() => {
                // console.log(`Scheduled audio started for subtitle ${item.id}`)
              }).catch(error => {
                console.error(`Error playing scheduled audio for subtitle ${item.id}:`, error.message)
              })
            }

            // Schedule to return audio to pool when subtitle ends
            const duration = itemEndTime - itemStartTime
            const endTimeoutId = setTimeout(() => {
              this.returnAudioToPool(item.id)
            }, duration * 1000)
            this.scheduledTimeouts.add(endTimeoutId)
          }
        }, delay)

        this.scheduledTimeouts.add(timeoutId)
        scheduledPlayCount++
      }
    })

    console.log(`Scheduled ${immediatePlayCount} immediate and ${scheduledPlayCount} future audio playbacks`)
  }

  /**
   * Stop global synchronized playback
   */
  stopGlobalPlayback() {
    try {
      // console.log('🛑 Stopping global synchronized playback')

      // Clear all scheduled timeouts
      this.scheduledTimeouts.forEach(timeoutId => {
        clearTimeout(timeoutId)
      })
      this.scheduledTimeouts.clear()
      // console.log('🧹 Cleared all scheduled timeouts')

      // Stop video
      if (this.videoElement) {
        this.videoElement.pause()
        // Restore original video volume and playback rate
        this.videoElement.volume = this.originalVideoVolume
        this.videoElement.playbackRate = 1
        this.lastPlaybackRate = 1
        // console.log('📺 Video stopped and volume restored')
      }

      // Stop all active audio elements and return them to pool
      this.activeAudioElements.forEach((audio, subtitleId) => {
        audio.pause()
        audio.currentTime = 0
        this.returnAudioToPool(subtitleId)
        // console.log(`🔇 Stopped audio for subtitle ${subtitleId}`)
      })

      // Stop all Web Audio API sounds
      this.audioCombineList.forEach(segment => {
        segment.isPlaying = false
        this.stopSoundById(segment.id)
      })

      // Clear global state
      this.isPlaying = false
      state.isGlobalPlayback = false
      state.globalPlaybackStartTime = 0
      this.currentIndex = 0

      // console.log('✅ Global synchronized playback stopped successfully')

    } catch (error) {
      console.error('❌ Error stopping global playback:', error)
    }
  }

  /**
   * Pause global playback
   */
  pauseGlobalPlayback() {
    if (!this.isPlaying) return

    // console.log('⏸️ Pausing global synchronized playback')

    // Clear all scheduled timeouts to prevent duplicate audio
    // console.log('🧹 Clearing', this.scheduledTimeouts.size, 'scheduled timeouts')
    this.scheduledTimeouts.forEach(timeoutId => {
      clearTimeout(timeoutId)
    })
    this.scheduledTimeouts.clear()

    // Pause video
    if (this.videoElement) {
      this.videoElement.pause()
      // console.log('📺 Video paused')
    }

    // Pause all active audio elements
    this.activeAudioElements.forEach((audio) => {
      audio.pause()
    })

    // Stop all Web Audio API sounds
    this.audioCombineList.forEach(segment => {
      segment.isPlaying = false
      this.stopSoundById(segment.id)
    })

    this.isPlaying = false
    // console.log('✅ Global playback paused successfully')
  }

  /**
   * Resume global playback
   */
  async resumeGlobalPlayback() {
    if (this.isPlaying) return

    try {
      // console.log('▶️ Resuming global synchronized playback')

      // Clear any existing timeouts first
      this.scheduledTimeouts.forEach(timeoutId => {
        clearTimeout(timeoutId)
      })
      this.scheduledTimeouts.clear()

      // Resume video
      if (this.videoElement) {
        await this.videoElement.play()
        // console.log('📺 Video resumed')
      }

      // Get current time and reschedule audio
      const currentTime = this.videoElement?.currentTime || 0
      // console.log('🕐 Resuming from time:', currentTime)

      this.isPlaying = true
      state.isGlobalPlayback = true

      // Use speed balancing if Web Audio API is supported and enabled
      if (this.isWebAudioSupported && this.speedBalancingEnabled) {
        // Reset audio combine list playing states
        this.audioCombineList.forEach(segment => {
          segment.isPlaying = false
        })
        // Start speed balancing loop
        this.startSpeedBalancingLoop()
      } else {
        // Fallback to original scheduling method
        this.scheduleAudioPlayback(currentTime)
      }

      // console.log('✅ Global playback resumed successfully')

    } catch (error) {
      console.error('❌ Error resuming global playback:', error)
    }
  }

  /**
   * Seek to specific time
   */
  seekTo(time) {
    if (!this.videoElement) return

    // console.log('Seeking global playback to time:', time)

    // Seek video
    this.videoElement.currentTime = time

    // Return all active audio to pool and clear timeouts
    this.scheduledTimeouts.forEach(timeoutId => {
      clearTimeout(timeoutId)
    })
    this.scheduledTimeouts.clear()

    this.activeAudioElements.forEach((_, subtitleId) => {
      this.returnAudioToPool(subtitleId)
    })

    // Stop all Web Audio API sounds and reset playing states
    this.audioCombineList.forEach(segment => {
      segment.isPlaying = false
      this.stopSoundById(segment.id)
    })

    // Reset current index for speed balancing
    this.currentIndex = 0

    // If playing, reschedule audio from new time
    if (this.isPlaying) {
      if (this.isWebAudioSupported && this.speedBalancingEnabled) {
        this.startSpeedBalancingLoop()
      } else {
        this.scheduleAudioPlayback(time)
      }
    }
  }

  /**
   * Update subtitle items (when they change)
   */
  updateSubtitleItems(subtitleItems) {
    this.subtitleItems = subtitleItems

    // Rebuild audio combine list
    this.buildAudioCombineList()

    // Preload new audio if Web Audio API is supported
    if (this.isWebAudioSupported) {
      this.preloadSubtitleAudio()
    }
  }

  /**
   * Clean up all audio and timeouts
   */
  cleanupAll() {
    // Clear all timeouts
    this.scheduledTimeouts.forEach(timeoutId => {
      clearTimeout(timeoutId)
    })
    this.scheduledTimeouts.clear()

    // Return all active audio to pool
    this.activeAudioElements.forEach((_, subtitleId) => {
      this.returnAudioToPool(subtitleId)
    })

    // Reset state
    this.isPlaying = false
    state.isGlobalPlayback = false
  }

  /**
   * Cleanup
   */
  destroy() {
    this.stopGlobalPlayback()
    this.cleanupAll()

    // Clean up audio pool
    this.audioPool.forEach(audio => {
      audio.src = ''
      audio.removeEventListener('loadedmetadata', audio._loadedHandler)
      audio.removeEventListener('error', audio._errorHandler)
    })
    this.audioPool = []

    // Clean up Web Audio API
    this.audioMap.forEach((audioData) => {
      if (audioData[1]) {
        try {
          audioData[1].stop()
        } catch (error) {
          // Source might already be stopped
        }
      }
    })
    this.audioMap.clear()
    this.audioCombineList = []

    // Close audio context
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
    }

    this.audioElements.clear()
    this.activeAudioElements.clear()
    state.globalAudioElements.clear()
    this.audioElementsEnabled = false
    state.audioEnabled = false
  }
}

// Create global instance
export const globalAudioManager = new GlobalAudioManager()
