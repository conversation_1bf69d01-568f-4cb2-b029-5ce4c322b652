import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import vClickOutside from "click-outside-vue3"
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './main.css'
import router from './router'
import App from './App.vue'
import { createI18nProvider } from "@/i18n/i18n";

const app = createApp(App)
const i18nProvider = createI18nProvider(app);
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)
app.use(Antd)
app.use(router)
app.use(i18nProvider)
app.use(vClickOutside)


app.config.globalProperties.F = F;
app.config.globalProperties.S = S;

S.$router = router


app.mount('#app')

window.F = F
window.S = S