
const fs = require('fs');
const path = require('path');
const { useWebSocketSynthesis, getTokenVbee, requestSynthesis } = require('../useWebSocketSynthesis');

F.initSocket = async function () {
   const aivoice_refresh_token = await S.db.Config.getValueByName('vbee_refresh_token');
  if (!aivoice_refresh_token) {
    console.log('Không tìm thấy refresh token trong database:', aivoice_refresh_token);
    return;
  }

  const res = await getTokenVbee(aivoice_refresh_token);
  // console.log('vbee token initSocket', res);

  const { isConnected, startWs } = useWebSocketSynthesis(res.result?.accessToken);
  console.log('isConnected = ', isConnected);
  // save refresh token to file
  S.vbee = res.result;
  const refreshToken = res.result?.refreshToken || res.result?.refresh_token;
  if (refreshToken) {
    await S.db.Config.updateValueByName('vbee_refresh_token', refreshToken);
    await S.db.Config.updateValueByName('vbee_access_token', res.result?.accessToken);
  }
  return await startWs();
};

F.requestSynthesis = requestSynthesis;
