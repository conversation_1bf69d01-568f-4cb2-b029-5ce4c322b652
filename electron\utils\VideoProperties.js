const { isNumber } = require('lodash')


class VideoProperties {
    constructor(duration, timeBase, fps, width, height) {
        if (isNumber(duration)) {
            if (timeBase === undefined || fps === undefined || width === undefined || height === undefined) {
                throw new Error('Cannot init class from the provided parameters')
            }
            this.duration = duration
            this.timeBase = timeBase
            this.fps = fps
            this.width = width
            this.height = height
        } else {
            const videoProperties = duration
            this.duration = videoProperties.duration
            this.timeBase = videoProperties.timeBase
            this.fps = videoProperties.fps
            this.width = videoProperties.width
            this.height = videoProperties.height
        }
    }

    get unitFrame() {
        return this.timeBase[1] *
                this.fps[1] /
                this.timeBase[0] /
                this.fps[0]
    }

    get lastFrame() {
        return Math.floor(this.duration / this.unitFrame)
    }
}

module.exports = {
    VideoProperties
}