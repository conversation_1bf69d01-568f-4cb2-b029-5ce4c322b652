<template>
  <div class="timeline-wrapper card">
    <div
      class="timeline-main grabbable"
      @wheel.alt.prevent="wheelAltEvent"
      @wheel.exact.prevent="wheelEvent"
      @pointermove="pointerLocalMoveEvent"
      @pointerdown="pointerDownEvent"
    >
      <svg id="timeline-svg" :width="timelineMainWidth + 'px'" height="20px" />
      <svg id="current-svg" :width="timelineMainWidth + 'px'" height="100%">
        <g :transform="'translate(' + xScaleFunc(currentFrame) + ', 0)'">
          <line class="current-line" x1="0" x2="0" y1="20" y2="500" />
          <text class="current-time-text">{{ frameToTime(currentFrame, fps) }}</text>
        </g>
      </svg>
      <svg id="current-pointer-svg" :width="timelineMainWidth + 'px'" height="100%">
        <g :transform="'translate(' + timelinePointerX + ', 0)'">
          <line class="current-line" x1="0" x2="0" y1="20" y2="500" />
          <text class="current-time-text">
            {{ frameToTime(xScaleInvert(Math.floor(timelinePointerX)), fps) }}
          </text>
        </g>
      </svg>
      <svg id="subtitle-svg" :width="timelineMainWidth + 'px'">
        <g
          v-for="subtitleInfo in subtitleInfos"
          :key="subtitleInfo.id"
          :transform="'translate(' + xScaleFunc(subtitleInfo.startFrame) + ', 25)'"
        >
          <rect
            :class="{
              'subtitle-rect': true,
              'subtitle-rect-active': subtitleInfo.endFrame > currentFrame && currentFrame >= subtitleInfo.startFrame,
            }"
            :width="xScaleFunc(subtitleInfo.endFrame) - xScaleFunc(subtitleInfo.startFrame)"
            height="100"
            rx="4"
          />
          <foreignObject
            class="subtitle-text-object"
            :width="xScaleFunc(subtitleInfo.endFrame) - xScaleFunc(subtitleInfo.startFrame)"
            height="100"
          >
            <div class="subtitle-text-wrapper">
              <p
                :class="{
                  'subtitle-text': true,
                  'subtitle-text-active': subtitleInfo.endFrame > currentFrame && currentFrame >= subtitleInfo.startFrame,
                }"
                @pointerdown.stop=""
              >
                <EditableDiv v-model="subtitleInfo.text" />
              </p>
            </div>
          </foreignObject>
        </g>
      </svg>
    </div>
    <TimelineBar
      :totalFrame="totalFrame"
      v-model:leftPercent="leftPercent"
      v-model:rightPercent="rightPercent"
      @move="move"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeUnmount, reactive, ref, computed, watch } from 'vue'
import * as d3 from 'd3'
import lodash from 'lodash'
import EditableDiv from '@/components/subtitle/EditableDiv.vue'
import TimelineBar from '@/components/subtitle/TimelineBar.vue'
import { frameToTime } from '@/lib/utils'
import type { SubtitleInfo } from '@/lib/SubtitleInfo'

const props = defineProps<{
  disabled?: boolean
  totalFrame: number
  fps: number
  currentFrame: number
  subtitleInfos: Array<SubtitleInfo>
}>()

const emit = defineEmits(['change', 'update:leftPercent', 'update:rightPercent'])

const timelineMainWidth = ref(0)
const timelineMainHeight = ref(0)
const timelinePointerX = ref(0)

const leftPos = ref(0)
const rightPos = ref(0)
const lastX = ref(0)
const pointerId = ref<number | undefined>()
const pointerPos = ref(-1)

const length = computed({
  get: () => rightPos.value - leftPos.value,
  set: (val) => {
    rightPos.value = leftPos.value + val
  },
})

const xScale = computed(() =>
  d3.scaleLinear().domain([leftPos.value, rightPos.value]).range([0, timelineMainWidth.value])
)

const xScaleFunc = (frame: number) => xScale.value(frame)
const xScaleInvert = (x: number) => xScale.value.invert(x)

const leftPercent = computed({
  get: () => leftPos.value / props.totalFrame,
  set: (value) => {
    leftPos.value = Math.min(1, Math.max(0, value)) * props.totalFrame
    emit('update:leftPercent', value)
  },
})

const rightPercent = computed({
  get: () => rightPos.value / props.totalFrame,
  set: (value) => {
    rightPos.value = Math.min(1, Math.max(0, value)) * props.totalFrame
    emit('update:rightPercent', value)
  },
})

const updateTimelineSize = () => {
  const el = document.querySelector('.timeline-main')
  if (el) {
    const rect = el.getBoundingClientRect()
    timelineMainWidth.value = rect.width
    timelineMainHeight.value = rect.height
  }
}

const renderTimeline = () => {
  d3.select('#timeline-svg').selectAll('g').remove()
  const axis = d3.axisBottom(xScale.value).ticks(5).tickFormat((f) => frameToTime(f as number, props.fps))
  d3.select('#timeline-svg').append('g').attr('class', 'timeline-axis').call(axis).attr('text-anchor', 'left')

  const axisLines = d3.axisBottom(xScale.value).ticks(30)
  d3.select('#timeline-svg').append('g').attr('class', 'timeline-axis-lines').call(axisLines)
}

const move = (delta: number) => {
  if (leftPos.value + delta < 0) {
    const l = length.value
    leftPos.value = 0
    rightPos.value = l
  } else if (rightPos.value + delta > props.totalFrame) {
    const l = length.value
    leftPos.value = props.totalFrame - l
    rightPos.value = props.totalFrame
  } else {
    leftPos.value += delta
    rightPos.value += delta
  }
}

const wheelEvent = (event: WheelEvent) => {
  let delta = lodash.toSafeInteger(event.deltaX || event.deltaY || event.deltaZ || 0)
  move(0.05 * delta)
}

const wheelAltEvent = (event: WheelEvent) => {
  let delta = lodash.toSafeInteger(event.deltaX || event.deltaY || event.deltaZ || 0)
  delta *= 0.001

  const timeline = document.querySelector('.timeline-main')
  let percent = 0.5
  if (timeline) {
    const rect = timeline.getBoundingClientRect()
    if (event.pageX < rect.left) percent = 0
    else if (event.pageX > rect.right) percent = 1
    else percent = (event.pageX - rect.left) / (rect.right - rect.left)
  }

  scale(delta, percent)
}

const scale = (delta: number, centerPercent: number) => {
  leftPos.value = Math.max(0, leftPos.value * (1 - delta * centerPercent))
  rightPos.value = Math.min(rightPos.value * (1 + delta * (1 - centerPercent)), props.totalFrame)
  if (rightPos.value - leftPos.value < props.fps) {
    length.value = props.fps
  }
}

const pointerDownEvent = (event: PointerEvent) => {
  if (!props.disabled && pointerId.value === undefined) {
    event.preventDefault()
    pointerId.value = event.pointerId
    pointerPos.value = event.pageX
    lastX.value = event.pageX
  }
}

const pointerMoveEvent = (event: PointerEvent) => {
  if (pointerId.value === event.pointerId) {
    event.preventDefault()
    move((lastX.value - event.pageX) / timelineMainWidth.value * length.value)
    lastX.value = event.pageX
  }
}

const pointerUpEvent = (event: PointerEvent) => {
  if (pointerId.value === event.pointerId) {
    event.preventDefault()
    pointerId.value = undefined
    if (pointerPos.value === event.pageX) {
      emit('change', Math.min(props.totalFrame, Math.max(0, xScale.value.invert(Math.floor(timelinePointerX.value)))))
    }
  }
}

const pointerLocalMoveEvent = (event: PointerEvent) => {
  const el = document.querySelector('.timeline-main')
  if (el) {
    const rect = el.getBoundingClientRect()
    timelinePointerX.value = event.pageX - rect.left
  }
}

watch([leftPos, rightPos], renderTimeline)

watch(() => props.currentFrame, (val) => {
  if (val < leftPos.value || val > rightPos.value) {
    const middle = (rightPos.value - leftPos.value) / 2 + leftPos.value
    move(val - middle)
  }
})

watch(() => props.totalFrame, () => {
  length.value = Math.min(props.totalFrame, props.fps * 5)
})

watch(() => props.fps, () => {
  length.value = Math.min(props.totalFrame, props.fps * 5)
})

onMounted(() => {
  length.value = Math.min(props.totalFrame, props.fps * 5)
  updateTimelineSize()
  window.addEventListener('resize', updateTimelineSize)
  document.addEventListener('pointermove', pointerMoveEvent)
  document.addEventListener('pointerup', pointerUpEvent)
})

onBeforeUnmount(() => {
  document.removeEventListener('pointermove', pointerMoveEvent)
  document.removeEventListener('pointerup', pointerUpEvent)
  window.removeEventListener('resize', updateTimelineSize)
})
</script>



<style scoped>
.timeline-wrapper {
    flex-grow: 1;
    width: 100%;
    min-height: 150px;
    display: flex;
    flex-direction: column;
}

#current-svg,
#current-pointer-svg {
    position: absolute;
    top: 0;
    left: 0;
}

#current-pointer-svg {
    opacity: 0;
    transition: 0.1s all;
}

.timeline-main {
    width: 100%;
    min-height: 150px;
    background-color: #091620;
    flex-grow: 1;
    position: relative;
}

.timeline-main:hover #current-pointer-svg {
    opacity: 0.6;
}

.grabbable {
    cursor: grab;
}

.grabbable:active {
    cursor: grabbing;
}

#subtitle-svg {
    margin: 10px 0 0;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

</style>
<style>
.timeline-axis {
    transform: translate(0, 8px);
}

.timeline-axis-lines {
    transform: translate(0, 9px);
}

.timeline-axis path,
.timeline-axis-lines path {
    display: none;
}

.timeline-axis text {
    fill: rgba(255, 255, 255, 0.4);
    transform: translate(4px, -6px);
    opacity: 1;
}

.timeline-axis-lines text {
    display: none;
}

.timeline-axis line,
.timeline-axis-lines line {
    stroke-linecap: round;
    stroke-width: 1px;
}

.timeline-axis line {
    stroke: rgba(255, 255, 255, 0.24);
}

.timeline-axis-lines line {
    stroke: rgba(255, 255, 255, 0.16);
}

.subtitle-rect {
    fill: rgba(68, 173, 255, 0.06);
    stroke: transparent;
    stroke-linecap: round;
    stroke-width: 2px;
}

.subtitle-rect-active {
    stroke: #25587e;
}

.subtitle-text-object {
    display: flex;
}

.subtitle-text-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
}

.subtitle-text {
    padding: 0 16px;
    margin: auto;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    background-color: transparent;
    cursor: text;
    overflow-wrap: break-word;
    max-width: 100%;
    opacity: 0.25;
}

.subtitle-text-active {
    opacity: 1;
}

.current-line {
    stroke: #18a1b4;
    stroke-width: 1;
}

.current-time-text {
    transform: translate(4px, 34px);
    fill: #18a1b4;
    font-size: 10px;
}

</style>
