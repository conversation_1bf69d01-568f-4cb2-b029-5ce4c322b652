import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
// import { parseSRT } from '../lib/utils';

export const useSubtitleStore = defineStore('subtitle', {
  state: () => ({
    subtitles: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    editingId: null,
    editText: '',
    retryingBatch: null,
    expandedTable: false,
    selectedVoice: 'isVoice1',
    renderVoiceOptions: {
      // Text Subtitle options
      isVoice1: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#000000',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        fontFamily: 'Arial',
        shadowSize: 2,
        subInLine: 10,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },
      isVoice2: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#000000',
        subtitleBackgroundColor: '#FFCC00',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        fontFamily: 'Arial',
        shadowSize: 2,
        subInLine: 10,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },
      isVoice3: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#0066CC',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        fontFamily: 'Arial',
        shadowSize: 2,
        subInLine: 10,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },
      isVoice4: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#6600CC',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        fontFamily: 'Arial',
        shadowSize: 2,
        subInLine: 10,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },
      isVoice5: {
        showSubtitle: true,
        subtitleFontSize: 48,
        subtitleTextColor: '#ffffff',
        subtitleBackgroundColor: '#0066CC',
        subtitleBorderColor: '#000000',
        subtitleBold: true,
        fontFamily: 'Arial',
        shadowSize: 2,
        subInLine: 10,
        currentPlayingSubtitleId: null,
        activeSubtitleId: null,
        assOptions: {
          posX: 0,
          posY: 33,
          rotation: 0,
          align: 5,
          fontSize: 48,
        },
      },



    },

    renderOptions: {
      // Text options
      showText: true,
      fontSize: 24,
      textColor: '#fff700',
      textValue: '@Hello World',
      textOpacity: 50,
      textDirection: 'random',
      textFontFamily: 'Arial',

      // Text Subtitle options
      showSubtitle: true,
      subtitleFontSize: 48,
      subtitleTextColor: '#ffffff',
      subtitleBackgroundColor: '#000000',
      subtitleBorderColor: '#000000',
      subtitleBold: true,
      shadowSize: 2,
      fontFamily: 'Arial',
      currentPlayingSubtitleId: null,
      activeSubtitleId: null,
      subInLine: 10, // subtitle in line number
      assOptions: {
        posX: 0,
        posY: 33,
        rotation: 0,
        align: 5,
        fontSize: 48,
      },

      // Logo options
      showLogo: false,
      logoPosition: 'bottom-right',
      logoSize: 'medium',
      logoFile: null,
      logoOptions: {
        posX: 90,
        posY: 10,
        scale: 100,
        rotation: 0,
        opacity: 100
      },

      // Image options
      showImage: false,
      imageFile: null,
      imageOptions: {
        posX: 50,
        posY: 50,
        scale: 100,
        rotation: 0,
        opacity: 100,
        autoFit: false
      },

      // Fixed Text options
      showFixedText: false,
      fixedTextOptions: {
        text: 'Sample Text',
        fontSize: 24,
        color: '#ffffff',
        backgroundColor: 'transparent',
        borderColor: '#000000',
        fontFamily: 'Arial',
        bold: false,
        posX: 50,
        posY: 10,
        rotation: 0,
        opacity: 100,
        align: 'center'
      },

      // Multiple Overlays (New Structure)
      overlays: {
        logos: [],
        images: [],
        texts: []
      },

      // Audio options
      addBackgroundMusic: false,
      addLutColorFile: false,
      backgroundMusicVolume: 30,
      originalAudioVolume: 80,
      downOriginalAudioVolume: 0.1,
      holdOriginalAudio: false,
      holdMusicOnly: false,
      removeMusicOnly: false,
      holdLastAudio: false,

      // 
      lutFile: '',
      musicFile: '',

      // Output options
      videoQuality: 'custom', // '1080p/16:9',
      frameRate: '30',
      scaleFactor: 1,
      flipVideo: false,
      textSpeed: 50,
      volume: 15,

      // Blur Areas for video effects
      blurAreas: []
    },
  }),
  persist: {
    storage: localStorage,
    pick: ['renderOptions','renderVoiceOptions'],
  },
  actions: {
    setSubtitles(subtitles) {
      this.subtitles = subtitles;
      this.totalPages = Math.ceil(subtitles.length / this.pageSize);
    },
    setIsLoading(value) {
      this.isLoading = value;
    },
    setError(error) {
      this.error = error;
    },
    setCurrentPage(page) {
      this.currentPage = page;
    },
    setEditingId(id) {
      this.editingId = id;
    },
    setEditText(text) {
      this.editText = text;
    },
    setRetryingBatch(batch) {
      this.retryingBatch = batch;
    },
    setExpandedTable(value) {
      this.expandedTable = value;
    },

    // Migration function to convert old overlay format to new format
    migrateOverlaysToNewFormat() {
      // Only migrate if new format is empty and old format has data
      if (this.renderOptions.overlays.logos.length === 0 &&
          this.renderOptions.overlays.images.length === 0 &&
          this.renderOptions.overlays.texts.length === 0) {

        // Migrate logo
        if (this.renderOptions.showLogo && this.renderOptions.logoFile) {
          this.renderOptions.overlays.logos.push({
            id: 'migrated_logo_' + Date.now(),
            enabled: this.renderOptions.showLogo,
            file: this.renderOptions.logoFile,
            options: { ...this.renderOptions.logoOptions }
          });
        }

        // Migrate image
        if (this.renderOptions.showImage && this.renderOptions.imageFile) {
          this.renderOptions.overlays.images.push({
            id: 'migrated_image_' + Date.now(),
            enabled: this.renderOptions.showImage,
            file: this.renderOptions.imageFile,
            options: { ...this.renderOptions.imageOptions }
          });
        }

        // Migrate fixed text
        if (this.renderOptions.showFixedText && this.renderOptions.fixedTextOptions.text) {
          this.renderOptions.overlays.texts.push({
            id: 'migrated_text_' + Date.now(),
            enabled: this.renderOptions.showFixedText,
            options: { ...this.renderOptions.fixedTextOptions }
          });
        }

        console.log('✅ Migrated overlays to new format:', this.renderOptions.overlays);
      }
    },

    // Initialize overlays structure if not exists
    initializeOverlays() {
      if (!this.renderOptions.overlays) {
        this.renderOptions.overlays = {
          logos: [],
          images: [],
          texts: []
        };
        // Auto-migrate existing data
        this.migrateOverlaysToNewFormat();
      }
    },

    // Update blur areas
    updateBlurAreas(blurAreas) {
      this.renderOptions.blurAreas = JSON.parse(JSON.stringify(blurAreas || []));
    },

    // Get blur areas
    getBlurAreas() {
      return this.renderOptions.blurAreas || [];
    },
  },
});
